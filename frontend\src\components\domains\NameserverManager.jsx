"use client";
import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ody,
  <PERSON><PERSON>,
  <PERSON>put,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON>Header,
  TabsBody,
  Tab,
  TabPanel,
} from "@material-tailwind/react";
import {
  Server,
  Plus,
  Trash2,
  Save,
  AlertCircle,
  CheckCircle,
  Globe,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";
import ChildNameServerManager from "./ChildNameServerManager";

export default function NameserverManager({ domain, onUpdate }) {
  const [nameservers, setNameservers] = useState(
    domain?.nameservers || ["", ""]
  );
  const [isUpdating, setIsUpdating] = useState(false);
  const [errors, setErrors] = useState({});
  const [activeTab, setActiveTab] = useState("nameservers");

  const validateNameserver = (ns) => {
    if (!ns.trim()) return "Nameserver cannot be empty";

    // Basic domain validation
    const domainRegex =
      /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
    if (!domainRegex.test(ns.trim())) {
      return "Invalid nameserver format";
    }

    return null;
  };

  const validateAllNameservers = () => {
    const newErrors = {};
    const validNameservers = nameservers.filter((ns) => ns.trim());

    if (validNameservers.length < 2) {
      newErrors.general = "At least 2 nameservers are required";
    }

    if (validNameservers.length > 13) {
      newErrors.general = "Maximum 13 nameservers allowed";
    }

    nameservers.forEach((ns, index) => {
      if (ns.trim()) {
        const error = validateNameserver(ns);
        if (error) {
          newErrors[index] = error;
        }
      }
    });

    // Check for duplicates
    const trimmedNs = validNameservers.map((ns) => ns.trim().toLowerCase());
    const duplicates = trimmedNs.filter(
      (ns, index) => trimmedNs.indexOf(ns) !== index
    );
    if (duplicates.length > 0) {
      newErrors.general = "Duplicate nameservers are not allowed";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNameserverChange = (index, value) => {
    const newNameservers = [...nameservers];
    newNameservers[index] = value;
    setNameservers(newNameservers);

    // Clear error for this field
    if (errors[index]) {
      const newErrors = { ...errors };
      delete newErrors[index];
      setErrors(newErrors);
    }
  };

  const addNameserver = () => {
    if (nameservers.length < 13) {
      setNameservers([...nameservers, ""]);
    }
  };

  const removeNameserver = (index) => {
    if (nameservers.length > 2) {
      const newNameservers = nameservers.filter((_, i) => i !== index);
      setNameservers(newNameservers);

      // Clear error for this field
      if (errors[index]) {
        const newErrors = { ...errors };
        delete newErrors[index];
        setErrors(newErrors);
      }
    }
  };

  const handleSave = async () => {
    if (!validateAllNameservers()) {
      return;
    }

    try {
      setIsUpdating(true);

      const validNameservers = nameservers
        .filter((ns) => ns.trim())
        .map((ns) => ns.trim());

      console.log("🔧 Domain data:", domain);
      console.log("🔧 Domain name:", domain.name);
      console.log("🔧 Domain name type:", typeof domain.name);
      console.log("🔧 Getting domain order ID for:", domain.name);

      if (!domain.name) {
        throw new Error("Domain name is missing from domain data");
      }

      // First, get the real domain registration order ID from the API
      const orderIdResponse = await domainMngService.getDomainOrderId(
        domain.name
      );

      if (!orderIdResponse.data.success) {
        throw new Error("Failed to get domain order ID");
      }

      const realOrderId = orderIdResponse.data.orderId;
      console.log("✅ Got real domain order ID:", realOrderId);

      // Check domain status before attempting to modify nameservers
      console.log("🔍 Checking domain status before modification...");
      try {
        const domainDetailsResponse =
          await domainMngService.getDomainDetailsByName(domain.name, "All");
        console.log("📋 Domain details:", domainDetailsResponse.data);

        if (
          domainDetailsResponse.data.success &&
          domainDetailsResponse.data.domain
        ) {
          const domainInfo = domainDetailsResponse.data.domain;
          console.log("📊 Current status:", domainInfo.currentstatus);
          console.log("📊 Order status:", domainInfo.orderstatus);
          console.log("📊 Domain status:", domainInfo.domainstatus);
          console.log("📊 Order ID:", domainInfo.orderid);

          // Check if domain is in a state that allows nameserver modification
          if (
            domainInfo.currentstatus &&
            domainInfo.currentstatus !== "Active"
          ) {
            throw new Error(
              `Domain is not active. Current status: ${domainInfo.currentstatus}. Please wait for the domain to become active before modifying nameservers.`
            );
          }

          // Check if there are any order status locks that might prevent modification
          if (
            domainInfo.orderstatus &&
            Array.isArray(domainInfo.orderstatus) &&
            domainInfo.orderstatus.length > 0
          ) {
            console.log(
              "⚠️ Domain has order status locks:",
              domainInfo.orderstatus
            );
            toast.warning(
              `Domain has status locks: ${domainInfo.orderstatus.join(
                ", "
              )}. Nameserver modification may fail.`
            );
          }
        }
      } catch (statusError) {
        console.warn("⚠️ Could not check domain status:", statusError.message);
        // Continue with nameserver modification even if status check fails
      }

      const updateData = {
        orderId: realOrderId, // Use the real domain registration order ID
        nameservers: validNameservers,
      };

      console.log("🔧 Updating nameservers with data:", updateData);

      const response = await domainMngService.modifyNameservers(updateData);

      if (response.data.success) {
        toast.success("Nameservers updated successfully");

        // Call onUpdate callback if provided
        if (onUpdate) {
          onUpdate({
            ...domain,
            nameservers: validNameservers,
          });
        }
      } else {
        throw new Error(response.data.error || "Failed to update nameservers");
      }
    } catch (error) {
      console.error("Error updating nameservers:", error);

      // Provide specific error messages based on the error type
      let errorMessage = "Failed to update nameservers";

      if (error.response?.data?.details?.message) {
        const apiError = error.response.data.details.message;
        if (apiError.includes("Invalid Order status")) {
          errorMessage =
            "Cannot modify nameservers: Domain order is not in a valid status. The domain may be inactive, suspended, or locked.";
        } else {
          errorMessage = `API Error: ${apiError}`;
        }
      } else if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    } finally {
      setIsUpdating(false);
    }
  };

  const resetToDefault = async () => {
    try {
      console.log("🔄 Loading customer default nameservers...");

      // Get customer default nameservers from API
      const response = await domainMngService.getCustomerDefaultNameservers();

      console.log("🔍 Full API response:", response.data);

      if (response.data.success && response.data.nameservers) {
        let defaultNameservers = response.data.nameservers;

        // Handle different response formats
        if (Array.isArray(defaultNameservers)) {
          console.log(
            "✅ Loaded default nameservers from API:",
            defaultNameservers
          );
        } else {
          console.warn(
            "⚠️ Nameservers not in expected array format:",
            defaultNameservers
          );
          throw new Error("Invalid nameserver format from API");
        }

        setNameservers(defaultNameservers);
        setErrors({});

        toast.success("Default nameservers loaded successfully");
      } else {
        console.warn(
          "⚠️ API response missing success or nameservers:",
          response.data
        );
        throw new Error("Failed to load default nameservers");
      }
    } catch (error) {
      console.error("❌ Error loading default nameservers:", error);

      // Fallback to correct order from dashboard
      const fallbackNameservers = [
        "moha1280036.earth.orderbox-dns.com",
        "moha1280036.mars.orderbox-dns.com",
        "moha1280036.mercury.orderbox-dns.com",
        "moha1280036.venus.orderbox-dns.com",
      ];

      setNameservers(fallbackNameservers);
      setErrors({});

      toast.warning("Using fallback default nameservers");
    }
  };

  return (
    <Card>
      <CardBody>
        <div className="flex items-center gap-2 mb-4">
          <Server className="h-5 w-5 text-blue-600" />
          <Typography variant="h5" className="text-gray-800">
            DNS Management
          </Typography>
        </div>

        <Typography className="text-gray-600 mb-6">
          Manage nameservers and child name servers for {domain?.name}. Changes
          may take up to 24-48 hours to propagate globally.
        </Typography>

        {domain?.orderId && (
          <Typography className="text-xs text-gray-500 mb-4">
            Order ID: {domain.orderId}
          </Typography>
        )}

        <Tabs value={activeTab} onChange={setActiveTab}>
          <TabsHeader>
            <Tab value="nameservers">
              <div className="flex items-center gap-2">
                <Server className="h-4 w-4" />
                Nameservers
              </div>
            </Tab>
            <Tab value="child-nameservers">
              <div className="flex items-center gap-2">
                <Globe className="h-4 w-4" />
                Child Name Servers
              </div>
            </Tab>
          </TabsHeader>
          <TabsBody>
            <TabPanel value="nameservers">
              {/* Nameserver Management Content */}

              {errors.general && (
                <Alert color="red" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  {errors.general}
                </Alert>
              )}

              <div className="space-y-3 mb-6">
                {nameservers.map((ns, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <div className="flex-1">
                      <Input
                        label={`Nameserver ${index + 1}`}
                        value={ns}
                        onChange={(e) =>
                          handleNameserverChange(index, e.target.value)
                        }
                        error={!!errors[index]}
                        className={errors[index] ? "border-red-500" : ""}
                      />
                      {errors[index] && (
                        <Typography className="text-red-500 text-xs mt-1">
                          {errors[index]}
                        </Typography>
                      )}
                    </div>

                    {nameservers.length > 2 && (
                      <Button
                        variant="text"
                        color="red"
                        size="sm"
                        onClick={() => removeNameserver(index)}
                        className="p-2"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <div className="flex flex-wrap gap-2 mb-6">
                {nameservers.length < 13 && (
                  <Button
                    variant="outlined"
                    size="sm"
                    onClick={addNameserver}
                    className="flex items-center gap-2"
                  >
                    <Plus className="h-4 w-4" />
                    Add Nameserver
                  </Button>
                )}

                <Button
                  variant="outlined"
                  size="sm"
                  onClick={resetToDefault}
                  className="flex items-center gap-2"
                >
                  <Server className="h-4 w-4" />
                  Use Default
                </Button>
              </div>

              <Alert color="blue" className="mb-4">
                <AlertCircle className="h-4 w-4" />
                <div>
                  <Typography className="font-semibold">
                    Important Notes:
                  </Typography>
                  <ul className="text-sm mt-1 space-y-1">
                    <li>• At least 2 nameservers are required</li>
                    <li>• Maximum 13 nameservers allowed</li>
                    <li>• Changes may take 24-48 hours to propagate</li>
                    <li>• Incorrect nameservers may cause website downtime</li>
                  </ul>
                </div>
              </Alert>

              <div className="flex gap-2">
                <Button
                  className="bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
                  onClick={handleSave}
                  disabled={isUpdating}
                >
                  {isUpdating ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Updating...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      Save Changes
                    </>
                  )}
                </Button>
              </div>
            </TabPanel>
            <TabPanel value="child-nameservers">
              <ChildNameServerManager domain={domain} onUpdate={onUpdate} />
            </TabPanel>
          </TabsBody>
        </Tabs>
      </CardBody>
    </Card>
  );
}
