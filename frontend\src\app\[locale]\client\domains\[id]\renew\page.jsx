"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import {
  Typo<PERSON>,
  Card,
  CardBody,
  Button,
  Select,
  Option,
  Alert,
} from "@material-tailwind/react";
import {
  ArrowLeft,
  RefreshCw,
  Calendar,
  CreditCard,
  AlertCircle,
} from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import { toast } from "react-toastify";

export default function DomainRenewalPage({ params }) {
  const { id } = params;
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domain, setDomain] = useState(null);
  const [loading, setLoading] = useState(true);
  const [renewalYears, setRenewalYears] = useState("1");
  const [renewalCost, setRenewalCost] = useState(0);
  const [isRenewing, setIsRenewing] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const getDomainDetails = async () => {
      try {
        setLoading(true);
        
        // Get the user's domains to find the domain by ID
        const domainsRes = await domainMngService.getUserDomains();
        const userDomains = domainsRes.data?.domains || [];
        
        // Find the domain with the matching ID
        const userDomain = userDomains.find(d => d.id === id);
        
        if (!userDomain) {
          console.error("Domain not found with ID:", id);
          setLoading(false);
          return;
        }

        setDomain(userDomain);
        
        // Calculate renewal cost based on domain price and period
        const baseCost = userDomain.price || 100; // Fallback price
        setRenewalCost(baseCost * parseInt(renewalYears));
        
        setLoading(false);
      } catch (error) {
        console.error("Error getting domain details", error);
        setLoading(false);
      }
    };
    getDomainDetails();
  }, [id, renewalYears]);

  const handleRenewal = async () => {
    if (!domain) return;

    try {
      setIsRenewing(true);
      
      const renewalData = {
        domainName: domain.name,
        years: parseInt(renewalYears),
        orderId: domain.orderId, // Include order ID if available
      };

      console.log("Renewing domain with data:", renewalData);

      const response = await domainMngService.renewDomain(renewalData);
      
      if (response.data.success) {
        toast.success(
          t("domain.renewal_success", { 
            defaultValue: `Domain ${domain.name} renewed successfully for ${renewalYears} year(s)` 
          })
        );
        
        // Redirect back to domain details
        router.push(`/client/domains/${id}`);
      } else {
        throw new Error(response.data.error || "Renewal failed");
      }
    } catch (error) {
      console.error("Error renewing domain:", error);
      toast.error(
        error.response?.data?.error || 
        error.message || 
        t("domain.renewal_error", { defaultValue: "Failed to renew domain" })
      );
    } finally {
      setIsRenewing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!domain) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-8">
        <Typography variant="h4" className="text-gray-800 font-bold mb-2">
          {t("domain_not_found", { defaultValue: "Domain Not Found" })}
        </Typography>
        <Button
          className="mt-4 bg-blue-600 hover:bg-blue-700 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-4xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push(`/client/domains/${id}`)}
        >
          <ArrowLeft className="h-4 w-4" />
          {t("back_to_domain", { defaultValue: "Back to Domain" })}
        </Button>

        <div className="mb-6">
          <Typography variant="h3" className="text-gray-800 font-bold mb-2">
            {t("renew_domain", { defaultValue: "Renew Domain" })}
          </Typography>
          <Typography className="text-gray-600">
            {t("renew_domain_description", { 
              defaultValue: "Extend your domain registration to keep it active" 
            })}
          </Typography>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Domain Information */}
          <div className="lg:col-span-2">
            <Card className="mb-6">
              <CardBody>
                <Typography variant="h5" className="mb-4 text-gray-800">
                  {t("domain_information", { defaultValue: "Domain Information" })}
                </Typography>
                
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">{t("domain_name", { defaultValue: "Domain Name" })}</span>
                    <span className="font-semibold">{domain.name}</span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">{t("current_expiry", { defaultValue: "Current Expiry" })}</span>
                    <span className="font-semibold">
                      {new Date(domain.expiryDate).toLocaleDateString()}
                    </span>
                  </div>
                  
                  <div className="flex justify-between items-center py-2 border-b">
                    <span className="text-gray-600">{t("status", { defaultValue: "Status" })}</span>
                    <span className={`px-2 py-1 rounded-full text-xs font-semibold ${
                      domain.status === "active" 
                        ? "bg-green-100 text-green-800" 
                        : "bg-yellow-100 text-yellow-800"
                    }`}>
                      {domain.status}
                    </span>
                  </div>
                </div>
              </CardBody>
            </Card>

            {/* Renewal Options */}
            <Card>
              <CardBody>
                <Typography variant="h5" className="mb-4 text-gray-800">
                  {t("renewal_options", { defaultValue: "Renewal Options" })}
                </Typography>
                
                <div className="space-y-4">
                  <div>
                    <Typography className="mb-2 text-gray-700">
                      {t("renewal_period", { defaultValue: "Renewal Period" })}
                    </Typography>
                    <Select
                      value={renewalYears}
                      onChange={(value) => setRenewalYears(value)}
                      className="w-full"
                    >
                      <Option value="1">1 Year</Option>
                      <Option value="2">2 Years</Option>
                      <Option value="3">3 Years</Option>
                      <Option value="5">5 Years</Option>
                      <Option value="10">10 Years</Option>
                    </Select>
                  </div>

                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-2">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <Typography className="font-semibold text-blue-800">
                        {t("new_expiry_date", { defaultValue: "New Expiry Date" })}
                      </Typography>
                    </div>
                    <Typography className="text-blue-700">
                      {new Date(
                        new Date(domain.expiryDate).setFullYear(
                          new Date(domain.expiryDate).getFullYear() + parseInt(renewalYears)
                        )
                      ).toLocaleDateString()}
                    </Typography>
                  </div>
                </div>
              </CardBody>
            </Card>
          </div>

          {/* Renewal Summary */}
          <div>
            <Card className="sticky top-6">
              <CardBody>
                <Typography variant="h5" className="mb-4 text-gray-800">
                  {t("renewal_summary", { defaultValue: "Renewal Summary" })}
                </Typography>
                
                <div className="space-y-3 mb-6">
                  <div className="flex justify-between">
                    <span className="text-gray-600">{domain.name}</span>
                    <span>{renewalYears} year(s)</span>
                  </div>
                  
                  <div className="flex justify-between">
                    <span className="text-gray-600">{t("price_per_year", { defaultValue: "Price per year" })}</span>
                    <span>{domain.price || 100} MAD</span>
                  </div>
                  
                  <hr />
                  
                  <div className="flex justify-between font-bold text-lg">
                    <span>{t("total", { defaultValue: "Total" })}</span>
                    <span>{renewalCost} MAD</span>
                  </div>
                </div>

                <Alert className="mb-4" color="amber">
                  <AlertCircle className="h-4 w-4" />
                  <Typography className="text-sm">
                    {t("renewal_notice", { 
                      defaultValue: "Domain renewal will be processed immediately and cannot be refunded." 
                    })}
                  </Typography>
                </Alert>

                <Button
                  className="w-full bg-blue-600 hover:bg-blue-700 flex items-center justify-center gap-2"
                  onClick={handleRenewal}
                  disabled={isRenewing}
                >
                  {isRenewing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      {t("processing", { defaultValue: "Processing..." })}
                    </>
                  ) : (
                    <>
                      <CreditCard className="h-4 w-4" />
                      {t("renew_now", { defaultValue: "Renew Now" })}
                    </>
                  )}
                </Button>
              </CardBody>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
}
