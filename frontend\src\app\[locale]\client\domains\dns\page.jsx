"use client";
import { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { useRouter } from "next/navigation";
import { <PERSON>po<PERSON>, <PERSON>, CardBody, Button } from "@material-tailwind/react";
import { ArrowLeft, Server } from "lucide-react";
import domainMngService from "@/app/services/domainMngService";
import NameserverManager from "@/components/domains/NameserverManager";

export default function DnsMangementPage() {
  const t = useTranslations("client");
  const dt = useTranslations("client.domainWrapper");
  const [domains, setDomains] = useState([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();

  // Function to update a specific domain in the list
  const updateDomain = (updatedDomain) => {
    setDomains((prevDomains) =>
      prevDomains.map((domain) =>
        domain.id === updatedDomain.id
          ? { ...domain, ...updatedDomain }
          : domain
      )
    );
  };

  useEffect(() => {
    const getUserDomains = async () => {
      try {
        setLoading(true);
        // Get real domain data from the API
        const res = await domainMngService.getUserDomains();
        console.log("Domain data for DNS management:", res.data);

        if (res.data && res.data.domains) {
          // Filter only active domains for DNS management
          const activeDomains = res.data.domains.filter(
            (domain) => domain.status === "active"
          );
          console.log("🔧 Active domains for DNS management:", activeDomains);
          setDomains(activeDomains);
        } else {
          setDomains([]);
        }
        setLoading(false);
      } catch (error) {
        console.error("Error getting domains for DNS management", error);
        setLoading(false);
        // If there's an error, set domains to empty array
        setDomains([]);
      }
    };
    getUserDomains();
  }, []);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-pulse flex flex-col items-center">
          <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <Server className="h-6 w-6 text-blue-600" />
          </div>
          <Typography variant="h6" className="text-gray-600">
            {t("loading")}...
          </Typography>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-7xl mx-auto">
        <Button
          variant="text"
          className="mb-6 text-blue-600 flex items-center gap-2"
          onClick={() => router.push("/client/domains")}
        >
          <ArrowLeft className="h-4 w-4" />
          {dt("back_to_domains")}
        </Button>

        <div className="mb-8">
          <Typography variant="h1" className="text-3xl font-bold text-gray-800">
            {dt("dns_settings")}
          </Typography>
          <Typography className="text-gray-600 mt-1">
            {t("manage_dns_records_description", {
              defaultValue:
                "Configure DNS records, nameservers, and more for your domains.",
            })}
          </Typography>
        </div>

        <div className="space-y-6">
          {domains.map((domain) => (
            <Card
              key={domain.id}
              className="bg-white rounded-xl shadow-sm border border-gray-200"
            >
              <CardBody className="p-6">
                <div className="flex justify-between items-center mb-6">
                  <div className="flex items-center">
                    <div className="h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <Server className="h-5 w-5 text-blue-600" />
                    </div>
                    <div className="ml-3">
                      <Typography className="font-medium text-gray-900">
                        {domain.name}
                      </Typography>
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize ${
                          domain.status === "active"
                            ? "bg-green-100 text-green-800"
                            : domain.status === "pending"
                            ? "bg-yellow-100 text-yellow-800"
                            : "bg-gray-100 text-gray-800"
                        }`}
                      >
                        {dt(domain.status)}
                      </span>
                    </div>
                  </div>
                  <Button
                    className="bg-blue-600 hover:bg-blue-700"
                    onClick={() =>
                      router.push(`/client/domains/${domain.id}/dns`)
                    }
                  >
                    {t("manage_records", { defaultValue: "Manage Records" })}
                  </Button>
                </div>

                {/* Nameserver Management Section */}
                <div className="mb-6">
                  <NameserverManager domain={domain} onUpdate={updateDomain} />
                </div>

                {/* DNS Records Section */}
                <div className="border-t pt-6">
                  <Typography variant="h6" className="mb-4 text-gray-800">
                    DNS Records
                  </Typography>

                  {domain.dnsRecords && domain.dnsRecords.length > 0 ? (
                    <div className="overflow-x-auto">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-50 border-b border-gray-200">
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                              {t("type", { defaultValue: "Type" })}
                            </th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                              {t("name", { defaultValue: "Name" })}
                            </th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                              {t("content", { defaultValue: "Content" })}
                            </th>
                            <th className="px-4 py-3 text-left text-sm font-medium text-gray-500">
                              {t("ttl", { defaultValue: "TTL" })}
                            </th>
                          </tr>
                        </thead>
                        <tbody className="divide-y divide-gray-200">
                          {domain.dnsRecords.map((record) => (
                            <tr key={record.id} className="hover:bg-gray-50">
                              <td className="px-4 py-3 text-sm font-medium text-gray-900">
                                {record.type}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500">
                                {record.name}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500">
                                {record.content}
                              </td>
                              <td className="px-4 py-3 text-sm text-gray-500">
                                {record.ttl}
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  ) : (
                    <div className="text-center py-6 bg-gray-50 rounded-lg">
                      <Typography className="text-gray-500">
                        {t("no_dns_records", {
                          defaultValue: "No DNS records found",
                        })}
                      </Typography>
                    </div>
                  )}
                </div>
              </CardBody>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
