"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/dedicated/page",{

/***/ "(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css":
/*!***************************************************************!*\
  !*** ./node_modules/react-loading-skeleton/dist/skeleton.css ***!
  \***************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"7b3a190f6683\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9yZWFjdC1sb2FkaW5nLXNrZWxldG9uL2Rpc3Qvc2tlbGV0b24uY3NzIiwibWFwcGluZ3MiOiI7QUFBQSwrREFBZSxjQUFjO0FBQzdCLElBQUksSUFBVSxJQUFJLGlCQUFpQiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvcmVhY3QtbG9hZGluZy1za2VsZXRvbi9kaXN0L3NrZWxldG9uLmNzcz9lOWEwIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiN2IzYTE5MGY2NjgzXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/dedicated/page.jsx":
/*!*******************************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/dedicated/page.jsx ***!
  \*******************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DedicatedHostingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var react_loading_skeleton_dist_skeleton_css__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-loading-skeleton/dist/skeleton.css */ \"(app-pages-browser)/./node_modules/react-loading-skeleton/dist/skeleton.css\");\n/* harmony import */ var _components_hosting_pricingPlanGrid__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/hosting/pricingPlanGrid */ \"(app-pages-browser)/./src/components/hosting/pricingPlanGrid.jsx\");\n/* harmony import */ var _app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/app/helpers/helpers */ \"(app-pages-browser)/./src/app/helpers/helpers.js\");\n/* harmony import */ var _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/services/packageService */ \"(app-pages-browser)/./src/app/services/packageService.js\");\n/* harmony import */ var lottie_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! lottie-react */ \"(app-pages-browser)/./node_modules/lottie-react/build/index.umd.js\");\n/* harmony import */ var lottie_react__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(lottie_react__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var src_assets_dedicated_hosting_json__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! src/assets/dedicated-hosting.json */ \"(app-pages-browser)/./src/assets/dedicated-hosting.json\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircleIcon,HardDriveIcon,RocketIcon,ServerIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,CheckCircleIcon,HardDriveIcon,RocketIcon,ServerIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction DedicatedHostingPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations)(\"hosting\");\n    const [billingPeriod, setBillingPeriod] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"yearly\");\n    const [dedicatedHostingPacks, setDedicatedHostingPacks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getPackagesData = async ()=>{\n            try {\n                const response = await _app_services_packageService__WEBPACK_IMPORTED_MODULE_6__[\"default\"].getPackages(\"Dedicated hosting\");\n                setDedicatedHostingPacks(response.data);\n            } catch (error) {\n                console.error(\"error fetching promotions: \", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        getPackagesData();\n    }, []);\n    const dedicatedPlans = (0,_app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__.getFormattedPlans)(dedicatedHostingPacks, billingPeriod);\n    const maxDiscount = (0,_app_helpers_helpers__WEBPACK_IMPORTED_MODULE_5__.getMaxDiscount)(dedicatedHostingPacks);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50 pt-4 pb-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"h1\",\n                                        color: \"blue-gray\",\n                                        className: \"text-4xl lg:text-5xl font-inter font-bold mb-6 tracking-tight text-transparent bg-clip-text bg-gradient-to-r from-[#0B2D6A] to-indigo-500\",\n                                        children: t(\"dedicated_server_hosting\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 50,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                        variant: \"lead\",\n                                        color: \"gray\",\n                                        className: \"text-lg mb-8 font-light\",\n                                        children: t(\"enterprise_grade_servers\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 57,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        color: \"blue\",\n                                        size: \"lg\",\n                                        className: \"md:hidden rounded-full flex gap-2 justify-center items-center w-full sm:w-auto\",\n                                        onClick: ()=>{\n                                            var _document_getElementById;\n                                            return (_document_getElementById = document.getElementById(\"pricing\")) === null || _document_getElementById === void 0 ? void 0 : _document_getElementById.scrollIntoView({\n                                                behavior: \"smooth\"\n                                            });\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"w-6 h-6 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                                lineNumber: 70,\n                                                columnNumber: 17\n                                            }, this),\n                                            t(\"explore_plans\")\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 64,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center lg:justify-end\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((lottie_react__WEBPACK_IMPORTED_MODULE_7___default()), {\n                                    animationData: src_assets_dedicated_hosting_json__WEBPACK_IMPORTED_MODULE_8__,\n                                    loop: true,\n                                    className: \"w-96 max-w-sm lg:max-w-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    id: \"pricing\",\n                    className: \"mt-[-20px] mb-20\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_hosting_pricingPlanGrid__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        loading: loading,\n                        plans: dedicatedPlans,\n                        billingPeriod: billingPeriod,\n                        t: t,\n                        setBillingPeriod: setBillingPeriod,\n                        getMaxDiscount: ()=>maxDiscount\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                        lineNumber: 86,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                    className: \"py-16 bg-gray-50\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-12\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    variant: \"h2\",\n                                    color: \"blue-gray\",\n                                    className: \"text-3xl md:text-4xl lg:text-5xl font-bold tracking-tight mb-4\",\n                                    children: t(\"features_title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    variant: \"lead\",\n                                    color: \"gray\",\n                                    className: \"text-lg md:text-xl font-light text-gray-600 max-w-2xl mx-auto\",\n                                    children: t(\"features_description\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 px-4 sm:px-6 lg:px-8\",\n                            children: [\n                                {\n                                    title: t(\"dedicatedFeatures.0.title\"),\n                                    description: t(\"dedicatedFeatures.0.description\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 23\n                                    }, this)\n                                },\n                                {\n                                    title: t(\"dedicatedFeatures.1.title\"),\n                                    description: t(\"dedicatedFeatures.1.description\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 127,\n                                        columnNumber: 23\n                                    }, this)\n                                },\n                                {\n                                    title: t(\"dedicatedFeatures.2.title\"),\n                                    description: t(\"dedicatedFeatures.2.description\"),\n                                    icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_CheckCircleIcon_HardDriveIcon_RocketIcon_ServerIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-8 w-8 text-green-700\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                        lineNumber: 132,\n                                        columnNumber: 23\n                                    }, this)\n                                }\n                            ].map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n                                    className: \"bg-white hover:shadow-2xl transition-shadow duration-300 rounded-2xl border border-gray-100 p-6 flex flex-col items-center text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mb-6\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                            lineNumber: 139,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n                                            className: \"p-0\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                    variant: \"h5\",\n                                                    color: \"blue-gray\",\n                                                    className: \"text-xl md:text-2xl font-semibold mb-3\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                                    lineNumber: 143,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                    color: \"gray\",\n                                                    className: \"text-sm md:text-base font-normal text-gray-600\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                                    lineNumber: 150,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                                    lineNumber: 135,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                            lineNumber: 117,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n            lineNumber: 45,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\dedicated\\\\page.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(DedicatedHostingPage, \"j/xASROy8krvzTQtbRJvI4uzyK0=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_9__.useTranslations\n    ];\n});\n_c = DedicatedHostingPage;\nvar _c;\n$RefreshReg$(_c, \"DedicatedHostingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/dedicated/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/config/constant.js":
/*!************************************!*\
  !*** ./src/app/config/constant.js ***!
  \************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   API_GOOGLE_MAP_KEY: function() { return /* binding */ API_GOOGLE_MAP_KEY; },\n/* harmony export */   BACKEND_URL: function() { return /* binding */ BACKEND_URL; },\n/* harmony export */   COOKIE_DOMAIN: function() { return /* binding */ COOKIE_DOMAIN; },\n/* harmony export */   FRONTEND_URL: function() { return /* binding */ FRONTEND_URL; },\n/* harmony export */   REACT_APP_GG_APP_ID: function() { return /* binding */ REACT_APP_GG_APP_ID; }\n/* harmony export */ });\nconst backendDev = \"http://localhost:5002\";\nconst frontendDev = \"http://localhost:3001\";\nconst backend = \"https://api.ztechengineering.com\";\nconst frontend = \"https://ztechengineering.com\";\nconst isProd = false;\nconst BACKEND_URL = isProd ? backend : backendDev;\nconst FRONTEND_URL = isProd ? frontend : frontendDev;\nconst COOKIE_DOMAIN = isProd ? \".ztechengineering.com\" : \"localhost\";\nconst REACT_APP_GG_APP_ID = \"480987384459-h3cie2vcshp09vphuvnshccqprco3fbo.apps.googleusercontent.com\";\nconst API_GOOGLE_MAP_KEY = \"AIzaSyA5pGy3UEKwbgjUY-72RmoR7npEq1b_uf0\";\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvY29uZmlnL2NvbnN0YW50LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQUEsTUFBTUEsYUFBYTtBQUNuQixNQUFNQyxjQUFjO0FBRXBCLE1BQU1DLFVBQVU7QUFDaEIsTUFBTUMsV0FBVztBQUVqQixNQUFNQyxTQUFTO0FBQ1IsTUFBTUMsY0FBY0QsU0FBU0YsVUFBVUYsV0FBVztBQUNsRCxNQUFNTSxlQUFlRixTQUFTRCxXQUFXRixZQUFZO0FBQ3JELE1BQU1NLGdCQUFnQkgsU0FBUywwQkFBMEIsWUFBWTtBQUVyRSxNQUFNSSxzQkFDWCwyRUFBMkU7QUFDdEUsTUFBTUMscUJBQXFCLDBDQUEwQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2NvbmZpZy9jb25zdGFudC5qcz9iMTZjIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGJhY2tlbmREZXYgPSBcImh0dHA6Ly9sb2NhbGhvc3Q6NTAwMlwiO1xyXG5jb25zdCBmcm9udGVuZERldiA9IFwiaHR0cDovL2xvY2FsaG9zdDozMDAxXCI7XHJcblxyXG5jb25zdCBiYWNrZW5kID0gXCJodHRwczovL2FwaS56dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5jb25zdCBmcm9udGVuZCA9IFwiaHR0cHM6Ly96dGVjaGVuZ2luZWVyaW5nLmNvbVwiO1xyXG5cclxuY29uc3QgaXNQcm9kID0gZmFsc2U7XHJcbmV4cG9ydCBjb25zdCBCQUNLRU5EX1VSTCA9IGlzUHJvZCA/IGJhY2tlbmQgOiBiYWNrZW5kRGV2O1xyXG5leHBvcnQgY29uc3QgRlJPTlRFTkRfVVJMID0gaXNQcm9kID8gZnJvbnRlbmQgOiBmcm9udGVuZERldjtcclxuZXhwb3J0IGNvbnN0IENPT0tJRV9ET01BSU4gPSBpc1Byb2QgPyBcIi56dGVjaGVuZ2luZWVyaW5nLmNvbVwiIDogXCJsb2NhbGhvc3RcIjtcclxuXHJcbmV4cG9ydCBjb25zdCBSRUFDVF9BUFBfR0dfQVBQX0lEID1cclxuICBcIjQ4MDk4NzM4NDQ1OS1oM2NpZTJ2Y3NocDA5dnBodXZuc2hjY3FwcmNvM2Ziby5hcHBzLmdvb2dsZXVzZXJjb250ZW50LmNvbVwiO1xyXG5leHBvcnQgY29uc3QgQVBJX0dPT0dMRV9NQVBfS0VZID0gXCJBSXphU3lBNXBHeTNVRUt3YmdqVVktNzJSbW9SN25wRXExYl91ZjBcIjtcclxuIl0sIm5hbWVzIjpbImJhY2tlbmREZXYiLCJmcm9udGVuZERldiIsImJhY2tlbmQiLCJmcm9udGVuZCIsImlzUHJvZCIsIkJBQ0tFTkRfVVJMIiwiRlJPTlRFTkRfVVJMIiwiQ09PS0lFX0RPTUFJTiIsIlJFQUNUX0FQUF9HR19BUFBfSUQiLCJBUElfR09PR0xFX01BUF9LRVkiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/config/constant.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/context/AuthContext.jsx":
/*!*****************************************!*\
  !*** ./src/app/context/AuthContext.jsx ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _services_authService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../services/authService */ \"(app-pages-browser)/./src/app/services/authService.js\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Create the Auth Context\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)();\n// Create a Provider Component\nconst AuthProvider = (param)=>{\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cartCount, setCartCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Check if user exists in localStorage on mount\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            await checkAuth();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    const checkAuth = async ()=>{\n        setLoading(true);\n        try {\n            const response = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].checkAuth();\n            const updatedUser = response.data.user;\n            console.log(\"Fetched user:\", updatedUser);\n            // Update only if the data is different\n            if (JSON.stringify(updatedUser) !== localStorage.getItem(\"user\")) {\n                localStorage.setItem(\"user\", JSON.stringify(updatedUser));\n                document.cookie = \"role=\".concat(updatedUser.role, \"; max-age=604800; path=/; secure\");\n            }\n            setUser(updatedUser);\n            return updatedUser;\n        } catch (err) {\n            console.error(\"Auth check failed:\", err);\n            setUser(null);\n            localStorage.removeItem(\"user\");\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Handle authentication error\n    const handleAuthError = (error)=>{\n        var _error_response_data, _error_response;\n        const message = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || \"An unexpected error occurred\";\n        console.error(error);\n        return error;\n    };\n    // Login function\n    const login = async (credentials)=>{\n        setLoading(true);\n        try {\n            const loginRes = await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].login(credentials);\n            const userData = loginRes.data.user;\n            setUser(userData);\n            // Store user in localStorage\n            localStorage.setItem(\"user\", JSON.stringify(userData));\n            document.cookie = \"role=\".concat(userData.role, \"; max-age=604800; path=/; secure\");\n            // Check for pending cart items\n            const pendingItemJson = localStorage.getItem(\"pendingCartItem\");\n            if (pendingItemJson) {\n                try {\n                    // We'll handle this in a separate function after login completes\n                    // Just mark that we have a pending item\n                    loginRes.data.hasPendingCartItem = true;\n                } catch (cartError) {\n                    console.error(\"Error handling pending cart item:\", cartError);\n                }\n            }\n            return loginRes;\n        } catch (error) {\n            var _error_response, _error_response1;\n            const detailedError = {\n                status: (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status,\n                data: (_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : _error_response1.data\n            };\n            throw detailedError;\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Logout function\n    const logout = async ()=>{\n        setLoading(true);\n        try {\n            await _services_authService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].logout();\n            // Clear user from localStorage\n            localStorage.removeItem(\"user\");\n            // Clear cookies on logout\n            document.cookie = \"role=; Max-Age=0; path:/\";\n            document.cookie = \"refresh_token=; Max-Age=0; path=/;\";\n            document.cookie = \"token=; Max-Age=0; path=/;\";\n            setUser(null);\n            router.refresh();\n            router.push(\"/auth/login\"); // Redirect to login page after logout\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.log(\"Logout error:\", ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || error.message);\n        } finally{\n            setLoading(false);\n        }\n    };\n    // Compute if user is authenticated\n    const isAuthenticated = !!user;\n    const value = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>({\n            user,\n            loading,\n            login,\n            logout,\n            checkAuth,\n            cartCount,\n            setCartCount,\n            isAuthenticated\n        }), [\n        user,\n        loading,\n        cartCount,\n        checkAuth,\n        isAuthenticated\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\context\\\\AuthContext.jsx\",\n        lineNumber: 143,\n        columnNumber: 10\n    }, undefined);\n};\n_s(AuthProvider, \"vGpTtn+k/cSLMBUJ8G2NeTQqYd0=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = AuthProvider;\n// Custom hook for using AuthContext\nconst useAuth = ()=>{\n    _s1();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n};\n_s1(useAuth, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/context/AuthContext.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/lib/apiService.js":
/*!***********************************!*\
  !*** ./src/app/lib/apiService.js ***!
  \***********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _axiosInstance__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./axiosInstance */ \"(app-pages-browser)/./src/app/lib/axiosInstance.js\");\n\nconst apiService = {\n    get: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(url, config);\n    },\n    post: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(url, data, config);\n    },\n    put: function(url) {\n        let data = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {}, config = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(url, data, config);\n    },\n    delete: function(url) {\n        let config = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        return _axiosInstance__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(url, config);\n    }\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (apiService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbGliL2FwaVNlcnZpY2UuanMiLCJtYXBwaW5ncyI6Ijs7QUFBNEM7QUFFNUMsTUFBTUMsYUFBYTtJQUNqQkMsS0FBSyxTQUFDQztZQUFLQywwRUFBUyxDQUFDO2VBQU1KLHNEQUFhQSxDQUFDRSxHQUFHLENBQUNDLEtBQUtDOztJQUVsREMsTUFBTSxTQUFDRjtZQUFLRyx3RUFBTyxDQUFDLEdBQUdGLDBFQUFTLENBQUM7ZUFBTUosc0RBQWFBLENBQUNLLElBQUksQ0FBQ0YsS0FBS0csTUFBTUY7O0lBRXJFRyxLQUFLLFNBQUNKO1lBQUtHLHdFQUFPLENBQUMsR0FBR0YsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ08sR0FBRyxDQUFDSixLQUFLRyxNQUFNRjs7SUFFbkVJLFFBQVEsU0FBQ0w7WUFBS0MsMEVBQVMsQ0FBQztlQUFNSixzREFBYUEsQ0FBQ1EsTUFBTSxDQUFDTCxLQUFLQzs7QUFDMUQ7QUFFQSwrREFBZUgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL2xpYi9hcGlTZXJ2aWNlLmpzPzYxZDQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IGF4aW9zSW5zdGFuY2UgZnJvbSBcIi4vYXhpb3NJbnN0YW5jZVwiO1xyXG5cclxuY29uc3QgYXBpU2VydmljZSA9IHtcclxuICBnZXQ6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmdldCh1cmwsIGNvbmZpZyksXHJcblxyXG4gIHBvc3Q6ICh1cmwsIGRhdGEgPSB7fSwgY29uZmlnID0ge30pID0+IGF4aW9zSW5zdGFuY2UucG9zdCh1cmwsIGRhdGEsIGNvbmZpZyksXHJcblxyXG4gIHB1dDogKHVybCwgZGF0YSA9IHt9LCBjb25maWcgPSB7fSkgPT4gYXhpb3NJbnN0YW5jZS5wdXQodXJsLCBkYXRhLCBjb25maWcpLFxyXG5cclxuICBkZWxldGU6ICh1cmwsIGNvbmZpZyA9IHt9KSA9PiBheGlvc0luc3RhbmNlLmRlbGV0ZSh1cmwsIGNvbmZpZyksXHJcbn07XHJcblxyXG5leHBvcnQgZGVmYXVsdCBhcGlTZXJ2aWNlO1xyXG4iXSwibmFtZXMiOlsiYXhpb3NJbnN0YW5jZSIsImFwaVNlcnZpY2UiLCJnZXQiLCJ1cmwiLCJjb25maWciLCJwb3N0IiwiZGF0YSIsInB1dCIsImRlbGV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/lib/apiService.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/services/cartService.js":
/*!*****************************************!*\
  !*** ./src/app/services/cartService.js ***!
  \*****************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst cartService = {\n    // Get the user's cart\n    getCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/cart/get-cart\", {\n            withCredentials: true\n        }),\n    // Add an item to the cart\n    addItemToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/add-item\", data, {\n            withCredentials: true\n        }),\n    // Remove an item from the cart\n    removeItemFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-item\", data, {\n            withCredentials: true\n        }),\n    // Clear the user's cart\n    clearCart: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/clear\", {\n            withCredentials: true\n        }),\n    // Update item quantity in the cart\n    updateCartItemQuantity: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item\", data, {\n            withCredentials: true\n        }),\n    // Update item period in the cart\n    updateItemPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/cart/update-item-period\", data, {\n            withCredentials: true\n        }),\n    // Add method to remove domain from cart\n    removeDomainFromCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/remove-domain\", data, {\n            withCredentials: true\n        }),\n    // Add method to update domain period\n    updateDomainPeriod: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/cart/update-domain-period\", data, {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (cartService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/cartService.js\n"));

/***/ })

});