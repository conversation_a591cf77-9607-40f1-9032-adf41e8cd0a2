"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/layout",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/sidebar.jsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/client/sidebar.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headset.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_10___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_10__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: \"profile\",\n        href: \"/client/profile\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        checkHref: \"/client/profile\"\n    },\n    {\n        name: \"cart\",\n        href: \"/client/cart\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        checkHref: \"/client/cart\"\n    },\n    {\n        name: \"hosting_plans\",\n        href: \"/client/hosting-plans\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        checkHref: \"/client/hosting-plans\"\n    },\n    {\n        name: \"ssl_certificates\",\n        href: \"/client/ssl-certificates\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        checkHref: \"/client/ssl-certificates\"\n    },\n    // { name: \"domains\", href: \"/client/domains\", icon: Globe, checkHref: \"/client/domains\" },\n    {\n        name: \"web_dev.title\",\n        href: \"/client/web-development\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        checkHref: \"/client/web-development\"\n    },\n    {\n        name: \"payment_history\",\n        href: \"/client/payment-history\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        checkHref: \"/client/payment\"\n    },\n    {\n        name: \"support\",\n        href: \"/client/support/tickets\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        checkHref: \"/client/support\"\n    }\n];\nfunction Sidebar(param) {\n    let { isSidebarOpen, setIsSidebarOpen, isMobile } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const normalizedPath = pathname.replace(/^\\/[a-z]{2}/, \"\") || \"/\"; // Removes the locale prefix\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_10__.useTranslations)(\"client\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"sidebar\",\n        className: \"fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white border-l transform \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \" lg:translate-x-0 transition-transform duration-300 ease-in-out border-r border-gray-200 pt-16 lg:pt-0\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"mt-5 px-2 space-y-1\",\n            children: navigation.map((item)=>{\n                // const isActive = pathname.startsWith(item.href);\n                const isActive = normalizedPath.includes(item.checkHref);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: item.href,\n                    className: \"group flex items-center hover:bg-blue-400 hover:text-white px-3 py-3 text-sm font-medium rounded-lg transition-colors \".concat(isActive ? \"bg-blue-50 text-blue-600\" : \"text-gray-700  group-hover:text-gray-500\"),\n                    onClick: ()=>isMobile && setIsSidebarOpen(false),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                            className: \"mr-3 h-5 w-5 \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 15\n                        }, this),\n                        t(\"\".concat(item.name))\n                    ]\n                }, item.name, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n                    lineNumber: 79,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n            lineNumber: 73,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n        lineNumber: 67,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"ubWVzN2Z0wcQh3unbvhqt4fw9Wc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_10__.useTranslations\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/sidebar.jsx\n"));

/***/ })

});