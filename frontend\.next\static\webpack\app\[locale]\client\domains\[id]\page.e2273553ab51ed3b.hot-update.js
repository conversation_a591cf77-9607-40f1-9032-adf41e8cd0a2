"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            const updateData = {\n                orderId: domain.orderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error updating nameservers:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message || \"Failed to update nameservers\");\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 215,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"Nameserver Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 216,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 214,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage the nameservers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 221,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 227,\n                    columnNumber: 11\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"red\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 234,\n                            columnNumber: 13\n                        }, this),\n                        errors.general\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 233,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3 mb-6\",\n                    children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            label: \"Nameserver \".concat(index + 1),\n                                            value: ns,\n                                            onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                            error: !!errors[index],\n                                            className: errors[index] ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 243,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors[index]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 253,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 242,\n                                    columnNumber: 15\n                                }, this),\n                                nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    color: \"red\",\n                                    size: \"sm\",\n                                    onClick: ()=>removeNameserver(index),\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 267,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 260,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 241,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 239,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-6\",\n                    children: [\n                        nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: addNameserver,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 282,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Nameserver\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 276,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: resetToDefault,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 293,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Default\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 287,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 274,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"blue\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 299,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"font-semibold\",\n                                    children: \"Important Notes:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 301,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm mt-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• At least 2 nameservers are required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 303,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Maximum 13 nameservers allowed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 304,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Changes may take 24-48 hours to propagate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 305,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Incorrect nameservers may cause website downtime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 306,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 302,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 300,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                        onClick: handleSave,\n                        disabled: isUpdating,\n                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 17\n                                }, this),\n                                \"Updating...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 17\n                                }, this),\n                                \"Save Changes\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 213,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"0sR8sUYdtmYjlrPleVvRXk6Qprk=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});