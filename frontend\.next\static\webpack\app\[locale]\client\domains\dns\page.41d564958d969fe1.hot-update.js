"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _ChildNameServerManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ChildNameServerManager */ \"(app-pages-browser)/./src/components/domains/ChildNameServerManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Domain name:\", domain.name);\n            console.log(\"\\uD83D\\uDD27 Domain name type:\", typeof domain.name);\n            console.log(\"\\uD83D\\uDD27 Getting domain order ID for:\", domain.name);\n            if (!domain.name) {\n                throw new Error(\"Domain name is missing from domain data\");\n            }\n            // First, get the real domain registration order ID from the API\n            const orderIdResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainOrderId(domain.name);\n            if (!orderIdResponse.data.success) {\n                throw new Error(\"Failed to get domain order ID\");\n            }\n            const realOrderId = orderIdResponse.data.orderId;\n            console.log(\"✅ Got real domain order ID:\", realOrderId);\n            // Check domain status before attempting to modify nameservers\n            console.log(\"\\uD83D\\uDD0D Checking domain status before modification...\");\n            try {\n                const domainDetailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainDetailsByName(domain.name, \"All\");\n                console.log(\"\\uD83D\\uDCCB Domain details:\", domainDetailsResponse.data);\n                if (domainDetailsResponse.data.success && domainDetailsResponse.data.domain) {\n                    const domainInfo = domainDetailsResponse.data.domain;\n                    console.log(\"\\uD83D\\uDCCA Current status:\", domainInfo.currentstatus);\n                    console.log(\"\\uD83D\\uDCCA Order status:\", domainInfo.orderstatus);\n                    console.log(\"\\uD83D\\uDCCA Domain status:\", domainInfo.domainstatus);\n                    console.log(\"\\uD83D\\uDCCA Order ID:\", domainInfo.orderid);\n                    // Check if domain is in a state that allows nameserver modification\n                    if (domainInfo.currentstatus && domainInfo.currentstatus !== \"Active\") {\n                        throw new Error(\"Domain is not active. Current status: \".concat(domainInfo.currentstatus, \". Please wait for the domain to become active before modifying nameservers.\"));\n                    }\n                    // Check if there are any order status locks that might prevent modification\n                    if (domainInfo.orderstatus && Array.isArray(domainInfo.orderstatus) && domainInfo.orderstatus.length > 0) {\n                        console.log(\"⚠️ Domain has order status locks:\", domainInfo.orderstatus);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Domain has status locks: \".concat(domainInfo.orderstatus.join(\", \"), \". Nameserver modification may fail.\"));\n                    }\n                }\n            } catch (statusError) {\n                console.warn(\"⚠️ Could not check domain status:\", statusError.message);\n            // Continue with nameserver modification even if status check fails\n            }\n            const updateData = {\n                orderId: realOrderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data_details, _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error updating nameservers:\", error);\n            // Provide specific error messages based on the error type\n            let errorMessage = \"Failed to update nameservers\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_details = _error_response_data.details) === null || _error_response_data_details === void 0 ? void 0 : _error_response_data_details.message) {\n                const apiError = error.response.data.details.message;\n                if (apiError.includes(\"Invalid Order status\")) {\n                    errorMessage = \"Cannot modify nameservers: Domain order is not in a valid status. The domain may be inactive, suspended, or locked.\";\n                } else {\n                    errorMessage = \"API Error: \".concat(apiError);\n                }\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.error) {\n                errorMessage = error.response.data.error;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 305,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"Nameserver Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 304,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage the nameservers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 311,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 317,\n                    columnNumber: 11\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"red\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 324,\n                            columnNumber: 13\n                        }, this),\n                        errors.general\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 323,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3 mb-6\",\n                    children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            label: \"Nameserver \".concat(index + 1),\n                                            value: ns,\n                                            onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                            error: !!errors[index],\n                                            className: errors[index] ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 333,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors[index]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 332,\n                                    columnNumber: 15\n                                }, this),\n                                nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    color: \"red\",\n                                    size: \"sm\",\n                                    onClick: ()=>removeNameserver(index),\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 350,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 331,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 329,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-6\",\n                    children: [\n                        nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: addNameserver,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 372,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Nameserver\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 366,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: resetToDefault,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 383,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Default\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 377,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 364,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"blue\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 389,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"font-semibold\",\n                                    children: \"Important Notes:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 391,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm mt-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• At least 2 nameservers are required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Maximum 13 nameservers allowed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Changes may take 24-48 hours to propagate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Incorrect nameservers may cause website downtime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 388,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                        onClick: handleSave,\n                        disabled: isUpdating,\n                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 17\n                                }, this),\n                                \"Updating...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 17\n                                }, this),\n                                \"Save Changes\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                        lineNumber: 402,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 401,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 303,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 302,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"0sR8sUYdtmYjlrPleVvRXk6Qprk=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});