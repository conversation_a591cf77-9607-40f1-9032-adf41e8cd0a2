"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/page.jsx":
/*!*********************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/page.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hand-coins.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_6___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_6__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_home_Section__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* harmony import */ var _components_DynamicMetadataClient__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/DynamicMetadataClient */ \"(app-pages-browser)/./src/components/DynamicMetadataClient.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction Hosting() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations)(\"hosting\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Accordion state management\n    const [open, setOpen] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(0);\n    const handleOpen = (value)=>setOpen(open === value ? 0 : value);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_DynamicMetadataClient__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                title: \"ZtechEngineering | \".concat(t(\"title\")),\n                desc: \"\".concat(t(\"desc\"))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                lineNumber: 45,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Section__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"text-center mx-auto\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        variant: \"h1\",\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-extrabold text-gray-900 tracking-tight mb-4 sm:mb-6 animate-fade-in leading-tight\",\n                                        children: [\n                                            t(\"headline_start\"),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent animate-gradient block sm:inline\",\n                                                children: t(\"headline_highlight\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 83,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        variant: \"paragraph\",\n                                        className: \"text-base sm:text-lg xl:text-xl text-gray-600 mb-6 sm:mb-8 xl:mb-12 leading-relaxed animate-fade-in-up max-w-3xl mx-auto\",\n                                        children: t(\"subtext\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 87,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12 sm:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-6xl mx-auto animate-fade-in-up animation-delay-1000\",\n                                        children: [\n                                            {\n                                                text: t(\"trust_indic.0\"),\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                bgColor: \"bg-blue-50\",\n                                                iconColor: \"text-blue-600\"\n                                            },\n                                            {\n                                                text: t(\"trust_indic.1\"),\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                                bgColor: \"bg-green-50\",\n                                                iconColor: \"text-green-600\"\n                                            },\n                                            {\n                                                text: t(\"trust_indic.2\"),\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                                bgColor: \"bg-indigo-50\",\n                                                iconColor: \"text-indigo-600\"\n                                            },\n                                            {\n                                                text: t(\"trust_indic.3\"),\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                                bgColor: \"bg-teal-50\",\n                                                iconColor: \"text-teal-600\"\n                                            }\n                                        ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                className: \"group flex items-center justify-center gap-3 p-4 \".concat(item.bgColor, \" backdrop-blur-md border border-gray-200/50 rounded-xl shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-300\"),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                        className: \"h-6 w-6 \".concat(item.iconColor, \" flex-shrink-0 group-hover:scale-110 transition-transform duration-200\")\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 125,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"small\",\n                                                        className: \"text-sm sm:text-base text-gray-700 font-semibold leading-tight\",\n                                                        children: item.text\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 128,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 121,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                lineNumber: 74,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                id: \"plans\",\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mt-10 sm:mt-18 lg:mt-26 px-4 sm:px-0 max-w-7xl mx-auto\",\n                                children: [\n                                    {\n                                        href: \"/hosting/shared\",\n                                        icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                        title: t(\"hosting_types.0.title\"),\n                                        description: t(\"hosting_types.0.description\"),\n                                        price: t(\"hosting_types.0.price\")\n                                    },\n                                    {\n                                        href: \"/hosting/vps\",\n                                        icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                        title: t(\"hosting_types.1.title\"),\n                                        description: t(\"hosting_types.1.description\"),\n                                        price: t(\"hosting_types.1.price\")\n                                    },\n                                    {\n                                        href: \"/hosting/dedicated\",\n                                        icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n                                        title: t(\"hosting_types.2.title\"),\n                                        description: t(\"hosting_types.2.description\"),\n                                        price: t(\"hosting_types.2.price\")\n                                    }\n                                ].map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                        className: \"group relative h-full bg-white border border-gray-200/50 rounded-2xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 overflow-hidden\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-blue-50/0 via-blue-50/0 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 171,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-6 sm:p-8 flex flex-col items-center relative z-10\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 bg-blue-50 rounded-full mb-6 group-hover:bg-blue-100 group-hover:scale-105 group-hover:shadow-sm transition-all duration-300\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(plan.icon, {\n                                                            className: \"h-12 w-12 text-blue-600 group-hover:text-blue-700 transition-colors duration-300\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 175,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 174,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"h3\",\n                                                        className: \"text-xl sm:text-2xl font-bold text-gray-900 mb-4 tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                        children: plan.title\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 177,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"paragraph\",\n                                                        className: \"text-base text-gray-600 text-center mb-8 flex-grow leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-300\",\n                                                        children: plan.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 183,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        size: \"md\",\n                                                        className: \"w-full sm:w-auto rounded-full shadow-md hover:shadow-lg bg-[#606AF5] focus:ring-4 focus:ring-blue-200 transition-all duration-300 flex items-center justify-center gap-2 px-6 py-3 text-white hover:bg-blue-800 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                        onClick: ()=>router.push(plan.href),\n                                                        \"aria-label\": \"Start plan at \".concat(plan.price, \" MAD per month\"),\n                                                        disabled: !plan.href,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-5 w-5 text-white group-hover:rotate-12 transition-transform duration-300\",\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: t(\"starting_at\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 200,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-lg\",\n                                                                children: plan.price\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 201,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm\",\n                                                                children: \"MAD/mo\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 202,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-5 w-5 text-white group-hover:translate-x-1 transition-transform duration-300\",\n                                                                \"aria-hidden\": \"true\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 189,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, plan.title, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 166,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-16 sm:mt-24 lg:mt-32 mb-16 sm:mb-24 lg:mb-32\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center max-w-3xl mx-auto mb-12 sm:mb-16\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                variant: \"h2\",\n                                                className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6\",\n                                                children: t(\"why_choose_us\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                variant: \"paragraph\",\n                                                className: \"text-base sm:text-lg text-gray-600\",\n                                                children: t(\"experience_blend\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\",\n                                        children: [\n                                            {\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                                title: t(\"hosting_features.0.title\"),\n                                                description: t(\"hosting_features.0.description\")\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                                title: t(\"hosting_features.1.title\"),\n                                                description: t(\"hosting_features.1.description\")\n                                            },\n                                            {\n                                                icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"],\n                                                title: t(\"hosting_features.2.title\"),\n                                                description: t(\"hosting_features.2.description\")\n                                            }\n                                        ].map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                                className: \"p-6 sm:p-8 hover:shadow-lg transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex justify-center items-center gap-2 p-3 bg-blue-50 rounded-xl mb-4 sm:mb-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                                className: \"h-6 w-6 sm:h-8 sm:w-8 text-blue-600\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 251,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                                variant: \"h4\",\n                                                                className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                                                                children: feature.title\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                                lineNumber: 252,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 250,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"paragraph\",\n                                                        className: \"text-sm sm:text-base text-gray-600 leading-relaxed\",\n                                                        children: feature.description\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, feature.title, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                lineNumber: 213,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-20 sm:mb-28 lg:mb-36\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                        variant: \"h2\",\n                                        className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 text-center mb-12 sm:mb-16\",\n                                        children: t(\"faq_heading\")\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 max-w-4xl mx-auto\",\n                                        children: [\n                                            {\n                                                question: t(\"faq.0.question\"),\n                                                answer: t(\"faq.0.answer\")\n                                            },\n                                            {\n                                                question: t(\"faq.1.question\"),\n                                                answer: t(\"faq.1.answer\")\n                                            },\n                                            {\n                                                question: t(\"faq.2.question\"),\n                                                answer: t(\"faq.2.answer\")\n                                            },\n                                            {\n                                                question: t(\"faq.3.question\"),\n                                                answer: t(\"faq.3.answer\")\n                                            }\n                                        ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {\n                                                open: open === index + 1,\n                                                className: \"border border-gray-200/50 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.AccordionHeader, {\n                                                        onClick: ()=>handleOpen(index + 1),\n                                                        className: \"p-6 text-left hover:bg-gray-50 rounded-xl transition-colors duration-200\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                            variant: \"h5\",\n                                                            className: \"text-lg sm:text-xl font-semibold text-gray-900\",\n                                                            children: faq.question\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 387,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 383,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.AccordionBody, {\n                                                        className: \"p-6 pt-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                            variant: \"paragraph\",\n                                                            className: \"text-base text-gray-600 leading-relaxed\",\n                                                            children: faq.answer\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, faq.question, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                lineNumber: 378,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                lineNumber: 364,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                lineNumber: 49,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(Hosting, \"/FdS8onEd06rbGvgZfUyOZPSZGU=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_6__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Hosting;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Hosting);\nvar _c;\n$RefreshReg$(_c, \"Hosting\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/page.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/DynamicMetadataClient.jsx":
/*!**************************************************!*\
  !*** ./src/components/DynamicMetadataClient.jsx ***!
  \**************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst DynamicMetadataClient = (param)=>{\n    let { title, desc } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: title\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\DynamicMetadataClient.jsx\",\n                lineNumber: 6,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: desc\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\DynamicMetadataClient.jsx\",\n                lineNumber: 7,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true);\n};\n_c = DynamicMetadataClient;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DynamicMetadataClient);\nvar _c;\n$RefreshReg$(_c, \"DynamicMetadataClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL0R5bmFtaWNNZXRhZGF0YUNsaWVudC5qc3giLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBeUI7QUFFekIsTUFBTUMsd0JBQXdCO1FBQUMsRUFBQ0MsS0FBSyxFQUFFQyxJQUFJLEVBQUM7SUFDMUMscUJBQ0U7OzBCQUNJLDhEQUFDRDswQkFBT0E7Ozs7OzswQkFDUiw4REFBQ0U7Z0JBQUtDLE1BQUs7Z0JBQWNDLFNBQVNIOzs7Ozs7OztBQUcxQztLQVBNRjtBQVNOLCtEQUFlQSxxQkFBcUJBLEVBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vc3JjL2NvbXBvbmVudHMvRHluYW1pY01ldGFkYXRhQ2xpZW50LmpzeD81NWUyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCdcclxuXHJcbmNvbnN0IER5bmFtaWNNZXRhZGF0YUNsaWVudCA9ICh7dGl0bGUsIGRlc2N9KSA9PiB7XHJcbiAgcmV0dXJuIChcclxuICAgIDw+XHJcbiAgICAgICAgPHRpdGxlPnt0aXRsZX08L3RpdGxlPlxyXG4gICAgICAgIDxtZXRhIG5hbWU9XCJkZXNjcmlwdGlvblwiIGNvbnRlbnQ9e2Rlc2N9IC8+XHJcbiAgICA8Lz5cclxuICApXHJcbn1cclxuXHJcbmV4cG9ydCBkZWZhdWx0IER5bmFtaWNNZXRhZGF0YUNsaWVudCJdLCJuYW1lcyI6WyJSZWFjdCIsIkR5bmFtaWNNZXRhZGF0YUNsaWVudCIsInRpdGxlIiwiZGVzYyIsIm1ldGEiLCJuYW1lIiwiY29udGVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicMetadataClient.jsx\n"));

/***/ })

});