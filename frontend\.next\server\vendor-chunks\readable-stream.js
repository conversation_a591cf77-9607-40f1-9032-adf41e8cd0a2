"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/readable-stream";
exports.ids = ["vendor-chunks/readable-stream"];
exports.modules = {

/***/ "(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js":
/*!************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_duplex.js ***!
  \************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a duplex stream is just a stream that is both readable and writable.\n// Since JS doesn't have multiple prototypal inheritance, this class\n// prototypally inherits from Readable, and then parasitically from\n// Writable.\n\n/*<replacement>*/ var pna = __webpack_require__(/*! process-nextick-args */ \"(ssr)/./node_modules/process-nextick-args/index.js\");\n/*</replacement>*/ /*<replacement>*/ var objectKeys = Object.keys || function(obj) {\n    var keys = [];\n    for(var key in obj){\n        keys.push(key);\n    }\n    return keys;\n};\n/*</replacement>*/ module.exports = Duplex;\n/*<replacement>*/ var util = Object.create(__webpack_require__(/*! core-util-is */ \"(ssr)/./node_modules/core-util-is/lib/util.js\"));\nutil.inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\");\n/*</replacement>*/ var Readable = __webpack_require__(/*! ./_stream_readable */ \"(ssr)/./node_modules/readable-stream/lib/_stream_readable.js\");\nvar Writable = __webpack_require__(/*! ./_stream_writable */ \"(ssr)/./node_modules/readable-stream/lib/_stream_writable.js\");\nutil.inherits(Duplex, Readable);\n{\n    // avoid scope creep, the keys array can then be collected\n    var keys = objectKeys(Writable.prototype);\n    for(var v = 0; v < keys.length; v++){\n        var method = keys[v];\n        if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];\n    }\n}function Duplex(options) {\n    if (!(this instanceof Duplex)) return new Duplex(options);\n    Readable.call(this, options);\n    Writable.call(this, options);\n    if (options && options.readable === false) this.readable = false;\n    if (options && options.writable === false) this.writable = false;\n    this.allowHalfOpen = true;\n    if (options && options.allowHalfOpen === false) this.allowHalfOpen = false;\n    this.once(\"end\", onend);\n}\nObject.defineProperty(Duplex.prototype, \"writableHighWaterMark\", {\n    // making it explicit this property is not enumerable\n    // because otherwise some prototype manipulation in\n    // userland will fail\n    enumerable: false,\n    get: function() {\n        return this._writableState.highWaterMark;\n    }\n});\n// the no-half-open enforcer\nfunction onend() {\n    // if we allow half-open state, or if the writable side ended,\n    // then we're ok.\n    if (this.allowHalfOpen || this._writableState.ended) return;\n    // no more data can be written.\n    // But allow more writes to happen in this tick.\n    pna.nextTick(onEndNT, this);\n}\nfunction onEndNT(self) {\n    self.end();\n}\nObject.defineProperty(Duplex.prototype, \"destroyed\", {\n    get: function() {\n        if (this._readableState === undefined || this._writableState === undefined) {\n            return false;\n        }\n        return this._readableState.destroyed && this._writableState.destroyed;\n    },\n    set: function(value) {\n        // we ignore the value if the stream\n        // has not been initialized yet\n        if (this._readableState === undefined || this._writableState === undefined) {\n            return;\n        }\n        // backward compatibility, the user is explicitly\n        // managing destroyed\n        this._readableState.destroyed = value;\n        this._writableState.destroyed = value;\n    }\n});\nDuplex.prototype._destroy = function(err, cb) {\n    this.push(null);\n    this.end();\n    pna.nextTick(cb, err);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/_stream_passthrough.js":
/*!*****************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_passthrough.js ***!
  \*****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a passthrough stream.\n// basically just the most minimal sort of Transform stream.\n// Every written chunk gets output as-is.\n\nmodule.exports = PassThrough;\nvar Transform = __webpack_require__(/*! ./_stream_transform */ \"(ssr)/./node_modules/readable-stream/lib/_stream_transform.js\");\n/*<replacement>*/ var util = Object.create(__webpack_require__(/*! core-util-is */ \"(ssr)/./node_modules/core-util-is/lib/util.js\"));\nutil.inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\");\n/*</replacement>*/ util.inherits(PassThrough, Transform);\nfunction PassThrough(options) {\n    if (!(this instanceof PassThrough)) return new PassThrough(options);\n    Transform.call(this, options);\n}\nPassThrough.prototype._transform = function(chunk, encoding, cb) {\n    cb(null, chunk);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/_stream_passthrough.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/_stream_readable.js":
/*!**************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_readable.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n/*<replacement>*/ var pna = __webpack_require__(/*! process-nextick-args */ \"(ssr)/./node_modules/process-nextick-args/index.js\");\n/*</replacement>*/ module.exports = Readable;\n/*<replacement>*/ var isArray = __webpack_require__(/*! isarray */ \"(ssr)/./node_modules/readable-stream/node_modules/isarray/index.js\");\n/*</replacement>*/ /*<replacement>*/ var Duplex;\n/*</replacement>*/ Readable.ReadableState = ReadableState;\n/*<replacement>*/ var EE = (__webpack_require__(/*! events */ \"events\").EventEmitter);\nvar EElistenerCount = function(emitter, type) {\n    return emitter.listeners(type).length;\n};\n/*</replacement>*/ /*<replacement>*/ var Stream = __webpack_require__(/*! ./internal/streams/stream */ \"(ssr)/./node_modules/readable-stream/lib/internal/streams/stream.js\");\n/*</replacement>*/ /*<replacement>*/ var Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js\").Buffer);\nvar OurUint8Array = (typeof global !== \"undefined\" ? global :  false ? 0 : typeof self !== \"undefined\" ? self : {}).Uint8Array || function() {};\nfunction _uint8ArrayToBuffer(chunk) {\n    return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n    return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n/*</replacement>*/ /*<replacement>*/ var util = Object.create(__webpack_require__(/*! core-util-is */ \"(ssr)/./node_modules/core-util-is/lib/util.js\"));\nutil.inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\");\n/*</replacement>*/ /*<replacement>*/ var debugUtil = __webpack_require__(/*! util */ \"util\");\nvar debug = void 0;\nif (debugUtil && debugUtil.debuglog) {\n    debug = debugUtil.debuglog(\"stream\");\n} else {\n    debug = function() {};\n}\n/*</replacement>*/ var BufferList = __webpack_require__(/*! ./internal/streams/BufferList */ \"(ssr)/./node_modules/readable-stream/lib/internal/streams/BufferList.js\");\nvar destroyImpl = __webpack_require__(/*! ./internal/streams/destroy */ \"(ssr)/./node_modules/readable-stream/lib/internal/streams/destroy.js\");\nvar StringDecoder;\nutil.inherits(Readable, Stream);\nvar kProxyEvents = [\n    \"error\",\n    \"close\",\n    \"destroy\",\n    \"pause\",\n    \"resume\"\n];\nfunction prependListener(emitter, event, fn) {\n    // Sadly this is not cacheable as some libraries bundle their own\n    // event emitter implementation with them.\n    if (typeof emitter.prependListener === \"function\") return emitter.prependListener(event, fn);\n    // This is a hack to make sure that our error handler is attached before any\n    // userland ones.  NEVER DO THIS. This is here only because this code needs\n    // to continue to work with older versions of Node.js that do not include\n    // the prependListener() method. The goal is to eventually remove this hack.\n    if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);\n    else if (isArray(emitter._events[event])) emitter._events[event].unshift(fn);\n    else emitter._events[event] = [\n        fn,\n        emitter._events[event]\n    ];\n}\nfunction ReadableState(options, stream) {\n    Duplex = Duplex || __webpack_require__(/*! ./_stream_duplex */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n    options = options || {};\n    // Duplex streams are both readable and writable, but share\n    // the same options object.\n    // However, some cases require setting options to different\n    // values for the readable and the writable sides of the duplex stream.\n    // These options can be provided separately as readableXXX and writableXXX.\n    var isDuplex = stream instanceof Duplex;\n    // object stream flag. Used to make read(n) ignore n and to\n    // make all the buffer merging and length checks go away\n    this.objectMode = !!options.objectMode;\n    if (isDuplex) this.objectMode = this.objectMode || !!options.readableObjectMode;\n    // the point at which it stops calling _read() to fill the buffer\n    // Note: 0 is a valid value, means \"don't call _read preemptively ever\"\n    var hwm = options.highWaterMark;\n    var readableHwm = options.readableHighWaterMark;\n    var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n    if (hwm || hwm === 0) this.highWaterMark = hwm;\n    else if (isDuplex && (readableHwm || readableHwm === 0)) this.highWaterMark = readableHwm;\n    else this.highWaterMark = defaultHwm;\n    // cast to ints.\n    this.highWaterMark = Math.floor(this.highWaterMark);\n    // A linked list is used to store data chunks instead of an array because the\n    // linked list can remove elements from the beginning faster than\n    // array.shift()\n    this.buffer = new BufferList();\n    this.length = 0;\n    this.pipes = null;\n    this.pipesCount = 0;\n    this.flowing = null;\n    this.ended = false;\n    this.endEmitted = false;\n    this.reading = false;\n    // a flag to be able to tell if the event 'readable'/'data' is emitted\n    // immediately, or on a later tick.  We set this to true at first, because\n    // any actions that shouldn't happen until \"later\" should generally also\n    // not happen before the first read call.\n    this.sync = true;\n    // whenever we return null, then we set a flag to say\n    // that we're awaiting a 'readable' event emission.\n    this.needReadable = false;\n    this.emittedReadable = false;\n    this.readableListening = false;\n    this.resumeScheduled = false;\n    // has it been destroyed\n    this.destroyed = false;\n    // Crypto is kind of old and crusty.  Historically, its default string\n    // encoding is 'binary' so we have to make this configurable.\n    // Everything else in the universe uses 'utf8', though.\n    this.defaultEncoding = options.defaultEncoding || \"utf8\";\n    // the number of writers that are awaiting a drain event in .pipe()s\n    this.awaitDrain = 0;\n    // if true, a maybeReadMore has been scheduled\n    this.readingMore = false;\n    this.decoder = null;\n    this.encoding = null;\n    if (options.encoding) {\n        if (!StringDecoder) StringDecoder = (__webpack_require__(/*! string_decoder/ */ \"(ssr)/./node_modules/readable-stream/node_modules/string_decoder/lib/string_decoder.js\").StringDecoder);\n        this.decoder = new StringDecoder(options.encoding);\n        this.encoding = options.encoding;\n    }\n}\nfunction Readable(options) {\n    Duplex = Duplex || __webpack_require__(/*! ./_stream_duplex */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n    if (!(this instanceof Readable)) return new Readable(options);\n    this._readableState = new ReadableState(options, this);\n    // legacy\n    this.readable = true;\n    if (options) {\n        if (typeof options.read === \"function\") this._read = options.read;\n        if (typeof options.destroy === \"function\") this._destroy = options.destroy;\n    }\n    Stream.call(this);\n}\nObject.defineProperty(Readable.prototype, \"destroyed\", {\n    get: function() {\n        if (this._readableState === undefined) {\n            return false;\n        }\n        return this._readableState.destroyed;\n    },\n    set: function(value) {\n        // we ignore the value if the stream\n        // has not been initialized yet\n        if (!this._readableState) {\n            return;\n        }\n        // backward compatibility, the user is explicitly\n        // managing destroyed\n        this._readableState.destroyed = value;\n    }\n});\nReadable.prototype.destroy = destroyImpl.destroy;\nReadable.prototype._undestroy = destroyImpl.undestroy;\nReadable.prototype._destroy = function(err, cb) {\n    this.push(null);\n    cb(err);\n};\n// Manually shove something into the read() buffer.\n// This returns true if the highWaterMark has not been hit yet,\n// similar to how Writable.write() returns true if you should\n// write() some more.\nReadable.prototype.push = function(chunk, encoding) {\n    var state = this._readableState;\n    var skipChunkCheck;\n    if (!state.objectMode) {\n        if (typeof chunk === \"string\") {\n            encoding = encoding || state.defaultEncoding;\n            if (encoding !== state.encoding) {\n                chunk = Buffer.from(chunk, encoding);\n                encoding = \"\";\n            }\n            skipChunkCheck = true;\n        }\n    } else {\n        skipChunkCheck = true;\n    }\n    return readableAddChunk(this, chunk, encoding, false, skipChunkCheck);\n};\n// Unshift should *always* be something directly out of read()\nReadable.prototype.unshift = function(chunk) {\n    return readableAddChunk(this, chunk, null, true, false);\n};\nfunction readableAddChunk(stream, chunk, encoding, addToFront, skipChunkCheck) {\n    var state = stream._readableState;\n    if (chunk === null) {\n        state.reading = false;\n        onEofChunk(stream, state);\n    } else {\n        var er;\n        if (!skipChunkCheck) er = chunkInvalid(state, chunk);\n        if (er) {\n            stream.emit(\"error\", er);\n        } else if (state.objectMode || chunk && chunk.length > 0) {\n            if (typeof chunk !== \"string\" && !state.objectMode && Object.getPrototypeOf(chunk) !== Buffer.prototype) {\n                chunk = _uint8ArrayToBuffer(chunk);\n            }\n            if (addToFront) {\n                if (state.endEmitted) stream.emit(\"error\", new Error(\"stream.unshift() after end event\"));\n                else addChunk(stream, state, chunk, true);\n            } else if (state.ended) {\n                stream.emit(\"error\", new Error(\"stream.push() after EOF\"));\n            } else {\n                state.reading = false;\n                if (state.decoder && !encoding) {\n                    chunk = state.decoder.write(chunk);\n                    if (state.objectMode || chunk.length !== 0) addChunk(stream, state, chunk, false);\n                    else maybeReadMore(stream, state);\n                } else {\n                    addChunk(stream, state, chunk, false);\n                }\n            }\n        } else if (!addToFront) {\n            state.reading = false;\n        }\n    }\n    return needMoreData(state);\n}\nfunction addChunk(stream, state, chunk, addToFront) {\n    if (state.flowing && state.length === 0 && !state.sync) {\n        stream.emit(\"data\", chunk);\n        stream.read(0);\n    } else {\n        // update the buffer info.\n        state.length += state.objectMode ? 1 : chunk.length;\n        if (addToFront) state.buffer.unshift(chunk);\n        else state.buffer.push(chunk);\n        if (state.needReadable) emitReadable(stream);\n    }\n    maybeReadMore(stream, state);\n}\nfunction chunkInvalid(state, chunk) {\n    var er;\n    if (!_isUint8Array(chunk) && typeof chunk !== \"string\" && chunk !== undefined && !state.objectMode) {\n        er = new TypeError(\"Invalid non-string/buffer chunk\");\n    }\n    return er;\n}\n// if it's past the high water mark, we can push in some more.\n// Also, if we have no data yet, we can stand some\n// more bytes.  This is to work around cases where hwm=0,\n// such as the repl.  Also, if the push() triggered a\n// readable event, and the user called read(largeNumber) such that\n// needReadable was set, then we ought to push more, so that another\n// 'readable' event will be triggered.\nfunction needMoreData(state) {\n    return !state.ended && (state.needReadable || state.length < state.highWaterMark || state.length === 0);\n}\nReadable.prototype.isPaused = function() {\n    return this._readableState.flowing === false;\n};\n// backwards compatibility.\nReadable.prototype.setEncoding = function(enc) {\n    if (!StringDecoder) StringDecoder = (__webpack_require__(/*! string_decoder/ */ \"(ssr)/./node_modules/readable-stream/node_modules/string_decoder/lib/string_decoder.js\").StringDecoder);\n    this._readableState.decoder = new StringDecoder(enc);\n    this._readableState.encoding = enc;\n    return this;\n};\n// Don't raise the hwm > 8MB\nvar MAX_HWM = 0x800000;\nfunction computeNewHighWaterMark(n) {\n    if (n >= MAX_HWM) {\n        n = MAX_HWM;\n    } else {\n        // Get the next highest power of 2 to prevent increasing hwm excessively in\n        // tiny amounts\n        n--;\n        n |= n >>> 1;\n        n |= n >>> 2;\n        n |= n >>> 4;\n        n |= n >>> 8;\n        n |= n >>> 16;\n        n++;\n    }\n    return n;\n}\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction howMuchToRead(n, state) {\n    if (n <= 0 || state.length === 0 && state.ended) return 0;\n    if (state.objectMode) return 1;\n    if (n !== n) {\n        // Only flow one buffer at a time\n        if (state.flowing && state.length) return state.buffer.head.data.length;\n        else return state.length;\n    }\n    // If we're asking for more than the current hwm, then raise the hwm.\n    if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);\n    if (n <= state.length) return n;\n    // Don't have enough\n    if (!state.ended) {\n        state.needReadable = true;\n        return 0;\n    }\n    return state.length;\n}\n// you can override either this method, or the async _read(n) below.\nReadable.prototype.read = function(n) {\n    debug(\"read\", n);\n    n = parseInt(n, 10);\n    var state = this._readableState;\n    var nOrig = n;\n    if (n !== 0) state.emittedReadable = false;\n    // if we're doing read(0) to trigger a readable event, but we\n    // already have a bunch of data in the buffer, then just trigger\n    // the 'readable' event and move on.\n    if (n === 0 && state.needReadable && (state.length >= state.highWaterMark || state.ended)) {\n        debug(\"read: emitReadable\", state.length, state.ended);\n        if (state.length === 0 && state.ended) endReadable(this);\n        else emitReadable(this);\n        return null;\n    }\n    n = howMuchToRead(n, state);\n    // if we've ended, and we're now clear, then finish it up.\n    if (n === 0 && state.ended) {\n        if (state.length === 0) endReadable(this);\n        return null;\n    }\n    // All the actual chunk generation logic needs to be\n    // *below* the call to _read.  The reason is that in certain\n    // synthetic stream cases, such as passthrough streams, _read\n    // may be a completely synchronous operation which may change\n    // the state of the read buffer, providing enough data when\n    // before there was *not* enough.\n    //\n    // So, the steps are:\n    // 1. Figure out what the state of things will be after we do\n    // a read from the buffer.\n    //\n    // 2. If that resulting state will trigger a _read, then call _read.\n    // Note that this may be asynchronous, or synchronous.  Yes, it is\n    // deeply ugly to write APIs this way, but that still doesn't mean\n    // that the Readable class should behave improperly, as streams are\n    // designed to be sync/async agnostic.\n    // Take note if the _read call is sync or async (ie, if the read call\n    // has returned yet), so that we know whether or not it's safe to emit\n    // 'readable' etc.\n    //\n    // 3. Actually pull the requested chunks out of the buffer and return.\n    // if we need a readable event, then we need to do some reading.\n    var doRead = state.needReadable;\n    debug(\"need readable\", doRead);\n    // if we currently have less than the highWaterMark, then also read some\n    if (state.length === 0 || state.length - n < state.highWaterMark) {\n        doRead = true;\n        debug(\"length less than watermark\", doRead);\n    }\n    // however, if we've ended, then there's no point, and if we're already\n    // reading, then it's unnecessary.\n    if (state.ended || state.reading) {\n        doRead = false;\n        debug(\"reading or ended\", doRead);\n    } else if (doRead) {\n        debug(\"do read\");\n        state.reading = true;\n        state.sync = true;\n        // if the length is currently zero, then we *need* a readable event.\n        if (state.length === 0) state.needReadable = true;\n        // call internal read method\n        this._read(state.highWaterMark);\n        state.sync = false;\n        // If _read pushed data synchronously, then `reading` will be false,\n        // and we need to re-evaluate how much data we can return to the user.\n        if (!state.reading) n = howMuchToRead(nOrig, state);\n    }\n    var ret;\n    if (n > 0) ret = fromList(n, state);\n    else ret = null;\n    if (ret === null) {\n        state.needReadable = true;\n        n = 0;\n    } else {\n        state.length -= n;\n    }\n    if (state.length === 0) {\n        // If we have nothing in the buffer, then we want to know\n        // as soon as we *do* get something into the buffer.\n        if (!state.ended) state.needReadable = true;\n        // If we tried to read() past the EOF, then emit end on the next tick.\n        if (nOrig !== n && state.ended) endReadable(this);\n    }\n    if (ret !== null) this.emit(\"data\", ret);\n    return ret;\n};\nfunction onEofChunk(stream, state) {\n    if (state.ended) return;\n    if (state.decoder) {\n        var chunk = state.decoder.end();\n        if (chunk && chunk.length) {\n            state.buffer.push(chunk);\n            state.length += state.objectMode ? 1 : chunk.length;\n        }\n    }\n    state.ended = true;\n    // emit 'readable' now to make sure it gets picked up.\n    emitReadable(stream);\n}\n// Don't emit readable right away in sync mode, because this can trigger\n// another read() call => stack overflow.  This way, it might trigger\n// a nextTick recursion warning, but that's not so bad.\nfunction emitReadable(stream) {\n    var state = stream._readableState;\n    state.needReadable = false;\n    if (!state.emittedReadable) {\n        debug(\"emitReadable\", state.flowing);\n        state.emittedReadable = true;\n        if (state.sync) pna.nextTick(emitReadable_, stream);\n        else emitReadable_(stream);\n    }\n}\nfunction emitReadable_(stream) {\n    debug(\"emit readable\");\n    stream.emit(\"readable\");\n    flow(stream);\n}\n// at this point, the user has presumably seen the 'readable' event,\n// and called read() to consume some data.  that may have triggered\n// in turn another _read(n) call, in which case reading = true if\n// it's in progress.\n// However, if we're not ended, or reading, and the length < hwm,\n// then go ahead and try to read some more preemptively.\nfunction maybeReadMore(stream, state) {\n    if (!state.readingMore) {\n        state.readingMore = true;\n        pna.nextTick(maybeReadMore_, stream, state);\n    }\n}\nfunction maybeReadMore_(stream, state) {\n    var len = state.length;\n    while(!state.reading && !state.flowing && !state.ended && state.length < state.highWaterMark){\n        debug(\"maybeReadMore read 0\");\n        stream.read(0);\n        if (len === state.length) break;\n        else len = state.length;\n    }\n    state.readingMore = false;\n}\n// abstract method.  to be overridden in specific implementation classes.\n// call cb(er, data) where data is <= n in length.\n// for virtual (non-string, non-buffer) streams, \"length\" is somewhat\n// arbitrary, and perhaps not very meaningful.\nReadable.prototype._read = function(n) {\n    this.emit(\"error\", new Error(\"_read() is not implemented\"));\n};\nReadable.prototype.pipe = function(dest, pipeOpts) {\n    var src = this;\n    var state = this._readableState;\n    switch(state.pipesCount){\n        case 0:\n            state.pipes = dest;\n            break;\n        case 1:\n            state.pipes = [\n                state.pipes,\n                dest\n            ];\n            break;\n        default:\n            state.pipes.push(dest);\n            break;\n    }\n    state.pipesCount += 1;\n    debug(\"pipe count=%d opts=%j\", state.pipesCount, pipeOpts);\n    var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;\n    var endFn = doEnd ? onend : unpipe;\n    if (state.endEmitted) pna.nextTick(endFn);\n    else src.once(\"end\", endFn);\n    dest.on(\"unpipe\", onunpipe);\n    function onunpipe(readable, unpipeInfo) {\n        debug(\"onunpipe\");\n        if (readable === src) {\n            if (unpipeInfo && unpipeInfo.hasUnpiped === false) {\n                unpipeInfo.hasUnpiped = true;\n                cleanup();\n            }\n        }\n    }\n    function onend() {\n        debug(\"onend\");\n        dest.end();\n    }\n    // when the dest drains, it reduces the awaitDrain counter\n    // on the source.  This would be more elegant with a .once()\n    // handler in flow(), but adding and removing repeatedly is\n    // too slow.\n    var ondrain = pipeOnDrain(src);\n    dest.on(\"drain\", ondrain);\n    var cleanedUp = false;\n    function cleanup() {\n        debug(\"cleanup\");\n        // cleanup event handlers once the pipe is broken\n        dest.removeListener(\"close\", onclose);\n        dest.removeListener(\"finish\", onfinish);\n        dest.removeListener(\"drain\", ondrain);\n        dest.removeListener(\"error\", onerror);\n        dest.removeListener(\"unpipe\", onunpipe);\n        src.removeListener(\"end\", onend);\n        src.removeListener(\"end\", unpipe);\n        src.removeListener(\"data\", ondata);\n        cleanedUp = true;\n        // if the reader is waiting for a drain event from this\n        // specific writer, then it would cause it to never start\n        // flowing again.\n        // So, if this is awaiting a drain, then we just call it now.\n        // If we don't know, then assume that we are waiting for one.\n        if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();\n    }\n    // If the user pushes more data while we're writing to dest then we'll end up\n    // in ondata again. However, we only want to increase awaitDrain once because\n    // dest will only emit one 'drain' event for the multiple writes.\n    // => Introduce a guard on increasing awaitDrain.\n    var increasedAwaitDrain = false;\n    src.on(\"data\", ondata);\n    function ondata(chunk) {\n        debug(\"ondata\");\n        increasedAwaitDrain = false;\n        var ret = dest.write(chunk);\n        if (false === ret && !increasedAwaitDrain) {\n            // If the user unpiped during `dest.write()`, it is possible\n            // to get stuck in a permanently paused state if that write\n            // also returned false.\n            // => Check whether `dest` is still a piping destination.\n            if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {\n                debug(\"false write response, pause\", state.awaitDrain);\n                state.awaitDrain++;\n                increasedAwaitDrain = true;\n            }\n            src.pause();\n        }\n    }\n    // if the dest has an error, then stop piping into it.\n    // however, don't suppress the throwing behavior for this.\n    function onerror(er) {\n        debug(\"onerror\", er);\n        unpipe();\n        dest.removeListener(\"error\", onerror);\n        if (EElistenerCount(dest, \"error\") === 0) dest.emit(\"error\", er);\n    }\n    // Make sure our error handler is attached before userland ones.\n    prependListener(dest, \"error\", onerror);\n    // Both close and finish should trigger unpipe, but only once.\n    function onclose() {\n        dest.removeListener(\"finish\", onfinish);\n        unpipe();\n    }\n    dest.once(\"close\", onclose);\n    function onfinish() {\n        debug(\"onfinish\");\n        dest.removeListener(\"close\", onclose);\n        unpipe();\n    }\n    dest.once(\"finish\", onfinish);\n    function unpipe() {\n        debug(\"unpipe\");\n        src.unpipe(dest);\n    }\n    // tell the dest that it's being piped to\n    dest.emit(\"pipe\", src);\n    // start the flow if it hasn't been started already.\n    if (!state.flowing) {\n        debug(\"pipe resume\");\n        src.resume();\n    }\n    return dest;\n};\nfunction pipeOnDrain(src) {\n    return function() {\n        var state = src._readableState;\n        debug(\"pipeOnDrain\", state.awaitDrain);\n        if (state.awaitDrain) state.awaitDrain--;\n        if (state.awaitDrain === 0 && EElistenerCount(src, \"data\")) {\n            state.flowing = true;\n            flow(src);\n        }\n    };\n}\nReadable.prototype.unpipe = function(dest) {\n    var state = this._readableState;\n    var unpipeInfo = {\n        hasUnpiped: false\n    };\n    // if we're not piping anywhere, then do nothing.\n    if (state.pipesCount === 0) return this;\n    // just one destination.  most common case.\n    if (state.pipesCount === 1) {\n        // passed in one, but it's not the right one.\n        if (dest && dest !== state.pipes) return this;\n        if (!dest) dest = state.pipes;\n        // got a match.\n        state.pipes = null;\n        state.pipesCount = 0;\n        state.flowing = false;\n        if (dest) dest.emit(\"unpipe\", this, unpipeInfo);\n        return this;\n    }\n    // slow case. multiple pipe destinations.\n    if (!dest) {\n        // remove all.\n        var dests = state.pipes;\n        var len = state.pipesCount;\n        state.pipes = null;\n        state.pipesCount = 0;\n        state.flowing = false;\n        for(var i = 0; i < len; i++){\n            dests[i].emit(\"unpipe\", this, {\n                hasUnpiped: false\n            });\n        }\n        return this;\n    }\n    // try to find the right one.\n    var index = indexOf(state.pipes, dest);\n    if (index === -1) return this;\n    state.pipes.splice(index, 1);\n    state.pipesCount -= 1;\n    if (state.pipesCount === 1) state.pipes = state.pipes[0];\n    dest.emit(\"unpipe\", this, unpipeInfo);\n    return this;\n};\n// set up data events if they are asked for\n// Ensure readable listeners eventually get something\nReadable.prototype.on = function(ev, fn) {\n    var res = Stream.prototype.on.call(this, ev, fn);\n    if (ev === \"data\") {\n        // Start flowing on next tick if stream isn't explicitly paused\n        if (this._readableState.flowing !== false) this.resume();\n    } else if (ev === \"readable\") {\n        var state = this._readableState;\n        if (!state.endEmitted && !state.readableListening) {\n            state.readableListening = state.needReadable = true;\n            state.emittedReadable = false;\n            if (!state.reading) {\n                pna.nextTick(nReadingNextTick, this);\n            } else if (state.length) {\n                emitReadable(this);\n            }\n        }\n    }\n    return res;\n};\nReadable.prototype.addListener = Readable.prototype.on;\nfunction nReadingNextTick(self1) {\n    debug(\"readable nexttick read 0\");\n    self1.read(0);\n}\n// pause() and resume() are remnants of the legacy readable stream API\n// If the user uses them, then switch into old mode.\nReadable.prototype.resume = function() {\n    var state = this._readableState;\n    if (!state.flowing) {\n        debug(\"resume\");\n        state.flowing = true;\n        resume(this, state);\n    }\n    return this;\n};\nfunction resume(stream, state) {\n    if (!state.resumeScheduled) {\n        state.resumeScheduled = true;\n        pna.nextTick(resume_, stream, state);\n    }\n}\nfunction resume_(stream, state) {\n    if (!state.reading) {\n        debug(\"resume read 0\");\n        stream.read(0);\n    }\n    state.resumeScheduled = false;\n    state.awaitDrain = 0;\n    stream.emit(\"resume\");\n    flow(stream);\n    if (state.flowing && !state.reading) stream.read(0);\n}\nReadable.prototype.pause = function() {\n    debug(\"call pause flowing=%j\", this._readableState.flowing);\n    if (false !== this._readableState.flowing) {\n        debug(\"pause\");\n        this._readableState.flowing = false;\n        this.emit(\"pause\");\n    }\n    return this;\n};\nfunction flow(stream) {\n    var state = stream._readableState;\n    debug(\"flow\", state.flowing);\n    while(state.flowing && stream.read() !== null){}\n}\n// wrap an old-style stream as the async data source.\n// This is *not* part of the readable stream interface.\n// It is an ugly unfortunate mess of history.\nReadable.prototype.wrap = function(stream) {\n    var _this = this;\n    var state = this._readableState;\n    var paused = false;\n    stream.on(\"end\", function() {\n        debug(\"wrapped end\");\n        if (state.decoder && !state.ended) {\n            var chunk = state.decoder.end();\n            if (chunk && chunk.length) _this.push(chunk);\n        }\n        _this.push(null);\n    });\n    stream.on(\"data\", function(chunk) {\n        debug(\"wrapped data\");\n        if (state.decoder) chunk = state.decoder.write(chunk);\n        // don't skip over falsy values in objectMode\n        if (state.objectMode && (chunk === null || chunk === undefined)) return;\n        else if (!state.objectMode && (!chunk || !chunk.length)) return;\n        var ret = _this.push(chunk);\n        if (!ret) {\n            paused = true;\n            stream.pause();\n        }\n    });\n    // proxy all the other methods.\n    // important when wrapping filters and duplexes.\n    for(var i in stream){\n        if (this[i] === undefined && typeof stream[i] === \"function\") {\n            this[i] = function(method) {\n                return function() {\n                    return stream[method].apply(stream, arguments);\n                };\n            }(i);\n        }\n    }\n    // proxy certain important events.\n    for(var n = 0; n < kProxyEvents.length; n++){\n        stream.on(kProxyEvents[n], this.emit.bind(this, kProxyEvents[n]));\n    }\n    // when we try to consume some more bytes, simply unpause the\n    // underlying stream.\n    this._read = function(n) {\n        debug(\"wrapped _read\", n);\n        if (paused) {\n            paused = false;\n            stream.resume();\n        }\n    };\n    return this;\n};\nObject.defineProperty(Readable.prototype, \"readableHighWaterMark\", {\n    // making it explicit this property is not enumerable\n    // because otherwise some prototype manipulation in\n    // userland will fail\n    enumerable: false,\n    get: function() {\n        return this._readableState.highWaterMark;\n    }\n});\n// exposed for testing purposes only.\nReadable._fromList = fromList;\n// Pluck off n bytes from an array of buffers.\n// Length is the combined lengths of all the buffers in the list.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromList(n, state) {\n    // nothing buffered\n    if (state.length === 0) return null;\n    var ret;\n    if (state.objectMode) ret = state.buffer.shift();\n    else if (!n || n >= state.length) {\n        // read it all, truncate the list\n        if (state.decoder) ret = state.buffer.join(\"\");\n        else if (state.buffer.length === 1) ret = state.buffer.head.data;\n        else ret = state.buffer.concat(state.length);\n        state.buffer.clear();\n    } else {\n        // read part of list\n        ret = fromListPartial(n, state.buffer, state.decoder);\n    }\n    return ret;\n}\n// Extracts only enough buffered data to satisfy the amount requested.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction fromListPartial(n, list, hasStrings) {\n    var ret;\n    if (n < list.head.data.length) {\n        // slice is the same for buffers and strings\n        ret = list.head.data.slice(0, n);\n        list.head.data = list.head.data.slice(n);\n    } else if (n === list.head.data.length) {\n        // first chunk is a perfect match\n        ret = list.shift();\n    } else {\n        // result spans more than one buffer\n        ret = hasStrings ? copyFromBufferString(n, list) : copyFromBuffer(n, list);\n    }\n    return ret;\n}\n// Copies a specified amount of characters from the list of buffered data\n// chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBufferString(n, list) {\n    var p = list.head;\n    var c = 1;\n    var ret = p.data;\n    n -= ret.length;\n    while(p = p.next){\n        var str = p.data;\n        var nb = n > str.length ? str.length : n;\n        if (nb === str.length) ret += str;\n        else ret += str.slice(0, n);\n        n -= nb;\n        if (n === 0) {\n            if (nb === str.length) {\n                ++c;\n                if (p.next) list.head = p.next;\n                else list.head = list.tail = null;\n            } else {\n                list.head = p;\n                p.data = str.slice(nb);\n            }\n            break;\n        }\n        ++c;\n    }\n    list.length -= c;\n    return ret;\n}\n// Copies a specified amount of bytes from the list of buffered data chunks.\n// This function is designed to be inlinable, so please take care when making\n// changes to the function body.\nfunction copyFromBuffer(n, list) {\n    var ret = Buffer.allocUnsafe(n);\n    var p = list.head;\n    var c = 1;\n    p.data.copy(ret);\n    n -= p.data.length;\n    while(p = p.next){\n        var buf = p.data;\n        var nb = n > buf.length ? buf.length : n;\n        buf.copy(ret, ret.length - n, 0, nb);\n        n -= nb;\n        if (n === 0) {\n            if (nb === buf.length) {\n                ++c;\n                if (p.next) list.head = p.next;\n                else list.head = list.tail = null;\n            } else {\n                list.head = p;\n                p.data = buf.slice(nb);\n            }\n            break;\n        }\n        ++c;\n    }\n    list.length -= c;\n    return ret;\n}\nfunction endReadable(stream) {\n    var state = stream._readableState;\n    // If we get here before consuming all the bytes, then that is a\n    // bug in node.  Should never happen.\n    if (state.length > 0) throw new Error('\"endReadable()\" called on non-empty stream');\n    if (!state.endEmitted) {\n        state.ended = true;\n        pna.nextTick(endReadableNT, state, stream);\n    }\n}\nfunction endReadableNT(state, stream) {\n    // Check that we didn't get one last unshift.\n    if (!state.endEmitted && state.length === 0) {\n        state.endEmitted = true;\n        stream.readable = false;\n        stream.emit(\"end\");\n    }\n}\nfunction indexOf(xs, x) {\n    for(var i = 0, l = xs.length; i < l; i++){\n        if (xs[i] === x) return i;\n    }\n    return -1;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/_stream_readable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/_stream_transform.js":
/*!***************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_transform.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// a transform stream is a readable/writable stream where you do\n// something with the data.  Sometimes it's called a \"filter\",\n// but that's not a great name for it, since that implies a thing where\n// some bits pass through, and others are simply ignored.  (That would\n// be a valid example of a transform, of course.)\n//\n// While the output is causally related to the input, it's not a\n// necessarily symmetric or synchronous transformation.  For example,\n// a zlib stream might take multiple plain-text writes(), and then\n// emit a single compressed chunk some time in the future.\n//\n// Here's how this works:\n//\n// The Transform stream has all the aspects of the readable and writable\n// stream classes.  When you write(chunk), that calls _write(chunk,cb)\n// internally, and returns false if there's a lot of pending writes\n// buffered up.  When you call read(), that calls _read(n) until\n// there's enough pending readable data buffered up.\n//\n// In a transform stream, the written data is placed in a buffer.  When\n// _read(n) is called, it transforms the queued up data, calling the\n// buffered _write cb's as it consumes chunks.  If consuming a single\n// written chunk would result in multiple output chunks, then the first\n// outputted bit calls the readcb, and subsequent chunks just go into\n// the read buffer, and will cause it to emit 'readable' if necessary.\n//\n// This way, back-pressure is actually determined by the reading side,\n// since _read has to be called to start processing a new chunk.  However,\n// a pathological inflate type of transform can cause excessive buffering\n// here.  For example, imagine a stream where every byte of input is\n// interpreted as an integer from 0-255, and then results in that many\n// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in\n// 1kb of data being output.  In this case, you could write a very small\n// amount of input, and end up with a very large amount of output.  In\n// such a pathological inflating mechanism, there'd be no way to tell\n// the system to stop doing the transform.  A single 4MB write could\n// cause the system to run out of memory.\n//\n// However, even in such a pathological case, only a single written chunk\n// would be consumed, and then the rest would wait (un-transformed) until\n// the results of the previous transformed chunk were consumed.\n\nmodule.exports = Transform;\nvar Duplex = __webpack_require__(/*! ./_stream_duplex */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n/*<replacement>*/ var util = Object.create(__webpack_require__(/*! core-util-is */ \"(ssr)/./node_modules/core-util-is/lib/util.js\"));\nutil.inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\");\n/*</replacement>*/ util.inherits(Transform, Duplex);\nfunction afterTransform(er, data) {\n    var ts = this._transformState;\n    ts.transforming = false;\n    var cb = ts.writecb;\n    if (!cb) {\n        return this.emit(\"error\", new Error(\"write callback called multiple times\"));\n    }\n    ts.writechunk = null;\n    ts.writecb = null;\n    if (data != null) this.push(data);\n    cb(er);\n    var rs = this._readableState;\n    rs.reading = false;\n    if (rs.needReadable || rs.length < rs.highWaterMark) {\n        this._read(rs.highWaterMark);\n    }\n}\nfunction Transform(options) {\n    if (!(this instanceof Transform)) return new Transform(options);\n    Duplex.call(this, options);\n    this._transformState = {\n        afterTransform: afterTransform.bind(this),\n        needTransform: false,\n        transforming: false,\n        writecb: null,\n        writechunk: null,\n        writeencoding: null\n    };\n    // start out asking for a readable event once data is transformed.\n    this._readableState.needReadable = true;\n    // we have implemented the _read method, and done the other things\n    // that Readable wants before the first _read call, so unset the\n    // sync guard flag.\n    this._readableState.sync = false;\n    if (options) {\n        if (typeof options.transform === \"function\") this._transform = options.transform;\n        if (typeof options.flush === \"function\") this._flush = options.flush;\n    }\n    // When the writable side finishes, then flush out anything remaining.\n    this.on(\"prefinish\", prefinish);\n}\nfunction prefinish() {\n    var _this = this;\n    if (typeof this._flush === \"function\") {\n        this._flush(function(er, data) {\n            done(_this, er, data);\n        });\n    } else {\n        done(this, null, null);\n    }\n}\nTransform.prototype.push = function(chunk, encoding) {\n    this._transformState.needTransform = false;\n    return Duplex.prototype.push.call(this, chunk, encoding);\n};\n// This is the part where you do stuff!\n// override this function in implementation classes.\n// 'chunk' is an input chunk.\n//\n// Call `push(newChunk)` to pass along transformed output\n// to the readable side.  You may call 'push' zero or more times.\n//\n// Call `cb(err)` when you are done with this chunk.  If you pass\n// an error, then that'll put the hurt on the whole operation.  If you\n// never call cb(), then you'll never get another chunk.\nTransform.prototype._transform = function(chunk, encoding, cb) {\n    throw new Error(\"_transform() is not implemented\");\n};\nTransform.prototype._write = function(chunk, encoding, cb) {\n    var ts = this._transformState;\n    ts.writecb = cb;\n    ts.writechunk = chunk;\n    ts.writeencoding = encoding;\n    if (!ts.transforming) {\n        var rs = this._readableState;\n        if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);\n    }\n};\n// Doesn't matter what the args are here.\n// _transform does all the work.\n// That we got here means that the readable side wants more data.\nTransform.prototype._read = function(n) {\n    var ts = this._transformState;\n    if (ts.writechunk !== null && ts.writecb && !ts.transforming) {\n        ts.transforming = true;\n        this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);\n    } else {\n        // mark that we need a transform, so that any data that comes in\n        // will get processed, now that we've asked for it.\n        ts.needTransform = true;\n    }\n};\nTransform.prototype._destroy = function(err, cb) {\n    var _this2 = this;\n    Duplex.prototype._destroy.call(this, err, function(err2) {\n        cb(err2);\n        _this2.emit(\"close\");\n    });\n};\nfunction done(stream, er, data) {\n    if (er) return stream.emit(\"error\", er);\n    if (data != null) stream.push(data);\n    // if there's nothing in the write buffer, then that means\n    // that nothing more will ever be provided\n    if (stream._writableState.length) throw new Error(\"Calling transform done when ws.length != 0\");\n    if (stream._transformState.transforming) throw new Error(\"Calling transform done when still transforming\");\n    return stream.push(null);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/_stream_transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/_stream_writable.js":
/*!**************************************************************!*\
  !*** ./node_modules/readable-stream/lib/_stream_writable.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n// A bit simpler than readable streams.\n// Implement an async ._write(chunk, encoding, cb), and it'll handle all\n// the drain event emission and buffering.\n\n/*<replacement>*/ var pna = __webpack_require__(/*! process-nextick-args */ \"(ssr)/./node_modules/process-nextick-args/index.js\");\n/*</replacement>*/ module.exports = Writable;\n/* <replacement> */ function WriteReq(chunk, encoding, cb) {\n    this.chunk = chunk;\n    this.encoding = encoding;\n    this.callback = cb;\n    this.next = null;\n}\n// It seems a linked list but it is not\n// there will be only 2 of these for each stream\nfunction CorkedRequest(state) {\n    var _this = this;\n    this.next = null;\n    this.entry = null;\n    this.finish = function() {\n        onCorkedFinish(_this, state);\n    };\n}\n/* </replacement> */ /*<replacement>*/ var asyncWrite =  true && [\n    \"v0.10\",\n    \"v0.9.\"\n].indexOf(process.version.slice(0, 5)) > -1 ? setImmediate : pna.nextTick;\n/*</replacement>*/ /*<replacement>*/ var Duplex;\n/*</replacement>*/ Writable.WritableState = WritableState;\n/*<replacement>*/ var util = Object.create(__webpack_require__(/*! core-util-is */ \"(ssr)/./node_modules/core-util-is/lib/util.js\"));\nutil.inherits = __webpack_require__(/*! inherits */ \"(ssr)/./node_modules/inherits/inherits.js\");\n/*</replacement>*/ /*<replacement>*/ var internalUtil = {\n    deprecate: __webpack_require__(/*! util-deprecate */ \"(ssr)/./node_modules/util-deprecate/node.js\")\n};\n/*</replacement>*/ /*<replacement>*/ var Stream = __webpack_require__(/*! ./internal/streams/stream */ \"(ssr)/./node_modules/readable-stream/lib/internal/streams/stream.js\");\n/*</replacement>*/ /*<replacement>*/ var Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js\").Buffer);\nvar OurUint8Array = (typeof global !== \"undefined\" ? global :  false ? 0 : typeof self !== \"undefined\" ? self : {}).Uint8Array || function() {};\nfunction _uint8ArrayToBuffer(chunk) {\n    return Buffer.from(chunk);\n}\nfunction _isUint8Array(obj) {\n    return Buffer.isBuffer(obj) || obj instanceof OurUint8Array;\n}\n/*</replacement>*/ var destroyImpl = __webpack_require__(/*! ./internal/streams/destroy */ \"(ssr)/./node_modules/readable-stream/lib/internal/streams/destroy.js\");\nutil.inherits(Writable, Stream);\nfunction nop() {}\nfunction WritableState(options, stream) {\n    Duplex = Duplex || __webpack_require__(/*! ./_stream_duplex */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n    options = options || {};\n    // Duplex streams are both readable and writable, but share\n    // the same options object.\n    // However, some cases require setting options to different\n    // values for the readable and the writable sides of the duplex stream.\n    // These options can be provided separately as readableXXX and writableXXX.\n    var isDuplex = stream instanceof Duplex;\n    // object stream flag to indicate whether or not this stream\n    // contains buffers or objects.\n    this.objectMode = !!options.objectMode;\n    if (isDuplex) this.objectMode = this.objectMode || !!options.writableObjectMode;\n    // the point at which write() starts returning false\n    // Note: 0 is a valid value, means that we always return false if\n    // the entire buffer is not flushed immediately on write()\n    var hwm = options.highWaterMark;\n    var writableHwm = options.writableHighWaterMark;\n    var defaultHwm = this.objectMode ? 16 : 16 * 1024;\n    if (hwm || hwm === 0) this.highWaterMark = hwm;\n    else if (isDuplex && (writableHwm || writableHwm === 0)) this.highWaterMark = writableHwm;\n    else this.highWaterMark = defaultHwm;\n    // cast to ints.\n    this.highWaterMark = Math.floor(this.highWaterMark);\n    // if _final has been called\n    this.finalCalled = false;\n    // drain event flag.\n    this.needDrain = false;\n    // at the start of calling end()\n    this.ending = false;\n    // when end() has been called, and returned\n    this.ended = false;\n    // when 'finish' is emitted\n    this.finished = false;\n    // has it been destroyed\n    this.destroyed = false;\n    // should we decode strings into buffers before passing to _write?\n    // this is here so that some node-core streams can optimize string\n    // handling at a lower level.\n    var noDecode = options.decodeStrings === false;\n    this.decodeStrings = !noDecode;\n    // Crypto is kind of old and crusty.  Historically, its default string\n    // encoding is 'binary' so we have to make this configurable.\n    // Everything else in the universe uses 'utf8', though.\n    this.defaultEncoding = options.defaultEncoding || \"utf8\";\n    // not an actual buffer we keep track of, but a measurement\n    // of how much we're waiting to get pushed to some underlying\n    // socket or file.\n    this.length = 0;\n    // a flag to see when we're in the middle of a write.\n    this.writing = false;\n    // when true all writes will be buffered until .uncork() call\n    this.corked = 0;\n    // a flag to be able to tell if the onwrite cb is called immediately,\n    // or on a later tick.  We set this to true at first, because any\n    // actions that shouldn't happen until \"later\" should generally also\n    // not happen before the first write call.\n    this.sync = true;\n    // a flag to know if we're processing previously buffered items, which\n    // may call the _write() callback in the same tick, so that we don't\n    // end up in an overlapped onwrite situation.\n    this.bufferProcessing = false;\n    // the callback that's passed to _write(chunk,cb)\n    this.onwrite = function(er) {\n        onwrite(stream, er);\n    };\n    // the callback that the user supplies to write(chunk,encoding,cb)\n    this.writecb = null;\n    // the amount that is being written when _write is called.\n    this.writelen = 0;\n    this.bufferedRequest = null;\n    this.lastBufferedRequest = null;\n    // number of pending user-supplied write callbacks\n    // this must be 0 before 'finish' can be emitted\n    this.pendingcb = 0;\n    // emit prefinish if the only thing we're waiting for is _write cbs\n    // This is relevant for synchronous Transform streams\n    this.prefinished = false;\n    // True if the error was already emitted and should not be thrown again\n    this.errorEmitted = false;\n    // count buffered requests\n    this.bufferedRequestCount = 0;\n    // allocate the first CorkedRequest, there is always\n    // one allocated and free to use, and we maintain at most two\n    this.corkedRequestsFree = new CorkedRequest(this);\n}\nWritableState.prototype.getBuffer = function getBuffer() {\n    var current = this.bufferedRequest;\n    var out = [];\n    while(current){\n        out.push(current);\n        current = current.next;\n    }\n    return out;\n};\n(function() {\n    try {\n        Object.defineProperty(WritableState.prototype, \"buffer\", {\n            get: internalUtil.deprecate(function() {\n                return this.getBuffer();\n            }, \"_writableState.buffer is deprecated. Use _writableState.getBuffer \" + \"instead.\", \"DEP0003\")\n        });\n    } catch (_) {}\n})();\n// Test _writableState for inheritance to account for Duplex streams,\n// whose prototype chain only points to Readable.\nvar realHasInstance;\nif (typeof Symbol === \"function\" && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === \"function\") {\n    realHasInstance = Function.prototype[Symbol.hasInstance];\n    Object.defineProperty(Writable, Symbol.hasInstance, {\n        value: function(object) {\n            if (realHasInstance.call(this, object)) return true;\n            if (this !== Writable) return false;\n            return object && object._writableState instanceof WritableState;\n        }\n    });\n} else {\n    realHasInstance = function(object) {\n        return object instanceof this;\n    };\n}\nfunction Writable(options) {\n    Duplex = Duplex || __webpack_require__(/*! ./_stream_duplex */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n    // Writable ctor is applied to Duplexes, too.\n    // `realHasInstance` is necessary because using plain `instanceof`\n    // would return false, as no `_writableState` property is attached.\n    // Trying to use the custom `instanceof` for Writable here will also break the\n    // Node.js LazyTransform implementation, which has a non-trivial getter for\n    // `_writableState` that would lead to infinite recursion.\n    if (!realHasInstance.call(Writable, this) && !(this instanceof Duplex)) {\n        return new Writable(options);\n    }\n    this._writableState = new WritableState(options, this);\n    // legacy.\n    this.writable = true;\n    if (options) {\n        if (typeof options.write === \"function\") this._write = options.write;\n        if (typeof options.writev === \"function\") this._writev = options.writev;\n        if (typeof options.destroy === \"function\") this._destroy = options.destroy;\n        if (typeof options.final === \"function\") this._final = options.final;\n    }\n    Stream.call(this);\n}\n// Otherwise people can pipe Writable streams, which is just wrong.\nWritable.prototype.pipe = function() {\n    this.emit(\"error\", new Error(\"Cannot pipe, not readable\"));\n};\nfunction writeAfterEnd(stream, cb) {\n    var er = new Error(\"write after end\");\n    // TODO: defer error events consistently everywhere, not just the cb\n    stream.emit(\"error\", er);\n    pna.nextTick(cb, er);\n}\n// Checks that a user-supplied chunk is valid, especially for the particular\n// mode the stream is in. Currently this means that `null` is never accepted\n// and undefined/non-string values are only allowed in object mode.\nfunction validChunk(stream, state, chunk, cb) {\n    var valid = true;\n    var er = false;\n    if (chunk === null) {\n        er = new TypeError(\"May not write null values to stream\");\n    } else if (typeof chunk !== \"string\" && chunk !== undefined && !state.objectMode) {\n        er = new TypeError(\"Invalid non-string/buffer chunk\");\n    }\n    if (er) {\n        stream.emit(\"error\", er);\n        pna.nextTick(cb, er);\n        valid = false;\n    }\n    return valid;\n}\nWritable.prototype.write = function(chunk, encoding, cb) {\n    var state = this._writableState;\n    var ret = false;\n    var isBuf = !state.objectMode && _isUint8Array(chunk);\n    if (isBuf && !Buffer.isBuffer(chunk)) {\n        chunk = _uint8ArrayToBuffer(chunk);\n    }\n    if (typeof encoding === \"function\") {\n        cb = encoding;\n        encoding = null;\n    }\n    if (isBuf) encoding = \"buffer\";\n    else if (!encoding) encoding = state.defaultEncoding;\n    if (typeof cb !== \"function\") cb = nop;\n    if (state.ended) writeAfterEnd(this, cb);\n    else if (isBuf || validChunk(this, state, chunk, cb)) {\n        state.pendingcb++;\n        ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);\n    }\n    return ret;\n};\nWritable.prototype.cork = function() {\n    var state = this._writableState;\n    state.corked++;\n};\nWritable.prototype.uncork = function() {\n    var state = this._writableState;\n    if (state.corked) {\n        state.corked--;\n        if (!state.writing && !state.corked && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);\n    }\n};\nWritable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {\n    // node::ParseEncoding() requires lower case.\n    if (typeof encoding === \"string\") encoding = encoding.toLowerCase();\n    if (!([\n        \"hex\",\n        \"utf8\",\n        \"utf-8\",\n        \"ascii\",\n        \"binary\",\n        \"base64\",\n        \"ucs2\",\n        \"ucs-2\",\n        \"utf16le\",\n        \"utf-16le\",\n        \"raw\"\n    ].indexOf((encoding + \"\").toLowerCase()) > -1)) throw new TypeError(\"Unknown encoding: \" + encoding);\n    this._writableState.defaultEncoding = encoding;\n    return this;\n};\nfunction decodeChunk(state, chunk, encoding) {\n    if (!state.objectMode && state.decodeStrings !== false && typeof chunk === \"string\") {\n        chunk = Buffer.from(chunk, encoding);\n    }\n    return chunk;\n}\nObject.defineProperty(Writable.prototype, \"writableHighWaterMark\", {\n    // making it explicit this property is not enumerable\n    // because otherwise some prototype manipulation in\n    // userland will fail\n    enumerable: false,\n    get: function() {\n        return this._writableState.highWaterMark;\n    }\n});\n// if we're already writing something, then just put this\n// in the queue, and wait our turn.  Otherwise, call _write\n// If we return false, then we need a drain event, so set that flag.\nfunction writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {\n    if (!isBuf) {\n        var newChunk = decodeChunk(state, chunk, encoding);\n        if (chunk !== newChunk) {\n            isBuf = true;\n            encoding = \"buffer\";\n            chunk = newChunk;\n        }\n    }\n    var len = state.objectMode ? 1 : chunk.length;\n    state.length += len;\n    var ret = state.length < state.highWaterMark;\n    // we must ensure that previous needDrain will not be reset to false.\n    if (!ret) state.needDrain = true;\n    if (state.writing || state.corked) {\n        var last = state.lastBufferedRequest;\n        state.lastBufferedRequest = {\n            chunk: chunk,\n            encoding: encoding,\n            isBuf: isBuf,\n            callback: cb,\n            next: null\n        };\n        if (last) {\n            last.next = state.lastBufferedRequest;\n        } else {\n            state.bufferedRequest = state.lastBufferedRequest;\n        }\n        state.bufferedRequestCount += 1;\n    } else {\n        doWrite(stream, state, false, len, chunk, encoding, cb);\n    }\n    return ret;\n}\nfunction doWrite(stream, state, writev, len, chunk, encoding, cb) {\n    state.writelen = len;\n    state.writecb = cb;\n    state.writing = true;\n    state.sync = true;\n    if (writev) stream._writev(chunk, state.onwrite);\n    else stream._write(chunk, encoding, state.onwrite);\n    state.sync = false;\n}\nfunction onwriteError(stream, state, sync, er, cb) {\n    --state.pendingcb;\n    if (sync) {\n        // defer the callback if we are being called synchronously\n        // to avoid piling up things on the stack\n        pna.nextTick(cb, er);\n        // this can emit finish, and it will always happen\n        // after error\n        pna.nextTick(finishMaybe, stream, state);\n        stream._writableState.errorEmitted = true;\n        stream.emit(\"error\", er);\n    } else {\n        // the caller expect this to happen before if\n        // it is async\n        cb(er);\n        stream._writableState.errorEmitted = true;\n        stream.emit(\"error\", er);\n        // this can emit finish, but finish must\n        // always follow error\n        finishMaybe(stream, state);\n    }\n}\nfunction onwriteStateUpdate(state) {\n    state.writing = false;\n    state.writecb = null;\n    state.length -= state.writelen;\n    state.writelen = 0;\n}\nfunction onwrite(stream, er) {\n    var state = stream._writableState;\n    var sync = state.sync;\n    var cb = state.writecb;\n    onwriteStateUpdate(state);\n    if (er) onwriteError(stream, state, sync, er, cb);\n    else {\n        // Check if we're actually ready to finish, but don't emit yet\n        var finished = needFinish(state);\n        if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {\n            clearBuffer(stream, state);\n        }\n        if (sync) {\n            /*<replacement>*/ asyncWrite(afterWrite, stream, state, finished, cb);\n        /*</replacement>*/ } else {\n            afterWrite(stream, state, finished, cb);\n        }\n    }\n}\nfunction afterWrite(stream, state, finished, cb) {\n    if (!finished) onwriteDrain(stream, state);\n    state.pendingcb--;\n    cb();\n    finishMaybe(stream, state);\n}\n// Must force callback to be called on nextTick, so that we don't\n// emit 'drain' before the write() consumer gets the 'false' return\n// value, and has a chance to attach a 'drain' listener.\nfunction onwriteDrain(stream, state) {\n    if (state.length === 0 && state.needDrain) {\n        state.needDrain = false;\n        stream.emit(\"drain\");\n    }\n}\n// if there's something in the buffer waiting, then process it\nfunction clearBuffer(stream, state) {\n    state.bufferProcessing = true;\n    var entry = state.bufferedRequest;\n    if (stream._writev && entry && entry.next) {\n        // Fast case, write everything using _writev()\n        var l = state.bufferedRequestCount;\n        var buffer = new Array(l);\n        var holder = state.corkedRequestsFree;\n        holder.entry = entry;\n        var count = 0;\n        var allBuffers = true;\n        while(entry){\n            buffer[count] = entry;\n            if (!entry.isBuf) allBuffers = false;\n            entry = entry.next;\n            count += 1;\n        }\n        buffer.allBuffers = allBuffers;\n        doWrite(stream, state, true, state.length, buffer, \"\", holder.finish);\n        // doWrite is almost always async, defer these to save a bit of time\n        // as the hot path ends with doWrite\n        state.pendingcb++;\n        state.lastBufferedRequest = null;\n        if (holder.next) {\n            state.corkedRequestsFree = holder.next;\n            holder.next = null;\n        } else {\n            state.corkedRequestsFree = new CorkedRequest(state);\n        }\n        state.bufferedRequestCount = 0;\n    } else {\n        // Slow case, write chunks one-by-one\n        while(entry){\n            var chunk = entry.chunk;\n            var encoding = entry.encoding;\n            var cb = entry.callback;\n            var len = state.objectMode ? 1 : chunk.length;\n            doWrite(stream, state, false, len, chunk, encoding, cb);\n            entry = entry.next;\n            state.bufferedRequestCount--;\n            // if we didn't call the onwrite immediately, then\n            // it means that we need to wait until it does.\n            // also, that means that the chunk and cb are currently\n            // being processed, so move the buffer counter past them.\n            if (state.writing) {\n                break;\n            }\n        }\n        if (entry === null) state.lastBufferedRequest = null;\n    }\n    state.bufferedRequest = entry;\n    state.bufferProcessing = false;\n}\nWritable.prototype._write = function(chunk, encoding, cb) {\n    cb(new Error(\"_write() is not implemented\"));\n};\nWritable.prototype._writev = null;\nWritable.prototype.end = function(chunk, encoding, cb) {\n    var state = this._writableState;\n    if (typeof chunk === \"function\") {\n        cb = chunk;\n        chunk = null;\n        encoding = null;\n    } else if (typeof encoding === \"function\") {\n        cb = encoding;\n        encoding = null;\n    }\n    if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);\n    // .end() fully uncorks\n    if (state.corked) {\n        state.corked = 1;\n        this.uncork();\n    }\n    // ignore unnecessary end() calls.\n    if (!state.ending) endWritable(this, state, cb);\n};\nfunction needFinish(state) {\n    return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;\n}\nfunction callFinal(stream, state) {\n    stream._final(function(err) {\n        state.pendingcb--;\n        if (err) {\n            stream.emit(\"error\", err);\n        }\n        state.prefinished = true;\n        stream.emit(\"prefinish\");\n        finishMaybe(stream, state);\n    });\n}\nfunction prefinish(stream, state) {\n    if (!state.prefinished && !state.finalCalled) {\n        if (typeof stream._final === \"function\") {\n            state.pendingcb++;\n            state.finalCalled = true;\n            pna.nextTick(callFinal, stream, state);\n        } else {\n            state.prefinished = true;\n            stream.emit(\"prefinish\");\n        }\n    }\n}\nfunction finishMaybe(stream, state) {\n    var need = needFinish(state);\n    if (need) {\n        prefinish(stream, state);\n        if (state.pendingcb === 0) {\n            state.finished = true;\n            stream.emit(\"finish\");\n        }\n    }\n    return need;\n}\nfunction endWritable(stream, state, cb) {\n    state.ending = true;\n    finishMaybe(stream, state);\n    if (cb) {\n        if (state.finished) pna.nextTick(cb);\n        else stream.once(\"finish\", cb);\n    }\n    state.ended = true;\n    stream.writable = false;\n}\nfunction onCorkedFinish(corkReq, state, err) {\n    var entry = corkReq.entry;\n    corkReq.entry = null;\n    while(entry){\n        var cb = entry.callback;\n        state.pendingcb--;\n        cb(err);\n        entry = entry.next;\n    }\n    // reuse the free corkReq.\n    state.corkedRequestsFree.next = corkReq;\n}\nObject.defineProperty(Writable.prototype, \"destroyed\", {\n    get: function() {\n        if (this._writableState === undefined) {\n            return false;\n        }\n        return this._writableState.destroyed;\n    },\n    set: function(value) {\n        // we ignore the value if the stream\n        // has not been initialized yet\n        if (!this._writableState) {\n            return;\n        }\n        // backward compatibility, the user is explicitly\n        // managing destroyed\n        this._writableState.destroyed = value;\n    }\n});\nWritable.prototype.destroy = destroyImpl.destroy;\nWritable.prototype._undestroy = destroyImpl.undestroy;\nWritable.prototype._destroy = function(err, cb) {\n    this.end();\n    cb(err);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/_stream_writable.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/internal/streams/BufferList.js":
/*!*************************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/BufferList.js ***!
  \*************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nfunction _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n        throw new TypeError(\"Cannot call a class as a function\");\n    }\n}\nvar Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js\").Buffer);\nvar util = __webpack_require__(/*! util */ \"util\");\nfunction copyBuffer(src, target, offset) {\n    src.copy(target, offset);\n}\nmodule.exports = function() {\n    function BufferList() {\n        _classCallCheck(this, BufferList);\n        this.head = null;\n        this.tail = null;\n        this.length = 0;\n    }\n    BufferList.prototype.push = function push(v) {\n        var entry = {\n            data: v,\n            next: null\n        };\n        if (this.length > 0) this.tail.next = entry;\n        else this.head = entry;\n        this.tail = entry;\n        ++this.length;\n    };\n    BufferList.prototype.unshift = function unshift(v) {\n        var entry = {\n            data: v,\n            next: this.head\n        };\n        if (this.length === 0) this.tail = entry;\n        this.head = entry;\n        ++this.length;\n    };\n    BufferList.prototype.shift = function shift() {\n        if (this.length === 0) return;\n        var ret = this.head.data;\n        if (this.length === 1) this.head = this.tail = null;\n        else this.head = this.head.next;\n        --this.length;\n        return ret;\n    };\n    BufferList.prototype.clear = function clear() {\n        this.head = this.tail = null;\n        this.length = 0;\n    };\n    BufferList.prototype.join = function join(s) {\n        if (this.length === 0) return \"\";\n        var p = this.head;\n        var ret = \"\" + p.data;\n        while(p = p.next){\n            ret += s + p.data;\n        }\n        return ret;\n    };\n    BufferList.prototype.concat = function concat(n) {\n        if (this.length === 0) return Buffer.alloc(0);\n        var ret = Buffer.allocUnsafe(n >>> 0);\n        var p = this.head;\n        var i = 0;\n        while(p){\n            copyBuffer(p.data, ret, i);\n            i += p.data.length;\n            p = p.next;\n        }\n        return ret;\n    };\n    return BufferList;\n}();\nif (util && util.inspect && util.inspect.custom) {\n    module.exports.prototype[util.inspect.custom] = function() {\n        var obj = util.inspect({\n            length: this.length\n        });\n        return this.constructor.name + \" \" + obj;\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhZGFibGUtc3RyZWFtL2xpYi9pbnRlcm5hbC9zdHJlYW1zL0J1ZmZlckxpc3QuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQSxTQUFTQSxnQkFBZ0JDLFFBQVEsRUFBRUMsV0FBVztJQUFJLElBQUksQ0FBRUQsQ0FBQUEsb0JBQW9CQyxXQUFVLEdBQUk7UUFBRSxNQUFNLElBQUlDLFVBQVU7SUFBc0M7QUFBRTtBQUV4SixJQUFJQyxTQUFTQyx5SEFBNkI7QUFDMUMsSUFBSUMsT0FBT0QsbUJBQU9BLENBQUM7QUFFbkIsU0FBU0UsV0FBV0MsR0FBRyxFQUFFQyxNQUFNLEVBQUVDLE1BQU07SUFDckNGLElBQUlHLElBQUksQ0FBQ0YsUUFBUUM7QUFDbkI7QUFFQUUsT0FBT0MsT0FBTyxHQUFHO0lBQ2YsU0FBU0M7UUFDUGQsZ0JBQWdCLElBQUksRUFBRWM7UUFFdEIsSUFBSSxDQUFDQyxJQUFJLEdBQUc7UUFDWixJQUFJLENBQUNDLElBQUksR0FBRztRQUNaLElBQUksQ0FBQ0MsTUFBTSxHQUFHO0lBQ2hCO0lBRUFILFdBQVdJLFNBQVMsQ0FBQ0MsSUFBSSxHQUFHLFNBQVNBLEtBQUtDLENBQUM7UUFDekMsSUFBSUMsUUFBUTtZQUFFQyxNQUFNRjtZQUFHRyxNQUFNO1FBQUs7UUFDbEMsSUFBSSxJQUFJLENBQUNOLE1BQU0sR0FBRyxHQUFHLElBQUksQ0FBQ0QsSUFBSSxDQUFDTyxJQUFJLEdBQUdGO2FBQVcsSUFBSSxDQUFDTixJQUFJLEdBQUdNO1FBQzdELElBQUksQ0FBQ0wsSUFBSSxHQUFHSztRQUNaLEVBQUUsSUFBSSxDQUFDSixNQUFNO0lBQ2Y7SUFFQUgsV0FBV0ksU0FBUyxDQUFDTSxPQUFPLEdBQUcsU0FBU0EsUUFBUUosQ0FBQztRQUMvQyxJQUFJQyxRQUFRO1lBQUVDLE1BQU1GO1lBQUdHLE1BQU0sSUFBSSxDQUFDUixJQUFJO1FBQUM7UUFDdkMsSUFBSSxJQUFJLENBQUNFLE1BQU0sS0FBSyxHQUFHLElBQUksQ0FBQ0QsSUFBSSxHQUFHSztRQUNuQyxJQUFJLENBQUNOLElBQUksR0FBR007UUFDWixFQUFFLElBQUksQ0FBQ0osTUFBTTtJQUNmO0lBRUFILFdBQVdJLFNBQVMsQ0FBQ08sS0FBSyxHQUFHLFNBQVNBO1FBQ3BDLElBQUksSUFBSSxDQUFDUixNQUFNLEtBQUssR0FBRztRQUN2QixJQUFJUyxNQUFNLElBQUksQ0FBQ1gsSUFBSSxDQUFDTyxJQUFJO1FBQ3hCLElBQUksSUFBSSxDQUFDTCxNQUFNLEtBQUssR0FBRyxJQUFJLENBQUNGLElBQUksR0FBRyxJQUFJLENBQUNDLElBQUksR0FBRzthQUFVLElBQUksQ0FBQ0QsSUFBSSxHQUFHLElBQUksQ0FBQ0EsSUFBSSxDQUFDUSxJQUFJO1FBQ25GLEVBQUUsSUFBSSxDQUFDTixNQUFNO1FBQ2IsT0FBT1M7SUFDVDtJQUVBWixXQUFXSSxTQUFTLENBQUNTLEtBQUssR0FBRyxTQUFTQTtRQUNwQyxJQUFJLENBQUNaLElBQUksR0FBRyxJQUFJLENBQUNDLElBQUksR0FBRztRQUN4QixJQUFJLENBQUNDLE1BQU0sR0FBRztJQUNoQjtJQUVBSCxXQUFXSSxTQUFTLENBQUNVLElBQUksR0FBRyxTQUFTQSxLQUFLQyxDQUFDO1FBQ3pDLElBQUksSUFBSSxDQUFDWixNQUFNLEtBQUssR0FBRyxPQUFPO1FBQzlCLElBQUlhLElBQUksSUFBSSxDQUFDZixJQUFJO1FBQ2pCLElBQUlXLE1BQU0sS0FBS0ksRUFBRVIsSUFBSTtRQUNyQixNQUFPUSxJQUFJQSxFQUFFUCxJQUFJLENBQUU7WUFDakJHLE9BQU9HLElBQUlDLEVBQUVSLElBQUk7UUFDbkI7UUFBQyxPQUFPSTtJQUNWO0lBRUFaLFdBQVdJLFNBQVMsQ0FBQ2EsTUFBTSxHQUFHLFNBQVNBLE9BQU9DLENBQUM7UUFDN0MsSUFBSSxJQUFJLENBQUNmLE1BQU0sS0FBSyxHQUFHLE9BQU9iLE9BQU82QixLQUFLLENBQUM7UUFDM0MsSUFBSVAsTUFBTXRCLE9BQU84QixXQUFXLENBQUNGLE1BQU07UUFDbkMsSUFBSUYsSUFBSSxJQUFJLENBQUNmLElBQUk7UUFDakIsSUFBSW9CLElBQUk7UUFDUixNQUFPTCxFQUFHO1lBQ1J2QixXQUFXdUIsRUFBRVIsSUFBSSxFQUFFSSxLQUFLUztZQUN4QkEsS0FBS0wsRUFBRVIsSUFBSSxDQUFDTCxNQUFNO1lBQ2xCYSxJQUFJQSxFQUFFUCxJQUFJO1FBQ1o7UUFDQSxPQUFPRztJQUNUO0lBRUEsT0FBT1o7QUFDVDtBQUVBLElBQUlSLFFBQVFBLEtBQUs4QixPQUFPLElBQUk5QixLQUFLOEIsT0FBTyxDQUFDQyxNQUFNLEVBQUU7SUFDL0N6QixPQUFPQyxPQUFPLENBQUNLLFNBQVMsQ0FBQ1osS0FBSzhCLE9BQU8sQ0FBQ0MsTUFBTSxDQUFDLEdBQUc7UUFDOUMsSUFBSUMsTUFBTWhDLEtBQUs4QixPQUFPLENBQUM7WUFBRW5CLFFBQVEsSUFBSSxDQUFDQSxNQUFNO1FBQUM7UUFDN0MsT0FBTyxJQUFJLENBQUNzQixXQUFXLENBQUNDLElBQUksR0FBRyxNQUFNRjtJQUN2QztBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9yZWFkYWJsZS1zdHJlYW0vbGliL2ludGVybmFsL3N0cmVhbXMvQnVmZmVyTGlzdC5qcz9kZTMyIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuZnVuY3Rpb24gX2NsYXNzQ2FsbENoZWNrKGluc3RhbmNlLCBDb25zdHJ1Y3RvcikgeyBpZiAoIShpbnN0YW5jZSBpbnN0YW5jZW9mIENvbnN0cnVjdG9yKSkgeyB0aHJvdyBuZXcgVHlwZUVycm9yKFwiQ2Fubm90IGNhbGwgYSBjbGFzcyBhcyBhIGZ1bmN0aW9uXCIpOyB9IH1cblxudmFyIEJ1ZmZlciA9IHJlcXVpcmUoJ3NhZmUtYnVmZmVyJykuQnVmZmVyO1xudmFyIHV0aWwgPSByZXF1aXJlKCd1dGlsJyk7XG5cbmZ1bmN0aW9uIGNvcHlCdWZmZXIoc3JjLCB0YXJnZXQsIG9mZnNldCkge1xuICBzcmMuY29weSh0YXJnZXQsIG9mZnNldCk7XG59XG5cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gKCkge1xuICBmdW5jdGlvbiBCdWZmZXJMaXN0KCkge1xuICAgIF9jbGFzc0NhbGxDaGVjayh0aGlzLCBCdWZmZXJMaXN0KTtcblxuICAgIHRoaXMuaGVhZCA9IG51bGw7XG4gICAgdGhpcy50YWlsID0gbnVsbDtcbiAgICB0aGlzLmxlbmd0aCA9IDA7XG4gIH1cblxuICBCdWZmZXJMaXN0LnByb3RvdHlwZS5wdXNoID0gZnVuY3Rpb24gcHVzaCh2KSB7XG4gICAgdmFyIGVudHJ5ID0geyBkYXRhOiB2LCBuZXh0OiBudWxsIH07XG4gICAgaWYgKHRoaXMubGVuZ3RoID4gMCkgdGhpcy50YWlsLm5leHQgPSBlbnRyeTtlbHNlIHRoaXMuaGVhZCA9IGVudHJ5O1xuICAgIHRoaXMudGFpbCA9IGVudHJ5O1xuICAgICsrdGhpcy5sZW5ndGg7XG4gIH07XG5cbiAgQnVmZmVyTGlzdC5wcm90b3R5cGUudW5zaGlmdCA9IGZ1bmN0aW9uIHVuc2hpZnQodikge1xuICAgIHZhciBlbnRyeSA9IHsgZGF0YTogdiwgbmV4dDogdGhpcy5oZWFkIH07XG4gICAgaWYgKHRoaXMubGVuZ3RoID09PSAwKSB0aGlzLnRhaWwgPSBlbnRyeTtcbiAgICB0aGlzLmhlYWQgPSBlbnRyeTtcbiAgICArK3RoaXMubGVuZ3RoO1xuICB9O1xuXG4gIEJ1ZmZlckxpc3QucHJvdG90eXBlLnNoaWZ0ID0gZnVuY3Rpb24gc2hpZnQoKSB7XG4gICAgaWYgKHRoaXMubGVuZ3RoID09PSAwKSByZXR1cm47XG4gICAgdmFyIHJldCA9IHRoaXMuaGVhZC5kYXRhO1xuICAgIGlmICh0aGlzLmxlbmd0aCA9PT0gMSkgdGhpcy5oZWFkID0gdGhpcy50YWlsID0gbnVsbDtlbHNlIHRoaXMuaGVhZCA9IHRoaXMuaGVhZC5uZXh0O1xuICAgIC0tdGhpcy5sZW5ndGg7XG4gICAgcmV0dXJuIHJldDtcbiAgfTtcblxuICBCdWZmZXJMaXN0LnByb3RvdHlwZS5jbGVhciA9IGZ1bmN0aW9uIGNsZWFyKCkge1xuICAgIHRoaXMuaGVhZCA9IHRoaXMudGFpbCA9IG51bGw7XG4gICAgdGhpcy5sZW5ndGggPSAwO1xuICB9O1xuXG4gIEJ1ZmZlckxpc3QucHJvdG90eXBlLmpvaW4gPSBmdW5jdGlvbiBqb2luKHMpIHtcbiAgICBpZiAodGhpcy5sZW5ndGggPT09IDApIHJldHVybiAnJztcbiAgICB2YXIgcCA9IHRoaXMuaGVhZDtcbiAgICB2YXIgcmV0ID0gJycgKyBwLmRhdGE7XG4gICAgd2hpbGUgKHAgPSBwLm5leHQpIHtcbiAgICAgIHJldCArPSBzICsgcC5kYXRhO1xuICAgIH1yZXR1cm4gcmV0O1xuICB9O1xuXG4gIEJ1ZmZlckxpc3QucHJvdG90eXBlLmNvbmNhdCA9IGZ1bmN0aW9uIGNvbmNhdChuKSB7XG4gICAgaWYgKHRoaXMubGVuZ3RoID09PSAwKSByZXR1cm4gQnVmZmVyLmFsbG9jKDApO1xuICAgIHZhciByZXQgPSBCdWZmZXIuYWxsb2NVbnNhZmUobiA+Pj4gMCk7XG4gICAgdmFyIHAgPSB0aGlzLmhlYWQ7XG4gICAgdmFyIGkgPSAwO1xuICAgIHdoaWxlIChwKSB7XG4gICAgICBjb3B5QnVmZmVyKHAuZGF0YSwgcmV0LCBpKTtcbiAgICAgIGkgKz0gcC5kYXRhLmxlbmd0aDtcbiAgICAgIHAgPSBwLm5leHQ7XG4gICAgfVxuICAgIHJldHVybiByZXQ7XG4gIH07XG5cbiAgcmV0dXJuIEJ1ZmZlckxpc3Q7XG59KCk7XG5cbmlmICh1dGlsICYmIHV0aWwuaW5zcGVjdCAmJiB1dGlsLmluc3BlY3QuY3VzdG9tKSB7XG4gIG1vZHVsZS5leHBvcnRzLnByb3RvdHlwZVt1dGlsLmluc3BlY3QuY3VzdG9tXSA9IGZ1bmN0aW9uICgpIHtcbiAgICB2YXIgb2JqID0gdXRpbC5pbnNwZWN0KHsgbGVuZ3RoOiB0aGlzLmxlbmd0aCB9KTtcbiAgICByZXR1cm4gdGhpcy5jb25zdHJ1Y3Rvci5uYW1lICsgJyAnICsgb2JqO1xuICB9O1xufSJdLCJuYW1lcyI6WyJfY2xhc3NDYWxsQ2hlY2siLCJpbnN0YW5jZSIsIkNvbnN0cnVjdG9yIiwiVHlwZUVycm9yIiwiQnVmZmVyIiwicmVxdWlyZSIsInV0aWwiLCJjb3B5QnVmZmVyIiwic3JjIiwidGFyZ2V0Iiwib2Zmc2V0IiwiY29weSIsIm1vZHVsZSIsImV4cG9ydHMiLCJCdWZmZXJMaXN0IiwiaGVhZCIsInRhaWwiLCJsZW5ndGgiLCJwcm90b3R5cGUiLCJwdXNoIiwidiIsImVudHJ5IiwiZGF0YSIsIm5leHQiLCJ1bnNoaWZ0Iiwic2hpZnQiLCJyZXQiLCJjbGVhciIsImpvaW4iLCJzIiwicCIsImNvbmNhdCIsIm4iLCJhbGxvYyIsImFsbG9jVW5zYWZlIiwiaSIsImluc3BlY3QiLCJjdXN0b20iLCJvYmoiLCJjb25zdHJ1Y3RvciIsIm5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/internal/streams/BufferList.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/internal/streams/destroy.js":
/*!**********************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/destroy.js ***!
  \**********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/*<replacement>*/ var pna = __webpack_require__(/*! process-nextick-args */ \"(ssr)/./node_modules/process-nextick-args/index.js\");\n/*</replacement>*/ // undocumented cb() API, needed for core, not for public API\nfunction destroy(err, cb) {\n    var _this = this;\n    var readableDestroyed = this._readableState && this._readableState.destroyed;\n    var writableDestroyed = this._writableState && this._writableState.destroyed;\n    if (readableDestroyed || writableDestroyed) {\n        if (cb) {\n            cb(err);\n        } else if (err) {\n            if (!this._writableState) {\n                pna.nextTick(emitErrorNT, this, err);\n            } else if (!this._writableState.errorEmitted) {\n                this._writableState.errorEmitted = true;\n                pna.nextTick(emitErrorNT, this, err);\n            }\n        }\n        return this;\n    }\n    // we set destroyed to true before firing error callbacks in order\n    // to make it re-entrance safe in case destroy() is called within callbacks\n    if (this._readableState) {\n        this._readableState.destroyed = true;\n    }\n    // if this is a duplex stream mark the writable part as destroyed as well\n    if (this._writableState) {\n        this._writableState.destroyed = true;\n    }\n    this._destroy(err || null, function(err) {\n        if (!cb && err) {\n            if (!_this._writableState) {\n                pna.nextTick(emitErrorNT, _this, err);\n            } else if (!_this._writableState.errorEmitted) {\n                _this._writableState.errorEmitted = true;\n                pna.nextTick(emitErrorNT, _this, err);\n            }\n        } else if (cb) {\n            cb(err);\n        }\n    });\n    return this;\n}\nfunction undestroy() {\n    if (this._readableState) {\n        this._readableState.destroyed = false;\n        this._readableState.reading = false;\n        this._readableState.ended = false;\n        this._readableState.endEmitted = false;\n    }\n    if (this._writableState) {\n        this._writableState.destroyed = false;\n        this._writableState.ended = false;\n        this._writableState.ending = false;\n        this._writableState.finalCalled = false;\n        this._writableState.prefinished = false;\n        this._writableState.finished = false;\n        this._writableState.errorEmitted = false;\n    }\n}\nfunction emitErrorNT(self, err) {\n    self.emit(\"error\", err);\n}\nmodule.exports = {\n    destroy: destroy,\n    undestroy: undestroy\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/internal/streams/destroy.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/lib/internal/streams/stream.js":
/*!*********************************************************************!*\
  !*** ./node_modules/readable-stream/lib/internal/streams/stream.js ***!
  \*********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nmodule.exports = __webpack_require__(/*! stream */ \"stream\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhZGFibGUtc3RyZWFtL2xpYi9pbnRlcm5hbC9zdHJlYW1zL3N0cmVhbS5qcyIsIm1hcHBpbmdzIjoiO0FBQUFBLDREQUF5QiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvcmVhZGFibGUtc3RyZWFtL2xpYi9pbnRlcm5hbC9zdHJlYW1zL3N0cmVhbS5qcz85NDlmIl0sInNvdXJjZXNDb250ZW50IjpbIm1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnc3RyZWFtJyk7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/lib/internal/streams/stream.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/node_modules/isarray/index.js":
/*!********************************************************************!*\
  !*** ./node_modules/readable-stream/node_modules/isarray/index.js ***!
  \********************************************************************/
/***/ ((module) => {

eval("\nvar toString = {}.toString;\nmodule.exports = Array.isArray || function(arr) {\n    return toString.call(arr) == \"[object Array]\";\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVhZGFibGUtc3RyZWFtL25vZGVfbW9kdWxlcy9pc2FycmF5L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7QUFBQSxJQUFJQSxXQUFXLENBQUMsRUFBRUEsUUFBUTtBQUUxQkMsT0FBT0MsT0FBTyxHQUFHQyxNQUFNQyxPQUFPLElBQUksU0FBVUMsR0FBRztJQUM3QyxPQUFPTCxTQUFTTSxJQUFJLENBQUNELFFBQVE7QUFDL0IiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3JlYWRhYmxlLXN0cmVhbS9ub2RlX21vZHVsZXMvaXNhcnJheS9pbmRleC5qcz9hMDMzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciB0b1N0cmluZyA9IHt9LnRvU3RyaW5nO1xuXG5tb2R1bGUuZXhwb3J0cyA9IEFycmF5LmlzQXJyYXkgfHwgZnVuY3Rpb24gKGFycikge1xuICByZXR1cm4gdG9TdHJpbmcuY2FsbChhcnIpID09ICdbb2JqZWN0IEFycmF5XSc7XG59O1xuIl0sIm5hbWVzIjpbInRvU3RyaW5nIiwibW9kdWxlIiwiZXhwb3J0cyIsIkFycmF5IiwiaXNBcnJheSIsImFyciIsImNhbGwiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/node_modules/isarray/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js":
/*!************************************************************************!*\
  !*** ./node_modules/readable-stream/node_modules/safe-buffer/index.js ***!
  \************************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("/* eslint-disable node/no-deprecated-api */ \nvar buffer = __webpack_require__(/*! buffer */ \"buffer\");\nvar Buffer = buffer.Buffer;\n// alternative to using Object.keys for old browsers\nfunction copyProps(src, dst) {\n    for(var key in src){\n        dst[key] = src[key];\n    }\n}\nif (Buffer.from && Buffer.alloc && Buffer.allocUnsafe && Buffer.allocUnsafeSlow) {\n    module.exports = buffer;\n} else {\n    // Copy properties from require('buffer')\n    copyProps(buffer, exports);\n    exports.Buffer = SafeBuffer;\n}\nfunction SafeBuffer(arg, encodingOrOffset, length) {\n    return Buffer(arg, encodingOrOffset, length);\n}\n// Copy static methods from Buffer\ncopyProps(Buffer, SafeBuffer);\nSafeBuffer.from = function(arg, encodingOrOffset, length) {\n    if (typeof arg === \"number\") {\n        throw new TypeError(\"Argument must not be a number\");\n    }\n    return Buffer(arg, encodingOrOffset, length);\n};\nSafeBuffer.alloc = function(size, fill, encoding) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    var buf = Buffer(size);\n    if (fill !== undefined) {\n        if (typeof encoding === \"string\") {\n            buf.fill(fill, encoding);\n        } else {\n            buf.fill(fill);\n        }\n    } else {\n        buf.fill(0);\n    }\n    return buf;\n};\nSafeBuffer.allocUnsafe = function(size) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    return Buffer(size);\n};\nSafeBuffer.allocUnsafeSlow = function(size) {\n    if (typeof size !== \"number\") {\n        throw new TypeError(\"Argument must be a number\");\n    }\n    return buffer.SlowBuffer(size);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/node_modules/string_decoder/lib/string_decoder.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/readable-stream/node_modules/string_decoder/lib/string_decoder.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("// Copyright Joyent, Inc. and other Node contributors.\n//\n// Permission is hereby granted, free of charge, to any person obtaining a\n// copy of this software and associated documentation files (the\n// \"Software\"), to deal in the Software without restriction, including\n// without limitation the rights to use, copy, modify, merge, publish,\n// distribute, sublicense, and/or sell copies of the Software, and to permit\n// persons to whom the Software is furnished to do so, subject to the\n// following conditions:\n//\n// The above copyright notice and this permission notice shall be included\n// in all copies or substantial portions of the Software.\n//\n// THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS\n// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF\n// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN\n// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,\n// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR\n// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE\n// USE OR OTHER DEALINGS IN THE SOFTWARE.\n\n/*<replacement>*/ var Buffer = (__webpack_require__(/*! safe-buffer */ \"(ssr)/./node_modules/readable-stream/node_modules/safe-buffer/index.js\").Buffer);\n/*</replacement>*/ var isEncoding = Buffer.isEncoding || function(encoding) {\n    encoding = \"\" + encoding;\n    switch(encoding && encoding.toLowerCase()){\n        case \"hex\":\n        case \"utf8\":\n        case \"utf-8\":\n        case \"ascii\":\n        case \"binary\":\n        case \"base64\":\n        case \"ucs2\":\n        case \"ucs-2\":\n        case \"utf16le\":\n        case \"utf-16le\":\n        case \"raw\":\n            return true;\n        default:\n            return false;\n    }\n};\nfunction _normalizeEncoding(enc) {\n    if (!enc) return \"utf8\";\n    var retried;\n    while(true){\n        switch(enc){\n            case \"utf8\":\n            case \"utf-8\":\n                return \"utf8\";\n            case \"ucs2\":\n            case \"ucs-2\":\n            case \"utf16le\":\n            case \"utf-16le\":\n                return \"utf16le\";\n            case \"latin1\":\n            case \"binary\":\n                return \"latin1\";\n            case \"base64\":\n            case \"ascii\":\n            case \"hex\":\n                return enc;\n            default:\n                if (retried) return; // undefined\n                enc = (\"\" + enc).toLowerCase();\n                retried = true;\n        }\n    }\n}\n;\n// Do not cache `Buffer.isEncoding` when checking encoding names as some\n// modules monkey-patch it to support additional encodings\nfunction normalizeEncoding(enc) {\n    var nenc = _normalizeEncoding(enc);\n    if (typeof nenc !== \"string\" && (Buffer.isEncoding === isEncoding || !isEncoding(enc))) throw new Error(\"Unknown encoding: \" + enc);\n    return nenc || enc;\n}\n// StringDecoder provides an interface for efficiently splitting a series of\n// buffers into a series of JS strings without breaking apart multi-byte\n// characters.\nexports.StringDecoder = StringDecoder;\nfunction StringDecoder(encoding) {\n    this.encoding = normalizeEncoding(encoding);\n    var nb;\n    switch(this.encoding){\n        case \"utf16le\":\n            this.text = utf16Text;\n            this.end = utf16End;\n            nb = 4;\n            break;\n        case \"utf8\":\n            this.fillLast = utf8FillLast;\n            nb = 4;\n            break;\n        case \"base64\":\n            this.text = base64Text;\n            this.end = base64End;\n            nb = 3;\n            break;\n        default:\n            this.write = simpleWrite;\n            this.end = simpleEnd;\n            return;\n    }\n    this.lastNeed = 0;\n    this.lastTotal = 0;\n    this.lastChar = Buffer.allocUnsafe(nb);\n}\nStringDecoder.prototype.write = function(buf) {\n    if (buf.length === 0) return \"\";\n    var r;\n    var i;\n    if (this.lastNeed) {\n        r = this.fillLast(buf);\n        if (r === undefined) return \"\";\n        i = this.lastNeed;\n        this.lastNeed = 0;\n    } else {\n        i = 0;\n    }\n    if (i < buf.length) return r ? r + this.text(buf, i) : this.text(buf, i);\n    return r || \"\";\n};\nStringDecoder.prototype.end = utf8End;\n// Returns only complete characters in a Buffer\nStringDecoder.prototype.text = utf8Text;\n// Attempts to complete a partial non-UTF-8 character using bytes from a Buffer\nStringDecoder.prototype.fillLast = function(buf) {\n    if (this.lastNeed <= buf.length) {\n        buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, this.lastNeed);\n        return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n    }\n    buf.copy(this.lastChar, this.lastTotal - this.lastNeed, 0, buf.length);\n    this.lastNeed -= buf.length;\n};\n// Checks the type of a UTF-8 byte, whether it's ASCII, a leading byte, or a\n// continuation byte. If an invalid byte is detected, -2 is returned.\nfunction utf8CheckByte(byte) {\n    if (byte <= 0x7F) return 0;\n    else if (byte >> 5 === 0x06) return 2;\n    else if (byte >> 4 === 0x0E) return 3;\n    else if (byte >> 3 === 0x1E) return 4;\n    return byte >> 6 === 0x02 ? -1 : -2;\n}\n// Checks at most 3 bytes at the end of a Buffer in order to detect an\n// incomplete multi-byte UTF-8 character. The total number of bytes (2, 3, or 4)\n// needed to complete the UTF-8 character (if applicable) are returned.\nfunction utf8CheckIncomplete(self, buf, i) {\n    var j = buf.length - 1;\n    if (j < i) return 0;\n    var nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) self.lastNeed = nb - 1;\n        return nb;\n    }\n    if (--j < i || nb === -2) return 0;\n    nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) self.lastNeed = nb - 2;\n        return nb;\n    }\n    if (--j < i || nb === -2) return 0;\n    nb = utf8CheckByte(buf[j]);\n    if (nb >= 0) {\n        if (nb > 0) {\n            if (nb === 2) nb = 0;\n            else self.lastNeed = nb - 3;\n        }\n        return nb;\n    }\n    return 0;\n}\n// Validates as many continuation bytes for a multi-byte UTF-8 character as\n// needed or are available. If we see a non-continuation byte where we expect\n// one, we \"replace\" the validated continuation bytes we've seen so far with\n// a single UTF-8 replacement character ('\\ufffd'), to match v8's UTF-8 decoding\n// behavior. The continuation byte check is included three times in the case\n// where all of the continuation bytes for a character exist in the same buffer.\n// It is also done this way as a slight performance increase instead of using a\n// loop.\nfunction utf8CheckExtraBytes(self, buf, p) {\n    if ((buf[0] & 0xC0) !== 0x80) {\n        self.lastNeed = 0;\n        return \"�\";\n    }\n    if (self.lastNeed > 1 && buf.length > 1) {\n        if ((buf[1] & 0xC0) !== 0x80) {\n            self.lastNeed = 1;\n            return \"�\";\n        }\n        if (self.lastNeed > 2 && buf.length > 2) {\n            if ((buf[2] & 0xC0) !== 0x80) {\n                self.lastNeed = 2;\n                return \"�\";\n            }\n        }\n    }\n}\n// Attempts to complete a multi-byte UTF-8 character using bytes from a Buffer.\nfunction utf8FillLast(buf) {\n    var p = this.lastTotal - this.lastNeed;\n    var r = utf8CheckExtraBytes(this, buf, p);\n    if (r !== undefined) return r;\n    if (this.lastNeed <= buf.length) {\n        buf.copy(this.lastChar, p, 0, this.lastNeed);\n        return this.lastChar.toString(this.encoding, 0, this.lastTotal);\n    }\n    buf.copy(this.lastChar, p, 0, buf.length);\n    this.lastNeed -= buf.length;\n}\n// Returns all complete UTF-8 characters in a Buffer. If the Buffer ended on a\n// partial character, the character's bytes are buffered until the required\n// number of bytes are available.\nfunction utf8Text(buf, i) {\n    var total = utf8CheckIncomplete(this, buf, i);\n    if (!this.lastNeed) return buf.toString(\"utf8\", i);\n    this.lastTotal = total;\n    var end = buf.length - (total - this.lastNeed);\n    buf.copy(this.lastChar, 0, end);\n    return buf.toString(\"utf8\", i, end);\n}\n// For UTF-8, a replacement character is added when ending on a partial\n// character.\nfunction utf8End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) return r + \"�\";\n    return r;\n}\n// UTF-16LE typically needs two bytes per character, but even if we have an even\n// number of bytes available, we need to check if we end on a leading/high\n// surrogate. In that case, we need to wait for the next two bytes in order to\n// decode the last character properly.\nfunction utf16Text(buf, i) {\n    if ((buf.length - i) % 2 === 0) {\n        var r = buf.toString(\"utf16le\", i);\n        if (r) {\n            var c = r.charCodeAt(r.length - 1);\n            if (c >= 0xD800 && c <= 0xDBFF) {\n                this.lastNeed = 2;\n                this.lastTotal = 4;\n                this.lastChar[0] = buf[buf.length - 2];\n                this.lastChar[1] = buf[buf.length - 1];\n                return r.slice(0, -1);\n            }\n        }\n        return r;\n    }\n    this.lastNeed = 1;\n    this.lastTotal = 2;\n    this.lastChar[0] = buf[buf.length - 1];\n    return buf.toString(\"utf16le\", i, buf.length - 1);\n}\n// For UTF-16LE we do not explicitly append special replacement characters if we\n// end on a partial character, we simply let v8 handle that.\nfunction utf16End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) {\n        var end = this.lastTotal - this.lastNeed;\n        return r + this.lastChar.toString(\"utf16le\", 0, end);\n    }\n    return r;\n}\nfunction base64Text(buf, i) {\n    var n = (buf.length - i) % 3;\n    if (n === 0) return buf.toString(\"base64\", i);\n    this.lastNeed = 3 - n;\n    this.lastTotal = 3;\n    if (n === 1) {\n        this.lastChar[0] = buf[buf.length - 1];\n    } else {\n        this.lastChar[0] = buf[buf.length - 2];\n        this.lastChar[1] = buf[buf.length - 1];\n    }\n    return buf.toString(\"base64\", i, buf.length - n);\n}\nfunction base64End(buf) {\n    var r = buf && buf.length ? this.write(buf) : \"\";\n    if (this.lastNeed) return r + this.lastChar.toString(\"base64\", 0, 3 - this.lastNeed);\n    return r;\n}\n// Pass bytes on through for single-byte encodings (e.g. ascii, latin1, hex)\nfunction simpleWrite(buf) {\n    return buf.toString(this.encoding);\n}\nfunction simpleEnd(buf) {\n    return buf && buf.length ? this.write(buf) : \"\";\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/node_modules/string_decoder/lib/string_decoder.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/readable-stream/readable.js":
/*!**************************************************!*\
  !*** ./node_modules/readable-stream/readable.js ***!
  \**************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval("\nvar Stream = __webpack_require__(/*! stream */ \"stream\");\nif (process.env.READABLE_STREAM === \"disable\" && Stream) {\n    module.exports = Stream;\n    exports = module.exports = Stream.Readable;\n    exports.Readable = Stream.Readable;\n    exports.Writable = Stream.Writable;\n    exports.Duplex = Stream.Duplex;\n    exports.Transform = Stream.Transform;\n    exports.PassThrough = Stream.PassThrough;\n    exports.Stream = Stream;\n} else {\n    exports = module.exports = __webpack_require__(/*! ./lib/_stream_readable.js */ \"(ssr)/./node_modules/readable-stream/lib/_stream_readable.js\");\n    exports.Stream = Stream || exports;\n    exports.Readable = exports;\n    exports.Writable = __webpack_require__(/*! ./lib/_stream_writable.js */ \"(ssr)/./node_modules/readable-stream/lib/_stream_writable.js\");\n    exports.Duplex = __webpack_require__(/*! ./lib/_stream_duplex.js */ \"(ssr)/./node_modules/readable-stream/lib/_stream_duplex.js\");\n    exports.Transform = __webpack_require__(/*! ./lib/_stream_transform.js */ \"(ssr)/./node_modules/readable-stream/lib/_stream_transform.js\");\n    exports.PassThrough = __webpack_require__(/*! ./lib/_stream_passthrough.js */ \"(ssr)/./node_modules/readable-stream/lib/_stream_passthrough.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/readable-stream/readable.js\n");

/***/ })

};
;