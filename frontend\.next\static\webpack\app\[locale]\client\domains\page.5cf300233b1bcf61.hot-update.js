"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/page",{

/***/ "(app-pages-browser)/./src/app/services/domainMngService.js":
/*!**********************************************!*\
  !*** ./src/app/services/domainMngService.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst domainMngService = {\n    // Customer management\n    customerSignup: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/customer-signup\", data, {\n            withCredentials: true\n        }),\n    // Domain operations\n    checkDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkIdnDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-idn-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkPremiumDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-premium-domain\", {\n            params,\n            withCredentials: true\n        }),\n    suggestDomainNames: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/suggest-names\", {\n            params,\n            withCredentials: true\n        }),\n    // Pricing information\n    getDNPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-dn-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Get reseller pricing\n    getResellerPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-reseller-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Add domain to cart\n    addDomainToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/add-domain-to-cart\", data, {\n            withCredentials: true\n        }),\n    // Add the new comprehensive search method\n    searchDomains: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/search-domains\", {\n            params,\n            withCredentials: true\n        }),\n    // Domain Management - User Domains\n    getUserDomains: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/order/get-domain-orders\", {\n            withCredentials: true\n        }),\n    // Get domain details\n    getDomainDetails: (domainName)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        }),\n    // Renew domain\n    renewDomain: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/renew-domain\", data, {\n            withCredentials: true\n        }),\n    // Get domain order ID\n    getDomainOrderId: (domainName)=>{\n        console.log(\"\\uD83D\\uDD27 Frontend service - getDomainOrderId called with:\", domainName);\n        console.log(\"\\uD83D\\uDD27 Frontend service - params object:\", {\n            domainName\n        });\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-order-id\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        });\n    },\n    // Get customer default nameservers\n    getCustomerDefaultNameservers: (customerId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/customer-default-nameservers\", {\n            params: customerId ? {\n                customerId\n            } : {},\n            withCredentials: true\n        }),\n    // Modify nameservers\n    modifyNameservers: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/modify-nameservers\", data, {\n            withCredentials: true\n        }),\n    // Enable privacy protection\n    enablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/enable-privacy\", data, {\n            withCredentials: true\n        }),\n    // Disable privacy protection\n    disablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/disable-privacy\", data, {\n            withCredentials: true\n        }),\n    getDomainById: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId), {\n            withCredentials: true\n        }),\n    // Domain Management - Nameservers\n    updateNameservers: (domainId, nameservers)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/nameservers\"), {\n            nameservers\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Auto Renewal\n    toggleAutoRenewal: (domainId, autoRenew)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/auto-renew\"), {\n            autoRenew\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Privacy Protection\n    togglePrivacyProtection: (domainId, privacyEnabled)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/privacy\"), {\n            privacyEnabled\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - DNS Records\n    getDnsRecords: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            withCredentials: true\n        }),\n    addDnsRecord: (domainId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    updateDnsRecord: (domainId, recordId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    deleteDnsRecord: (domainId, recordId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (domainMngService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/domainMngService.js\n"));

/***/ })

});