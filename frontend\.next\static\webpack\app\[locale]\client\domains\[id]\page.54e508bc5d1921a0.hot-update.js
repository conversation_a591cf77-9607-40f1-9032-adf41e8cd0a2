"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nameservers\");\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Domain name:\", domain.name);\n            console.log(\"\\uD83D\\uDD27 Domain name type:\", typeof domain.name);\n            console.log(\"\\uD83D\\uDD27 Getting domain order ID for:\", domain.name);\n            if (!domain.name) {\n                throw new Error(\"Domain name is missing from domain data\");\n            }\n            // First, get the real domain registration order ID from the API\n            const orderIdResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainOrderId(domain.name);\n            if (!orderIdResponse.data.success) {\n                throw new Error(\"Failed to get domain order ID\");\n            }\n            const realOrderId = orderIdResponse.data.orderId;\n            console.log(\"✅ Got real domain order ID:\", realOrderId);\n            // Check domain status before attempting to modify nameservers\n            console.log(\"\\uD83D\\uDD0D Checking domain status before modification...\");\n            try {\n                const domainDetailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainDetailsByName(domain.name, \"All\");\n                console.log(\"\\uD83D\\uDCCB Domain details:\", domainDetailsResponse.data);\n                if (domainDetailsResponse.data.success && domainDetailsResponse.data.domain) {\n                    const domainInfo = domainDetailsResponse.data.domain;\n                    console.log(\"\\uD83D\\uDCCA Current status:\", domainInfo.currentstatus);\n                    console.log(\"\\uD83D\\uDCCA Order status:\", domainInfo.orderstatus);\n                    console.log(\"\\uD83D\\uDCCA Domain status:\", domainInfo.domainstatus);\n                    console.log(\"\\uD83D\\uDCCA Order ID:\", domainInfo.orderid);\n                    // Check if domain is in a state that allows nameserver modification\n                    if (domainInfo.currentstatus && domainInfo.currentstatus !== \"Active\") {\n                        throw new Error(\"Domain is not active. Current status: \".concat(domainInfo.currentstatus, \". Please wait for the domain to become active before modifying nameservers.\"));\n                    }\n                    // Check if there are any order status locks that might prevent modification\n                    if (domainInfo.orderstatus && Array.isArray(domainInfo.orderstatus) && domainInfo.orderstatus.length > 0) {\n                        console.log(\"⚠️ Domain has order status locks:\", domainInfo.orderstatus);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Domain has status locks: \".concat(domainInfo.orderstatus.join(\", \"), \". Nameserver modification may fail.\"));\n                    }\n                }\n            } catch (statusError) {\n                console.warn(\"⚠️ Could not check domain status:\", statusError.message);\n            // Continue with nameserver modification even if status check fails\n            }\n            const updateData = {\n                orderId: realOrderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data_details, _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error updating nameservers:\", error);\n            // Provide specific error messages based on the error type\n            let errorMessage = \"Failed to update nameservers\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_details = _error_response_data.details) === null || _error_response_data_details === void 0 ? void 0 : _error_response_data_details.message) {\n                const apiError = error.response.data.details.message;\n                if (apiError.includes(\"Invalid Order status\")) {\n                    errorMessage = \"Cannot modify nameservers: Domain order is not in a valid status. The domain may be inactive, suspended, or locked.\";\n                } else {\n                    errorMessage = \"API Error: \".concat(apiError);\n                }\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.error) {\n                errorMessage = error.response.data.error;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 292,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"DNS Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 293,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 291,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage nameservers and child name servers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 298,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 304,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tabs, {\n                    value: activeTab,\n                    onChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                                    value: \"nameservers\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                lineNumber: 313,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Nameservers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Tab, {\n                                    value: \"child-nameservers\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Globe, {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Child Name Servers\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 318,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabsBody, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                                    value: \"nameservers\",\n                                    children: [\n                                        errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                                            color: \"red\",\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 330,\n                                                    columnNumber: 19\n                                                }, this),\n                                                errors.general\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3 mb-6\",\n                                            children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                                                    label: \"Nameserver \".concat(index + 1),\n                                                                    value: ns,\n                                                                    onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                                                    error: !!errors[index],\n                                                                    className: errors[index] ? \"border-red-500\" : \"\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 339,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                                    className: \"text-red-500 text-xs mt-1\",\n                                                                    children: errors[index]\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 349,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 338,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"text\",\n                                                            color: \"red\",\n                                                            size: \"sm\",\n                                                            onClick: ()=>removeNameserver(index),\n                                                            className: \"p-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 356,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 19\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2 mb-6\",\n                                            children: [\n                                                nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outlined\",\n                                                    size: \"sm\",\n                                                    onClick: addNameserver,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add Nameserver\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outlined\",\n                                                    size: \"sm\",\n                                                    onClick: resetToDefault,\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"Use Default\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 383,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 370,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                                            color: \"blue\",\n                                            className: \"mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                                            className: \"font-semibold\",\n                                                            children: \"Important Notes:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 397,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                            className: \"text-sm mt-1 space-y-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• At least 2 nameservers are required\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 401,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Maximum 13 nameservers allowed\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 402,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Changes may take 24-48 hours to propagate\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 403,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                                    children: \"• Incorrect nameservers may cause website downtime\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                                    lineNumber: 404,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 400,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                                                onClick: handleSave,\n                                                disabled: isUpdating,\n                                                children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 417,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Updating...\"\n                                                    ]\n                                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        \"Save Changes\"\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(TabPanel, {\n                                    value: \"child-nameservers\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ChildNameServerManager, {\n                                        domain: domain,\n                                        onUpdate: onUpdate\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 430,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 429,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 324,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 290,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 289,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"SfWsjn2wMbQu3s+nXySih6gHi8I=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});