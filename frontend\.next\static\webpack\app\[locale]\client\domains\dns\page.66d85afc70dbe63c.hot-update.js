"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Domain name:\", domain.name);\n            console.log(\"\\uD83D\\uDD27 Domain name type:\", typeof domain.name);\n            console.log(\"\\uD83D\\uDD27 Getting domain order ID for:\", domain.name);\n            if (!domain.name) {\n                throw new Error(\"Domain name is missing from domain data\");\n            }\n            // First, get the real domain registration order ID from the API\n            const orderIdResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainOrderId(domain.name);\n            if (!orderIdResponse.data.success) {\n                throw new Error(\"Failed to get domain order ID\");\n            }\n            const realOrderId = orderIdResponse.data.orderId;\n            console.log(\"✅ Got real domain order ID:\", realOrderId);\n            // Check domain status before attempting to modify nameservers\n            console.log(\"\\uD83D\\uDD0D Checking domain status before modification...\");\n            try {\n                const domainDetailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainDetailsByName(domain.name, \"All\");\n                console.log(\"\\uD83D\\uDCCB Domain details:\", domainDetailsResponse.data);\n                if (domainDetailsResponse.data.success && domainDetailsResponse.data.domain) {\n                    const domainInfo = domainDetailsResponse.data.domain;\n                    console.log(\"\\uD83D\\uDCCA Current status:\", domainInfo.currentstatus);\n                    console.log(\"\\uD83D\\uDCCA Order status:\", domainInfo.orderstatus);\n                    console.log(\"\\uD83D\\uDCCA Domain status:\", domainInfo.domainstatus);\n                    console.log(\"\\uD83D\\uDCCA Order ID:\", domainInfo.orderid);\n                    // Check if domain is in a state that allows nameserver modification\n                    if (domainInfo.currentstatus && domainInfo.currentstatus !== \"Active\") {\n                        throw new Error(\"Domain is not active. Current status: \".concat(domainInfo.currentstatus, \". Please wait for the domain to become active before modifying nameservers.\"));\n                    }\n                    // Check if there are any order status locks that might prevent modification\n                    if (domainInfo.orderstatus && Array.isArray(domainInfo.orderstatus) && domainInfo.orderstatus.length > 0) {\n                        console.log(\"⚠️ Domain has order status locks:\", domainInfo.orderstatus);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Domain has status locks: \".concat(domainInfo.orderstatus.join(\", \"), \". Nameserver modification may fail.\"));\n                    }\n                }\n            } catch (statusError) {\n                console.warn(\"⚠️ Could not check domain status:\", statusError.message);\n            // Continue with nameserver modification even if status check fails\n            }\n            const updateData = {\n                orderId: realOrderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data_details, _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error updating nameservers:\", error);\n            // Provide specific error messages based on the error type\n            let errorMessage = \"Failed to update nameservers\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_details = _error_response_data.details) === null || _error_response_data_details === void 0 ? void 0 : _error_response_data_details.message) {\n                const apiError = error.response.data.details.message;\n                if (apiError.includes(\"Invalid Order status\")) {\n                    errorMessage = \"Cannot modify nameservers: Domain order is not in a valid status. The domain may be inactive, suspended, or locked.\";\n                } else {\n                    errorMessage = \"API Error: \".concat(apiError);\n                }\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.error) {\n                errorMessage = error.response.data.error;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"Nameserver Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 304,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage the nameservers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 309,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"red\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 322,\n                            columnNumber: 13\n                        }, this),\n                        errors.general\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 321,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3 mb-6\",\n                    children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            label: \"Nameserver \".concat(index + 1),\n                                            value: ns,\n                                            onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                            error: !!errors[index],\n                                            className: errors[index] ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors[index]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 341,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 330,\n                                    columnNumber: 15\n                                }, this),\n                                nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    color: \"red\",\n                                    size: \"sm\",\n                                    onClick: ()=>removeNameserver(index),\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 329,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 327,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-6\",\n                    children: [\n                        nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: addNameserver,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Nameserver\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: resetToDefault,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 381,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Default\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 375,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 362,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"blue\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 387,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"font-semibold\",\n                                    children: \"Important Notes:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 389,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm mt-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• At least 2 nameservers are required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Maximum 13 nameservers allowed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 392,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Changes may take 24-48 hours to propagate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 393,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Incorrect nameservers may cause website downtime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 390,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 388,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 386,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                        onClick: handleSave,\n                        disabled: isUpdating,\n                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 407,\n                                    columnNumber: 17\n                                }, this),\n                                \"Updating...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 412,\n                                    columnNumber: 17\n                                }, this),\n                                \"Save Changes\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                        lineNumber: 400,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 399,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 301,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 300,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"0sR8sUYdtmYjlrPleVvRXk6Qprk=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});