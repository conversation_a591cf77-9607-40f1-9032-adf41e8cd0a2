const axios = require("axios");
const SubOrder = require("../models/SubOrder");
const Package = require("../models/Package");
const Contact = require("../models/Contact");
const User = require("../models/User");
const OrderStatus = require("../constants/enums/order-status");

// API configuration
const API_BASE_URL = process.env.API_BASE_URL_TEST;
const DOMAIN_CHECK_API_BASE_URL =
  process.env.API_BASE_URL_TEST + "/domaincheck";

const AUTH_PARAMS = {
  "auth-userid": process.env.AUTH_USERID_TEST,
  "api-key": process.env.API_KEY_TEST,
};

// Company customer ID for domain registrations (from environment)
const COMPANY_CUSTOMER_ID = process.env.COMPANY_CUSTOMER_ID || "31174676";

// Default nameservers (correct order from dashboard)
const DEFAULT_NAMESERVERS = [
  "moha1280036.earth.orderbox-dns.com",
  "moha1280036.mars.orderbox-dns.com",
  "moha1280036.mercury.orderbox-dns.com",
  "moha1280036.venus.orderbox-dns.com",
];

/**
 * Get user's domain contacts for registration
 * @param {String} userId - The user ID
 * @returns {Promise<Object>} - Object containing contact IDs
 */
async function getUserDomainContacts(userId) {
  try {
    console.log("🔄 Fetching user domain contacts for user:", userId);

    const user = await User.findById(userId).populate(
      "domainContacts.registrant domainContacts.admin domainContacts.tech domainContacts.billing"
    );

    if (!user) {
      throw new Error("User not found");
    }

    const contacts = {
      registrant: user.domainContacts.registrant?.externalContactId,
      admin: user.domainContacts.admin?.externalContactId,
      tech: user.domainContacts.tech?.externalContactId,
      billing: user.domainContacts.billing?.externalContactId,
    };

    console.log("📋 Retrieved user contacts:", contacts);

    // Check for missing contacts
    const missingContacts = [];
    Object.entries(contacts).forEach(([type, contactId]) => {
      if (!contactId) {
        missingContacts.push(type);
      }
    });

    if (missingContacts.length > 0) {
      console.warn("⚠️ Missing contact types:", missingContacts);
      throw new Error(
        `Missing required contacts: ${missingContacts.join(
          ", "
        )}. Please ensure all contact types are configured for the user.`
      );
    }

    return contacts;
  } catch (error) {
    console.error("❌ Error fetching user domain contacts:", error);
    throw error;
  }
}

/**
 * Process domain registration for an order
 * @param {Object} order - The order object with populated subOrders
 * @returns {Promise<Array>} - Array of registration results
 */
exports.processDomainRegistrations = async (order) => {
  try {
    console.log("🔄 Processing domain registrations for order:", order._id);

    if (!order.subOrders || order.subOrders.length === 0) {
      console.log("ℹ️ No suborders found in order");
      return [];
    }

    // Find domain suborders by checking if the package name contains a domain pattern
    const domainSubOrders = [];

    for (const subOrder of order.subOrders) {
      // Skip if package is not populated
      if (!subOrder.package) {
        console.log("⚠️ SubOrder has no package:", subOrder._id);
        continue;
      }

      // Check if this is a domain package by checking the reference
      if (
        subOrder.package.reference &&
        subOrder.package.reference.startsWith("domain-")
      ) {
        console.log("✅ Found domain suborder:", subOrder._id);
        domainSubOrders.push(subOrder);
      }
    }

    if (domainSubOrders.length === 0) {
      console.log("ℹ️ No domain suborders found in order");
      return [];
    }

    // Process each domain suborder
    const registrationResults = await Promise.all(
      domainSubOrders.map(async (subOrder) => {
        return await registerDomain(subOrder, order);
      })
    );

    return registrationResults;
  } catch (error) {
    console.error("❌ Error processing domain registrations:", error);
    throw error;
  }
};

/**
 * Register a domain for a specific suborder
 * @param {Object} subOrder - The suborder containing domain information
 * @param {Object} order - The parent order
 * @returns {Promise<Object>} - Registration result
 */
async function registerDomain(subOrder, order) {
  try {
    // Extract domain name from package name
    const domainName = subOrder.package.name;
    console.log("🔄 Registering domain:", domainName);

    // Get user's domain contacts
    const userContacts = await getUserDomainContacts(order.user._id);

    // Prepare registration parameters using user's contacts and company customer ID
    const params = {
      ...AUTH_PARAMS,
      "domain-name": domainName,
      years: subOrder.period || 1,
      "customer-id": COMPANY_CUSTOMER_ID,
      "reg-contact-id": userContacts.registrant,
      "admin-contact-id": userContacts.admin,
      "tech-contact-id": userContacts.tech,
      "billing-contact-id": userContacts.billing,
      "invoice-option": "KeepInvoice",
      "auto-renew": false, // Default to false for auto-renewal
      "purchase-privacy": true, // Enable privacy protection by default
      "protect-privacy": true,
    };

    console.log("🔧 Domain registration parameters:", {
      domain: domainName,
      customerId: COMPANY_CUSTOMER_ID,
      contacts: userContacts,
      nameservers: DEFAULT_NAMESERVERS,
    });

    // Create URLSearchParams for proper nameserver handling
    const searchParams = new URLSearchParams();

    // Add all parameters except nameservers
    for (const [key, value] of Object.entries(params)) {
      searchParams.append(key, value);
    }

    // Add each nameserver as a separate 'ns' parameter
    DEFAULT_NAMESERVERS.forEach((nameserver) => {
      searchParams.append("ns", nameserver);
    });

    console.log("🌐 Final API URL parameters:", searchParams.toString());

    // Call the domain registration API
    const response = await axios.post(
      `${API_BASE_URL}/domains/register.json?${searchParams.toString()}`,
      null
    );

    console.log("✅ Domain registration successful:", response.data);

    // Update suborder status to ACTIVE
    await SubOrder.findByIdAndUpdate(subOrder._id, {
      status: OrderStatus.ACTIVE,
    });

    return {
      subOrderId: subOrder._id,
      domainName,
      success: true,
      registrationData: response.data,
    };
  } catch (error) {
    console.error(
      "❌ Domain registration error:",
      error.response?.data || error.message
    );

    return {
      subOrderId: subOrder._id,
      domainName: subOrder.package.name,
      success: false,
      error: error.response?.data || error.message,
    };
  }
}
