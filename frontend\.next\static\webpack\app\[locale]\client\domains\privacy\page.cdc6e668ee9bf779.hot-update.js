"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/privacy/page",{

/***/ "(app-pages-browser)/./src/app/services/domainMngService.js":
/*!**********************************************!*\
  !*** ./src/app/services/domainMngService.js ***!
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _lib_apiService__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../lib/apiService */ \"(app-pages-browser)/./src/app/lib/apiService.js\");\n\nconst domainMngService = {\n    // Customer management\n    customerSignup: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/customer-signup\", data, {\n            withCredentials: true\n        }),\n    // Domain operations\n    checkDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkIdnDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-idn-domain\", {\n            params,\n            withCredentials: true\n        }),\n    checkPremiumDomainAvailability: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/check-premium-domain\", {\n            params,\n            withCredentials: true\n        }),\n    suggestDomainNames: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/suggest-names\", {\n            params,\n            withCredentials: true\n        }),\n    // Pricing information\n    getDNPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-dn-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Get reseller pricing\n    getResellerPricing: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/get-reseller-pricing\", {\n            params,\n            withCredentials: true\n        }),\n    // Add domain to cart\n    addDomainToCart: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/add-domain-to-cart\", data, {\n            withCredentials: true\n        }),\n    // Add the new comprehensive search method\n    searchDomains: (params)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/search-domains\", {\n            params,\n            withCredentials: true\n        }),\n    // Domain Management - User Domains\n    getUserDomains: ()=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/order/get-domain-orders\", {\n            withCredentials: true\n        }),\n    // Get domain details\n    getDomainDetails: (domainName)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        }),\n    // Get domain details by name (from registration system)\n    getDomainDetailsByName: function(domainName) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : \"OrderDetails\";\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-details-by-name\", {\n            params: {\n                domainName,\n                options\n            },\n            withCredentials: true\n        });\n    },\n    // Renew domain\n    renewDomain: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/renew-domain\", data, {\n            withCredentials: true\n        }),\n    // Get domain order ID\n    getDomainOrderId: (domainName)=>{\n        console.log(\"\\uD83D\\uDD27 Frontend service - getDomainOrderId called with:\", domainName);\n        console.log(\"\\uD83D\\uDD27 Frontend service - params object:\", {\n            domainName\n        });\n        return _lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domain-order-id\", {\n            params: {\n                domainName\n            },\n            withCredentials: true\n        });\n    },\n    // Get customer default nameservers\n    getCustomerDefaultNameservers: (customerId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/customer-default-nameservers\", {\n            params: customerId ? {\n                customerId\n            } : {},\n            withCredentials: true\n        }),\n    // Modify nameservers\n    modifyNameservers: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/modify-nameservers\", data, {\n            withCredentials: true\n        }),\n    // Enable privacy protection\n    enablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/enable-privacy\", data, {\n            withCredentials: true\n        }),\n    // Disable privacy protection\n    disablePrivacyProtection: (data)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/disable-privacy\", data, {\n            withCredentials: true\n        }),\n    getDomainById: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId), {\n            withCredentials: true\n        }),\n    // Domain Management - Nameservers\n    updateNameservers: (domainId, nameservers)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/nameservers\"), {\n            nameservers\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Auto Renewal\n    toggleAutoRenewal: (domainId, autoRenew)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/auto-renew\"), {\n            autoRenew\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - Privacy Protection\n    togglePrivacyProtection: (domainId, privacyEnabled)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/privacy\"), {\n            privacyEnabled\n        }, {\n            withCredentials: true\n        }),\n    // Domain Management - DNS Records\n    getDnsRecords: (domainId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            withCredentials: true\n        }),\n    addDnsRecord: (domainId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].post(\"/domainMng/domains/\".concat(domainId, \"/dns\"), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    updateDnsRecord: (domainId, recordId, record)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].put(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            record\n        }, {\n            withCredentials: true\n        }),\n    deleteDnsRecord: (domainId, recordId)=>_lib_apiService__WEBPACK_IMPORTED_MODULE_0__[\"default\"].delete(\"/domainMng/domains/\".concat(domainId, \"/dns/\").concat(recordId), {\n            withCredentials: true\n        })\n};\n/* harmony default export */ __webpack_exports__[\"default\"] = (domainMngService);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/services/domainMngService.js\n"));

/***/ })

});