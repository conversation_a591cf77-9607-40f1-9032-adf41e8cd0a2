"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/page",{

/***/ "(app-pages-browser)/./src/components/home/<USER>":
/*!**********************************************!*\
  !*** ./src/components/home/<USER>
  \**********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Check,Search,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nconst DomainSearch = (param)=>{\n    let { t } = param;\n    _s();\n    const [domain, setDomain] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [searchResults, setSearchResults] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedPeriods, setSelectedPeriods] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({}); // State to hold selected periods for each domain\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const handleSearch = async (e)=>{\n        e.preventDefault();\n        const trimmedDomain = domain.trim();\n        console.log(\"Searching for domain:\", domain);\n        if (!trimmedDomain) {\n            setError(t ? t(\"domain.error_no_domain\") : \"Please enter a domain name\");\n            setSearchResults(null); // Clear previous results\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSearchResults(null); // Clear previous results before new search\n        setSelectedPeriods({}); // Clear selected periods on new search\n        try {\n            // Call the new backend endpoint that handles all logic\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].searchDomains({\n                params: trimmedDomain\n            });\n            const backendAvailable = response.data.available || [];\n            const backendUnavailable = response.data.unavailable || [];\n            const backendSuggestions = response.data.suggestions || [];\n            const backendPremium = response.data.premium || [];\n            let primaryPremiumDomain = null;\n            // Separate the primary searched domain if it's in the premium list\n            backendPremium.forEach((item)=>{\n                if (item.name.toLowerCase() === trimmedDomain.toLowerCase()) {\n                    primaryPremiumDomain = {\n                        name: item.name,\n                        pricing: {\n                            register: item.price,\n                            period: 1,\n                            currency: item.currency || \"MAD\"\n                        },\n                        isPremium: true,\n                        isAvailable: true\n                    };\n                }\n            });\n            // Construct the final results object\n            const processedResults = {\n                available: [\n                    // Add standard available domains\n                    ...backendAvailable.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        })),\n                    // Add the primary premium domain if found and available\n                    ...primaryPremiumDomain ? [\n                        primaryPremiumDomain\n                    ] : []\n                ],\n                unavailable: backendUnavailable,\n                suggestions: [\n                    // Add standard suggestions\n                    ...backendSuggestions.map((item)=>({\n                            ...item,\n                            pricing: item.pricing || {\n                                register: {},\n                                renewal: {},\n                                period: 1,\n                                currency: \"MAD\"\n                            }\n                        }))\n                ]\n            };\n            console.log(\"Processed results from backend:\", processedResults);\n            setSearchResults(processedResults);\n            // Initialize selectedPeriods state with the first available period for each domain\n            const initialPeriods = {};\n            processedResults.available.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            processedResults.suggestions.forEach((item)=>{\n                var _item_pricing;\n                // Find the first available period key, default to '1' if none found or register is empty\n                const firstPeriod = Object.keys(((_item_pricing = item.pricing) === null || _item_pricing === void 0 ? void 0 : _item_pricing.register) || {})[0] || \"1\";\n                initialPeriods[item.name] = parseInt(firstPeriod);\n            });\n            setSelectedPeriods(initialPeriods);\n        } catch (err) {\n            var _err_response_data, _err_response;\n            console.error(\"Domain search error:\", err);\n            const errorMessage = ((_err_response = err.response) === null || _err_response === void 0 ? void 0 : (_err_response_data = _err_response.data) === null || _err_response_data === void 0 ? void 0 : _err_response_data.error) || err.message || (t ? t(\"domain.error_search\") : \"Failed to search for domain\");\n            setError(errorMessage);\n            setSearchResults(null); // Clear results on error\n            setSelectedPeriods({}); // Clear selected periods on error\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleAddToCart = async function(domainName, tld, price) {\n        let period = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 1;\n        try {\n            setAddingToCart(true);\n            console.log(\"Adding domain to cart:\", {\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_2__[\"default\"].addDomainToCart({\n                domain: domainName,\n                tld: tld,\n                price: price,\n                period: period\n            });\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.success(t ? t(\"domain.added_to_cart\") : \"Domain added to cart\");\n            router.push(\"/client/cart\");\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error adding domain to cart:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_3__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || (t ? t(\"domain.error_adding_to_cart\") : \"Failed to add domain to cart\"));\n        } finally{\n            setAddingToCart(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-5xl mx-auto -mt-14 relative z-20\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white rounded-lg shadow-md border-gray-50 p-6 mx-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                    onSubmit: handleSearch,\n                    className: \"flex flex-col md:flex-row gap-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-5 w-5 text-blue-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 164,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                    type: \"text\",\n                                    value: domain,\n                                    onChange: (e)=>setDomain(e.target.value.toLowerCase()),\n                                    placeholder: t ? t(\"domain.search_placeholder\") : \"Enter your domain name (e.g., example.com or just example)\",\n                                    className: \"pl-10 w-full py-3 rounded-md\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 167,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 163,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-tertiary hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-md transition-colors\",\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"animate-spin -ml-1 mr-2 h-4 w-4 text-white\",\n                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                        fill: \"none\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                                className: \"opacity-25\",\n                                                cx: \"12\",\n                                                cy: \"12\",\n                                                r: \"10\",\n                                                stroke: \"currentColor\",\n                                                strokeWidth: \"4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 193,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                className: \"opacity-75\",\n                                                fill: \"currentColor\",\n                                                d: \"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 17\n                                    }, undefined),\n                                    t ? t(\"domain.searching\") : \"Searching...\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                lineNumber: 186,\n                                columnNumber: 15\n                            }, undefined) : t ? t(\"domain.search_button\") : \"Search\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 180,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 159,\n                    columnNumber: 9\n                }, undefined),\n                error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 text-red-600\",\n                    children: error\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 217,\n                    columnNumber: 19\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                    children: searchResults && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                        initial: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        animate: {\n                            opacity: 1,\n                            height: \"auto\"\n                        },\n                        exit: {\n                            opacity: 0,\n                            height: 0\n                        },\n                        transition: {\n                            duration: 0.3\n                        },\n                        className: \"mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-200 pt-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                    className: \"font-medium text-lg mb-3\",\n                                    children: t ? t(\"domain.search_results\") : \"Search Results\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 229,\n                                    columnNumber: 17\n                                }, undefined),\n                                searchResults.available.map((domainItem, index)=>{\n                                    var _this, _domainItem_pricing_register, _domainItem_pricing_renewal_;\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border rounded-md mb-2 \".concat(domainItem.isPremium ? \"border-yellow-400 bg-yellow-50\" // Highlight premium domains\n                                         : \"border-green-200 bg-green-50\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-700 mr-2 font-semibold text-sm\",\n                                                        children: \"PREMIUM\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 245,\n                                                        columnNumber: 25\n                                                    }, undefined) // Add Premium label\n                                                     : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 249,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            domainItem.pricing && domainItem.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex flex-col items-end mr-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"font-medium \".concat(domainItem.isPremium ? \"text-yellow-800\" : \"text-green-700\"),\n                                                                children: [\n                                                                    !domainItem.isPremium ? ((_this = (domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0) * (selectedPeriods[domainItem.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\" : ((_domainItem_pricing_register = domainItem.pricing.register) === null || _domainItem_pricing_register === void 0 ? void 0 : _domainItem_pricing_register.toFixed(2)) || \"\",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 257,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            !domainItem.isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-xs text-gray-500\",\n                                                                children: [\n                                                                    t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                    \":\",\n                                                                    \" \",\n                                                                    ((_domainItem_pricing_renewal_ = domainItem.pricing.renewal[selectedPeriods[domainItem.name] || 1]) === null || _domainItem_pricing_renewal_ === void 0 ? void 0 : _domainItem_pricing_renewal_.toFixed(2)) || \"\",\n                                                                    \" \",\n                                                                    domainItem.pricing.currency || \"MAD\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 29\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 25\n                                                    }, undefined),\n                                                    domainItem.isPremium ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-2 p-1 text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            \"1 \",\n                                                            t ? t(\"domain.year\") : \"Year\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 27\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                        className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                        value: selectedPeriods[domainItem.name] || 1,\n                                                        onChange: (e)=>setSelectedPeriods({\n                                                                ...selectedPeriods,\n                                                                [domainItem.name]: parseInt(e.target.value)\n                                                            }),\n                                                        children: Object.keys(domainItem.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                value: parseInt(period),\n                                                                children: [\n                                                                    period,\n                                                                    \" \",\n                                                                    t ? t(\"domain.years\") : \"Year(s)\"\n                                                                ]\n                                                            }, period, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 33\n                                                            }, undefined))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 27\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2 \".concat(domainItem.isPremium ? \"bg-yellow-600 hover:bg-yellow-700\" // Premium button style\n                                                         : \"bg-green-600 hover:bg-green-700\"),\n                                                        onClick: ()=>{\n                                                            const parts = domainItem.name.split(\".\");\n                                                            const domainName = parts[0];\n                                                            const tld = parts.slice(1).join(\".\"); // Handles multi-part TLDs\n                                                            // Pass the price for the selected period\n                                                            // Calculate the total price for the selected period\n                                                            const pricePerYear = domainItem.pricing.register[selectedPeriods[domainItem.name] || 1] || 0;\n                                                            const selectedPeriod = selectedPeriods[domainItem.name] || 1;\n                                                            const totalPrice = pricePerYear * selectedPeriod;\n                                                            handleAddToCart(domainName, tld, totalPrice, selectedPeriod // Pass selected period\n                                                            );\n                                                        },\n                                                        disabled: addingToCart,\n                                                        children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 342,\n                                                            columnNumber: 29\n                                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 346,\n                                                            columnNumber: 29\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 25\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-500 mr-3\",\n                                                children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, \"available-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 19\n                                    }, undefined);\n                                }),\n                                searchResults.unavailable.map((domainItem, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-3 border border-gray-300 bg-gray-50 rounded-md mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-5 w-5 text-red-500 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 369,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: domainItem.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 368,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-gray-600\",\n                                                children: domainItem.reason\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                lineNumber: 372,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, \"unavailable-\".concat(index), true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 19\n                                    }, undefined)),\n                                searchResults.suggestions && searchResults.suggestions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-medium text-lg mb-3\",\n                                            children: t ? t(\"domain.suggestions\") : \"Suggestions\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 380,\n                                            columnNumber: 23\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 gap-2\",\n                                            children: searchResults.suggestions.map((suggestion, index)=>{\n                                                var _this, _suggestion_pricing_renewal_;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between p-3 border border-blue-200 bg-blue-50 rounded-md mb-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_Search_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                                    className: \"h-5 w-5 text-blue-500 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 390,\n                                                                    columnNumber: 31\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: suggestion.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 31\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 389,\n                                                            columnNumber: 29\n                                                        }, undefined),\n                                                        suggestion.pricing && suggestion.pricing.register !== null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-col items-end mr-3\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"font-medium text-blue-700\",\n                                                                            children: [\n                                                                                ((_this = (suggestion.pricing.register[selectedPeriods[suggestion.name] || 1] || 0) * (selectedPeriods[suggestion.name] || 1)) === null || _this === void 0 ? void 0 : _this.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 399,\n                                                                            columnNumber: 35\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"text-xs text-gray-500\",\n                                                                            children: [\n                                                                                t ? t(\"domain.renewal\") : \"Renewal\",\n                                                                                \":\",\n                                                                                \" \",\n                                                                                ((_suggestion_pricing_renewal_ = suggestion.pricing.renewal[selectedPeriods[suggestion.name] || 1]) === null || _suggestion_pricing_renewal_ === void 0 ? void 0 : _suggestion_pricing_renewal_.toFixed(2)) || \"\",\n                                                                                \" \",\n                                                                                suggestion.pricing.currency || \"MAD\"\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 409,\n                                                                            columnNumber: 35\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 398,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                    className: \"ml-2 p-1 border rounded-md text-sm\",\n                                                                    value: selectedPeriods[suggestion.name] || 1,\n                                                                    onChange: (e)=>setSelectedPeriods({\n                                                                            ...selectedPeriods,\n                                                                            [suggestion.name]: parseInt(e.target.value)\n                                                                        }),\n                                                                    children: Object.keys(suggestion.pricing.register).map((period)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                            value: parseInt(period),\n                                                                            children: [\n                                                                                period,\n                                                                                \" \",\n                                                                                t ? t(\"domain.years\") : \"Year(s)\"\n                                                                            ]\n                                                                        }, period, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 39\n                                                                        }, undefined))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 419,\n                                                                    columnNumber: 33\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    className: \"bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded-md text-sm whitespace-nowrap ml-2\",\n                                                                    onClick: ()=>handleAddToCart(suggestion.name.split(\".\")[0], suggestion.name.split(\".\").slice(1).join(\".\"), // Pass the price for the selected period\n                                                                        suggestion.pricing.register[selectedPeriods[suggestion.name] || 1], selectedPeriods[suggestion.name] || 1 // Pass selected period\n                                                                        ),\n                                                                    disabled: addingToCart,\n                                                                    children: addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.adding_to_cart\") : \"Adding...\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 463,\n                                                                        columnNumber: 37\n                                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        children: t ? t(\"domain.add_to_cart\") : \"Add to Cart\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                        lineNumber: 469,\n                                                                        columnNumber: 37\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 33\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-500 mr-3\",\n                                                            children: t ? t(\"domain.pricing_unavailable\") : \"Pricing unavailable\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                            lineNumber: 478,\n                                                            columnNumber: 31\n                                                        }, undefined)\n                                                    ]\n                                                }, \"suggestion-\".concat(index), true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                                    lineNumber: 385,\n                                                    columnNumber: 27\n                                                }, undefined);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                            lineNumber: 383,\n                                            columnNumber: 23\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 21\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                            lineNumber: 228,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                        lineNumber: 221,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n            lineNumber: 158,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\home\\\\domainSearch.jsx\",\n        lineNumber: 157,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DomainSearch, \"CnbqHtnQ24xX9c2xLqgEGfP4p/w=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = DomainSearch;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DomainSearch);\nvar _c;\n$RefreshReg$(_c, \"DomainSearch\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/home/<USER>"));

/***/ })

});