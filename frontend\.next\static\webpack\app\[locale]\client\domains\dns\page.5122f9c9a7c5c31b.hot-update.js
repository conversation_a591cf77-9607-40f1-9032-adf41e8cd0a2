"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/dns/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Globe,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* harmony import */ var _ChildNameServerManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ChildNameServerManager */ \"(app-pages-browser)/./src/components/domains/ChildNameServerManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"nameservers\");\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Domain name:\", domain.name);\n            console.log(\"\\uD83D\\uDD27 Domain name type:\", typeof domain.name);\n            console.log(\"\\uD83D\\uDD27 Getting domain order ID for:\", domain.name);\n            if (!domain.name) {\n                throw new Error(\"Domain name is missing from domain data\");\n            }\n            // First, get the real domain registration order ID from the API\n            const orderIdResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainOrderId(domain.name);\n            if (!orderIdResponse.data.success) {\n                throw new Error(\"Failed to get domain order ID\");\n            }\n            const realOrderId = orderIdResponse.data.orderId;\n            console.log(\"✅ Got real domain order ID:\", realOrderId);\n            // Check domain status before attempting to modify nameservers\n            console.log(\"\\uD83D\\uDD0D Checking domain status before modification...\");\n            try {\n                const domainDetailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainDetailsByName(domain.name, \"All\");\n                console.log(\"\\uD83D\\uDCCB Domain details:\", domainDetailsResponse.data);\n                if (domainDetailsResponse.data.success && domainDetailsResponse.data.domain) {\n                    const domainInfo = domainDetailsResponse.data.domain;\n                    console.log(\"\\uD83D\\uDCCA Current status:\", domainInfo.currentstatus);\n                    console.log(\"\\uD83D\\uDCCA Order status:\", domainInfo.orderstatus);\n                    console.log(\"\\uD83D\\uDCCA Domain status:\", domainInfo.domainstatus);\n                    console.log(\"\\uD83D\\uDCCA Order ID:\", domainInfo.orderid);\n                    // Check if domain is in a state that allows nameserver modification\n                    if (domainInfo.currentstatus && domainInfo.currentstatus !== \"Active\") {\n                        throw new Error(\"Domain is not active. Current status: \".concat(domainInfo.currentstatus, \". Please wait for the domain to become active before modifying nameservers.\"));\n                    }\n                    // Check if there are any order status locks that might prevent modification\n                    if (domainInfo.orderstatus && Array.isArray(domainInfo.orderstatus) && domainInfo.orderstatus.length > 0) {\n                        console.log(\"⚠️ Domain has order status locks:\", domainInfo.orderstatus);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Domain has status locks: \".concat(domainInfo.orderstatus.join(\", \"), \". Nameserver modification may fail.\"));\n                    }\n                }\n            } catch (statusError) {\n                console.warn(\"⚠️ Could not check domain status:\", statusError.message);\n            // Continue with nameserver modification even if status check fails\n            }\n            const updateData = {\n                orderId: realOrderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data_details, _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error(\"Error updating nameservers:\", error);\n            // Provide specific error messages based on the error type\n            let errorMessage = \"Failed to update nameservers\";\n            if ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : (_error_response_data_details = _error_response_data.details) === null || _error_response_data_details === void 0 ? void 0 : _error_response_data_details.message) {\n                const apiError = error.response.data.details.message;\n                if (apiError.includes(\"Invalid Order status\")) {\n                    errorMessage = \"Cannot modify nameservers: Domain order is not in a valid status. The domain may be inactive, suspended, or locked.\";\n                } else {\n                    errorMessage = \"API Error: \".concat(apiError);\n                }\n            } else if ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.error) {\n                errorMessage = error.response.data.error;\n            } else if (error.message) {\n                errorMessage = error.message;\n            }\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(errorMessage);\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 306,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"Nameserver Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 307,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 305,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage the nameservers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"red\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 325,\n                            columnNumber: 13\n                        }, this),\n                        errors.general\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 324,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3 mb-6\",\n                    children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            label: \"Nameserver \".concat(index + 1),\n                                            value: ns,\n                                            onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                            error: !!errors[index],\n                                            className: errors[index] ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors[index]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 344,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    color: \"red\",\n                                    size: \"sm\",\n                                    onClick: ()=>removeNameserver(index),\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 358,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 330,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-6\",\n                    children: [\n                        nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: addNameserver,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 373,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Nameserver\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 367,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: resetToDefault,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 384,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Default\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 378,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 365,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"blue\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 390,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"font-semibold\",\n                                    children: \"Important Notes:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm mt-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• At least 2 nameservers are required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Maximum 13 nameservers allowed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 395,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Changes may take 24-48 hours to propagate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 396,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Incorrect nameservers may cause website downtime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 397,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 391,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 389,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                        onClick: handleSave,\n                        disabled: isUpdating,\n                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 410,\n                                    columnNumber: 17\n                                }, this),\n                                \"Updating...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Globe_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 415,\n                                    columnNumber: 17\n                                }, this),\n                                \"Save Changes\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                        lineNumber: 403,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 402,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 304,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 303,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"SfWsjn2wMbQu3s+nXySih6gHi8I=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvTmFtZXNlcnZlck1hbmFnZXIuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDaUM7QUFhQztBQVNaO0FBQ3lDO0FBQ3hCO0FBQ3VCO0FBRS9DLFNBQVNzQixrQkFBa0IsS0FBb0I7UUFBcEIsRUFBRUMsTUFBTSxFQUFFQyxRQUFRLEVBQUUsR0FBcEI7O0lBQ3hDLE1BQU0sQ0FBQ0MsYUFBYUMsZUFBZSxHQUFHMUIsK0NBQVFBLENBQzVDdUIsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFRRSxXQUFXLEtBQUk7UUFBQztRQUFJO0tBQUc7SUFFakMsTUFBTSxDQUFDRSxZQUFZQyxjQUFjLEdBQUc1QiwrQ0FBUUEsQ0FBQztJQUM3QyxNQUFNLENBQUM2QixRQUFRQyxVQUFVLEdBQUc5QiwrQ0FBUUEsQ0FBQyxDQUFDO0lBQ3RDLE1BQU0sQ0FBQytCLFdBQVdDLGFBQWEsR0FBR2hDLCtDQUFRQSxDQUFDO0lBRTNDLE1BQU1pQyxxQkFBcUIsQ0FBQ0M7UUFDMUIsSUFBSSxDQUFDQSxHQUFHQyxJQUFJLElBQUksT0FBTztRQUV2QiwwQkFBMEI7UUFDMUIsTUFBTUMsY0FDSjtRQUNGLElBQUksQ0FBQ0EsWUFBWUMsSUFBSSxDQUFDSCxHQUFHQyxJQUFJLEtBQUs7WUFDaEMsT0FBTztRQUNUO1FBRUEsT0FBTztJQUNUO0lBRUEsTUFBTUcseUJBQXlCO1FBQzdCLE1BQU1DLFlBQVksQ0FBQztRQUNuQixNQUFNQyxtQkFBbUJmLFlBQVlnQixNQUFNLENBQUMsQ0FBQ1AsS0FBT0EsR0FBR0MsSUFBSTtRQUUzRCxJQUFJSyxpQkFBaUJFLE1BQU0sR0FBRyxHQUFHO1lBQy9CSCxVQUFVSSxPQUFPLEdBQUc7UUFDdEI7UUFFQSxJQUFJSCxpQkFBaUJFLE1BQU0sR0FBRyxJQUFJO1lBQ2hDSCxVQUFVSSxPQUFPLEdBQUc7UUFDdEI7UUFFQWxCLFlBQVltQixPQUFPLENBQUMsQ0FBQ1YsSUFBSVc7WUFDdkIsSUFBSVgsR0FBR0MsSUFBSSxJQUFJO2dCQUNiLE1BQU1XLFFBQVFiLG1CQUFtQkM7Z0JBQ2pDLElBQUlZLE9BQU87b0JBQ1RQLFNBQVMsQ0FBQ00sTUFBTSxHQUFHQztnQkFDckI7WUFDRjtRQUNGO1FBRUEsdUJBQXVCO1FBQ3ZCLE1BQU1DLFlBQVlQLGlCQUFpQlEsR0FBRyxDQUFDLENBQUNkLEtBQU9BLEdBQUdDLElBQUksR0FBR2MsV0FBVztRQUNwRSxNQUFNQyxhQUFhSCxVQUFVTixNQUFNLENBQ2pDLENBQUNQLElBQUlXLFFBQVVFLFVBQVVJLE9BQU8sQ0FBQ2pCLFFBQVFXO1FBRTNDLElBQUlLLFdBQVdSLE1BQU0sR0FBRyxHQUFHO1lBQ3pCSCxVQUFVSSxPQUFPLEdBQUc7UUFDdEI7UUFFQWIsVUFBVVM7UUFDVixPQUFPYSxPQUFPQyxJQUFJLENBQUNkLFdBQVdHLE1BQU0sS0FBSztJQUMzQztJQUVBLE1BQU1ZLHlCQUF5QixDQUFDVCxPQUFPVTtRQUNyQyxNQUFNQyxpQkFBaUI7ZUFBSS9CO1NBQVk7UUFDdkMrQixjQUFjLENBQUNYLE1BQU0sR0FBR1U7UUFDeEI3QixlQUFlOEI7UUFFZiw2QkFBNkI7UUFDN0IsSUFBSTNCLE1BQU0sQ0FBQ2dCLE1BQU0sRUFBRTtZQUNqQixNQUFNTixZQUFZO2dCQUFFLEdBQUdWLE1BQU07WUFBQztZQUM5QixPQUFPVSxTQUFTLENBQUNNLE1BQU07WUFDdkJmLFVBQVVTO1FBQ1o7SUFDRjtJQUVBLE1BQU1rQixnQkFBZ0I7UUFDcEIsSUFBSWhDLFlBQVlpQixNQUFNLEdBQUcsSUFBSTtZQUMzQmhCLGVBQWU7bUJBQUlEO2dCQUFhO2FBQUc7UUFDckM7SUFDRjtJQUVBLE1BQU1pQyxtQkFBbUIsQ0FBQ2I7UUFDeEIsSUFBSXBCLFlBQVlpQixNQUFNLEdBQUcsR0FBRztZQUMxQixNQUFNYyxpQkFBaUIvQixZQUFZZ0IsTUFBTSxDQUFDLENBQUNrQixHQUFHQyxJQUFNQSxNQUFNZjtZQUMxRG5CLGVBQWU4QjtZQUVmLDZCQUE2QjtZQUM3QixJQUFJM0IsTUFBTSxDQUFDZ0IsTUFBTSxFQUFFO2dCQUNqQixNQUFNTixZQUFZO29CQUFFLEdBQUdWLE1BQU07Z0JBQUM7Z0JBQzlCLE9BQU9VLFNBQVMsQ0FBQ00sTUFBTTtnQkFDdkJmLFVBQVVTO1lBQ1o7UUFDRjtJQUNGO0lBRUEsTUFBTXNCLGFBQWE7UUFDakIsSUFBSSxDQUFDdkIsMEJBQTBCO1lBQzdCO1FBQ0Y7UUFFQSxJQUFJO1lBQ0ZWLGNBQWM7WUFFZCxNQUFNWSxtQkFBbUJmLFlBQ3RCZ0IsTUFBTSxDQUFDLENBQUNQLEtBQU9BLEdBQUdDLElBQUksSUFDdEJhLEdBQUcsQ0FBQyxDQUFDZCxLQUFPQSxHQUFHQyxJQUFJO1lBRXRCMkIsUUFBUUMsR0FBRyxDQUFDLDZCQUFtQnhDO1lBQy9CdUMsUUFBUUMsR0FBRyxDQUFDLDZCQUFtQnhDLE9BQU95QyxJQUFJO1lBQzFDRixRQUFRQyxHQUFHLENBQUMsa0NBQXdCLE9BQU94QyxPQUFPeUMsSUFBSTtZQUN0REYsUUFBUUMsR0FBRyxDQUFDLDZDQUFtQ3hDLE9BQU95QyxJQUFJO1lBRTFELElBQUksQ0FBQ3pDLE9BQU95QyxJQUFJLEVBQUU7Z0JBQ2hCLE1BQU0sSUFBSUMsTUFBTTtZQUNsQjtZQUVBLGdFQUFnRTtZQUNoRSxNQUFNQyxrQkFBa0IsTUFBTS9DLHNFQUFnQkEsQ0FBQ2dELGdCQUFnQixDQUM3RDVDLE9BQU95QyxJQUFJO1lBR2IsSUFBSSxDQUFDRSxnQkFBZ0JFLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUNqQyxNQUFNLElBQUlKLE1BQU07WUFDbEI7WUFFQSxNQUFNSyxjQUFjSixnQkFBZ0JFLElBQUksQ0FBQ0csT0FBTztZQUNoRFQsUUFBUUMsR0FBRyxDQUFDLCtCQUErQk87WUFFM0MsOERBQThEO1lBQzlEUixRQUFRQyxHQUFHLENBQUM7WUFDWixJQUFJO2dCQUNGLE1BQU1TLHdCQUNKLE1BQU1yRCxzRUFBZ0JBLENBQUNzRCxzQkFBc0IsQ0FBQ2xELE9BQU95QyxJQUFJLEVBQUU7Z0JBQzdERixRQUFRQyxHQUFHLENBQUMsZ0NBQXNCUyxzQkFBc0JKLElBQUk7Z0JBRTVELElBQ0VJLHNCQUFzQkosSUFBSSxDQUFDQyxPQUFPLElBQ2xDRyxzQkFBc0JKLElBQUksQ0FBQzdDLE1BQU0sRUFDakM7b0JBQ0EsTUFBTW1ELGFBQWFGLHNCQUFzQkosSUFBSSxDQUFDN0MsTUFBTTtvQkFDcER1QyxRQUFRQyxHQUFHLENBQUMsZ0NBQXNCVyxXQUFXQyxhQUFhO29CQUMxRGIsUUFBUUMsR0FBRyxDQUFDLDhCQUFvQlcsV0FBV0UsV0FBVztvQkFDdERkLFFBQVFDLEdBQUcsQ0FBQywrQkFBcUJXLFdBQVdHLFlBQVk7b0JBQ3hEZixRQUFRQyxHQUFHLENBQUMsMEJBQWdCVyxXQUFXSSxPQUFPO29CQUU5QyxvRUFBb0U7b0JBQ3BFLElBQ0VKLFdBQVdDLGFBQWEsSUFDeEJELFdBQVdDLGFBQWEsS0FBSyxVQUM3Qjt3QkFDQSxNQUFNLElBQUlWLE1BQ1IseUNBQWtFLE9BQXpCUyxXQUFXQyxhQUFhLEVBQUM7b0JBRXRFO29CQUVBLDRFQUE0RTtvQkFDNUUsSUFDRUQsV0FBV0UsV0FBVyxJQUN0QkcsTUFBTUMsT0FBTyxDQUFDTixXQUFXRSxXQUFXLEtBQ3BDRixXQUFXRSxXQUFXLENBQUNsQyxNQUFNLEdBQUcsR0FDaEM7d0JBQ0FvQixRQUFRQyxHQUFHLENBQ1QscUNBQ0FXLFdBQVdFLFdBQVc7d0JBRXhCeEQsaURBQUtBLENBQUM2RCxPQUFPLENBQ1gsNEJBRUUsT0FGMEJQLFdBQVdFLFdBQVcsQ0FBQ00sSUFBSSxDQUNyRCxPQUNBO29CQUVOO2dCQUNGO1lBQ0YsRUFBRSxPQUFPQyxhQUFhO2dCQUNwQnJCLFFBQVFzQixJQUFJLENBQUMscUNBQXFDRCxZQUFZRSxPQUFPO1lBQ3JFLG1FQUFtRTtZQUNyRTtZQUVBLE1BQU1DLGFBQWE7Z0JBQ2pCZixTQUFTRDtnQkFDVDdDLGFBQWFlO1lBQ2Y7WUFFQXNCLFFBQVFDLEdBQUcsQ0FBQyxnREFBc0N1QjtZQUVsRCxNQUFNQyxXQUFXLE1BQU1wRSxzRUFBZ0JBLENBQUNxRSxpQkFBaUIsQ0FBQ0Y7WUFFMUQsSUFBSUMsU0FBU25CLElBQUksQ0FBQ0MsT0FBTyxFQUFFO2dCQUN6QmpELGlEQUFLQSxDQUFDaUQsT0FBTyxDQUFDO2dCQUVkLHFDQUFxQztnQkFDckMsSUFBSTdDLFVBQVU7b0JBQ1pBLFNBQVM7d0JBQ1AsR0FBR0QsTUFBTTt3QkFDVEUsYUFBYWU7b0JBQ2Y7Z0JBQ0Y7WUFDRixPQUFPO2dCQUNMLE1BQU0sSUFBSXlCLE1BQU1zQixTQUFTbkIsSUFBSSxDQUFDdEIsS0FBSyxJQUFJO1lBQ3pDO1FBQ0YsRUFBRSxPQUFPQSxPQUFPO2dCQU1WQSw4QkFBQUEsc0JBQUFBLGlCQVFPQSx1QkFBQUE7WUFiWGdCLFFBQVFoQixLQUFLLENBQUMsK0JBQStCQTtZQUU3QywwREFBMEQ7WUFDMUQsSUFBSTJDLGVBQWU7WUFFbkIsS0FBSTNDLGtCQUFBQSxNQUFNeUMsUUFBUSxjQUFkekMsdUNBQUFBLHVCQUFBQSxnQkFBZ0JzQixJQUFJLGNBQXBCdEIsNENBQUFBLCtCQUFBQSxxQkFBc0I0QyxPQUFPLGNBQTdCNUMsbURBQUFBLDZCQUErQnVDLE9BQU8sRUFBRTtnQkFDMUMsTUFBTU0sV0FBVzdDLE1BQU15QyxRQUFRLENBQUNuQixJQUFJLENBQUNzQixPQUFPLENBQUNMLE9BQU87Z0JBQ3BELElBQUlNLFNBQVNDLFFBQVEsQ0FBQyx5QkFBeUI7b0JBQzdDSCxlQUNFO2dCQUNKLE9BQU87b0JBQ0xBLGVBQWUsY0FBdUIsT0FBVEU7Z0JBQy9CO1lBQ0YsT0FBTyxLQUFJN0MsbUJBQUFBLE1BQU15QyxRQUFRLGNBQWR6Qyx3Q0FBQUEsd0JBQUFBLGlCQUFnQnNCLElBQUksY0FBcEJ0Qiw0Q0FBQUEsc0JBQXNCQSxLQUFLLEVBQUU7Z0JBQ3RDMkMsZUFBZTNDLE1BQU15QyxRQUFRLENBQUNuQixJQUFJLENBQUN0QixLQUFLO1lBQzFDLE9BQU8sSUFBSUEsTUFBTXVDLE9BQU8sRUFBRTtnQkFDeEJJLGVBQWUzQyxNQUFNdUMsT0FBTztZQUM5QjtZQUVBakUsaURBQUtBLENBQUMwQixLQUFLLENBQUMyQztRQUNkLFNBQVU7WUFDUjdELGNBQWM7UUFDaEI7SUFDRjtJQUVBLE1BQU1pRSxpQkFBaUI7UUFDckIsSUFBSTtZQUNGL0IsUUFBUUMsR0FBRyxDQUFDO1lBRVosNENBQTRDO1lBQzVDLE1BQU13QixXQUFXLE1BQU1wRSxzRUFBZ0JBLENBQUMyRSw2QkFBNkI7WUFFckVoQyxRQUFRQyxHQUFHLENBQUMsbUNBQXlCd0IsU0FBU25CLElBQUk7WUFFbEQsSUFBSW1CLFNBQVNuQixJQUFJLENBQUNDLE9BQU8sSUFBSWtCLFNBQVNuQixJQUFJLENBQUMzQyxXQUFXLEVBQUU7Z0JBQ3RELElBQUlzRSxxQkFBcUJSLFNBQVNuQixJQUFJLENBQUMzQyxXQUFXO2dCQUVsRCxvQ0FBb0M7Z0JBQ3BDLElBQUlzRCxNQUFNQyxPQUFPLENBQUNlLHFCQUFxQjtvQkFDckNqQyxRQUFRQyxHQUFHLENBQ1QsMENBQ0FnQztnQkFFSixPQUFPO29CQUNMakMsUUFBUXNCLElBQUksQ0FDVixnREFDQVc7b0JBRUYsTUFBTSxJQUFJOUIsTUFBTTtnQkFDbEI7Z0JBRUF2QyxlQUFlcUU7Z0JBQ2ZqRSxVQUFVLENBQUM7Z0JBRVhWLGlEQUFLQSxDQUFDaUQsT0FBTyxDQUFDO1lBQ2hCLE9BQU87Z0JBQ0xQLFFBQVFzQixJQUFJLENBQ1YsbURBQ0FHLFNBQVNuQixJQUFJO2dCQUVmLE1BQU0sSUFBSUgsTUFBTTtZQUNsQjtRQUNGLEVBQUUsT0FBT25CLE9BQU87WUFDZGdCLFFBQVFoQixLQUFLLENBQUMsd0NBQXdDQTtZQUV0RCwyQ0FBMkM7WUFDM0MsTUFBTWtELHNCQUFzQjtnQkFDMUI7Z0JBQ0E7Z0JBQ0E7Z0JBQ0E7YUFDRDtZQUVEdEUsZUFBZXNFO1lBQ2ZsRSxVQUFVLENBQUM7WUFFWFYsaURBQUtBLENBQUM2RCxPQUFPLENBQUM7UUFDaEI7SUFDRjtJQUVBLHFCQUNFLDhEQUFDL0UsMERBQUlBO2tCQUNILDRFQUFDQyw4REFBUUE7OzhCQUNQLDhEQUFDOEY7b0JBQUlDLFdBQVU7O3NDQUNiLDhEQUFDdEYsaUlBQU1BOzRCQUFDc0YsV0FBVTs7Ozs7O3NDQUNsQiw4REFBQ2pHLGdFQUFVQTs0QkFBQ2tHLFNBQVE7NEJBQUtELFdBQVU7c0NBQWdCOzs7Ozs7Ozs7Ozs7OEJBS3JELDhEQUFDakcsZ0VBQVVBO29CQUFDaUcsV0FBVTs7d0JBQXFCO3dCQUNiM0UsbUJBQUFBLDZCQUFBQSxPQUFReUMsSUFBSTt3QkFBQzs7Ozs7OztnQkFJMUN6QyxDQUFBQSxtQkFBQUEsNkJBQUFBLE9BQVFnRCxPQUFPLG1CQUNkLDhEQUFDdEUsZ0VBQVVBO29CQUFDaUcsV0FBVTs7d0JBQTZCO3dCQUN0QzNFLE9BQU9nRCxPQUFPOzs7Ozs7O2dCQUk1QjFDLE9BQU9jLE9BQU8sa0JBQ2IsOERBQUNyQywyREFBS0E7b0JBQUM4RixPQUFNO29CQUFNRixXQUFVOztzQ0FDM0IsOERBQUNsRixpSUFBV0E7NEJBQUNrRixXQUFVOzs7Ozs7d0JBQ3RCckUsT0FBT2MsT0FBTzs7Ozs7Ozs4QkFJbkIsOERBQUNzRDtvQkFBSUMsV0FBVTs4QkFDWnpFLFlBQVl1QixHQUFHLENBQUMsQ0FBQ2QsSUFBSVcsc0JBQ3BCLDhEQUFDb0Q7NEJBQWdCQyxXQUFVOzs4Q0FDekIsOERBQUNEO29DQUFJQyxXQUFVOztzREFDYiw4REFBQzdGLDJEQUFLQTs0Q0FDSmdHLE9BQU8sY0FBd0IsT0FBVnhELFFBQVE7NENBQzdCVSxPQUFPckI7NENBQ1BvRSxVQUFVLENBQUNDLElBQ1RqRCx1QkFBdUJULE9BQU8wRCxFQUFFQyxNQUFNLENBQUNqRCxLQUFLOzRDQUU5Q1QsT0FBTyxDQUFDLENBQUNqQixNQUFNLENBQUNnQixNQUFNOzRDQUN0QnFELFdBQVdyRSxNQUFNLENBQUNnQixNQUFNLEdBQUcsbUJBQW1COzs7Ozs7d0NBRS9DaEIsTUFBTSxDQUFDZ0IsTUFBTSxrQkFDWiw4REFBQzVDLGdFQUFVQTs0Q0FBQ2lHLFdBQVU7c0RBQ25CckUsTUFBTSxDQUFDZ0IsTUFBTTs7Ozs7Ozs7Ozs7O2dDQUtuQnBCLFlBQVlpQixNQUFNLEdBQUcsbUJBQ3BCLDhEQUFDdEMsNERBQU1BO29DQUNMK0YsU0FBUTtvQ0FDUkMsT0FBTTtvQ0FDTkssTUFBSztvQ0FDTEMsU0FBUyxJQUFNaEQsaUJBQWlCYjtvQ0FDaENxRCxXQUFVOzhDQUVWLDRFQUFDcEYsaUlBQU1BO3dDQUFDb0YsV0FBVTs7Ozs7Ozs7Ozs7OzJCQTFCZHJEOzs7Ozs7Ozs7OzhCQWlDZCw4REFBQ29EO29CQUFJQyxXQUFVOzt3QkFDWnpFLFlBQVlpQixNQUFNLEdBQUcsb0JBQ3BCLDhEQUFDdEMsNERBQU1BOzRCQUNMK0YsU0FBUTs0QkFDUk0sTUFBSzs0QkFDTEMsU0FBU2pEOzRCQUNUeUMsV0FBVTs7OENBRVYsOERBQUNyRixpSUFBSUE7b0NBQUNxRixXQUFVOzs7Ozs7Z0NBQVk7Ozs7Ozs7c0NBS2hDLDhEQUFDOUYsNERBQU1BOzRCQUNMK0YsU0FBUTs0QkFDUk0sTUFBSzs0QkFDTEMsU0FBU2I7NEJBQ1RLLFdBQVU7OzhDQUVWLDhEQUFDdEYsaUlBQU1BO29DQUFDc0YsV0FBVTs7Ozs7O2dDQUFZOzs7Ozs7Ozs7Ozs7OzhCQUtsQyw4REFBQzVGLDJEQUFLQTtvQkFBQzhGLE9BQU07b0JBQU9GLFdBQVU7O3NDQUM1Qiw4REFBQ2xGLGlJQUFXQTs0QkFBQ2tGLFdBQVU7Ozs7OztzQ0FDdkIsOERBQUNEOzs4Q0FDQyw4REFBQ2hHLGdFQUFVQTtvQ0FBQ2lHLFdBQVU7OENBQWdCOzs7Ozs7OENBQ3RDLDhEQUFDUztvQ0FBR1QsV0FBVTs7c0RBQ1osOERBQUNVO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNBO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNBO3NEQUFHOzs7Ozs7c0RBQ0osOERBQUNBO3NEQUFHOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBS1YsOERBQUNYO29CQUFJQyxXQUFVOzhCQUNiLDRFQUFDOUYsNERBQU1BO3dCQUNMOEYsV0FBVTt3QkFDVlEsU0FBUzdDO3dCQUNUZ0QsVUFBVWxGO2tDQUVUQSwyQkFDQzs7OENBQ0UsOERBQUNzRTtvQ0FBSUMsV0FBVTs7Ozs7O2dDQUFrRTs7eURBSW5GOzs4Q0FDRSw4REFBQ25GLGtJQUFJQTtvQ0FBQ21GLFdBQVU7Ozs7OztnQ0FBWTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUzVDO0dBM1l3QjVFO0tBQUFBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9jb21wb25lbnRzL2RvbWFpbnMvTmFtZXNlcnZlck1hbmFnZXIuanN4PzIzYzgiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XG5pbXBvcnQgeyB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHtcbiAgVHlwb2dyYXBoeSxcbiAgQ2FyZCxcbiAgQ2FyZEJvZHksXG4gIEJ1dHRvbixcbiAgSW5wdXQsXG4gIEFsZXJ0LFxuICBUYWJzLFxuICBUYWJzSGVhZGVyLFxuICBUYWJzQm9keSxcbiAgVGFiLFxuICBUYWJQYW5lbCxcbn0gZnJvbSBcIkBtYXRlcmlhbC10YWlsd2luZC9yZWFjdFwiO1xuaW1wb3J0IHtcbiAgU2VydmVyLFxuICBQbHVzLFxuICBUcmFzaDIsXG4gIFNhdmUsXG4gIEFsZXJ0Q2lyY2xlLFxuICBDaGVja0NpcmNsZSxcbiAgR2xvYmUsXG59IGZyb20gXCJsdWNpZGUtcmVhY3RcIjtcbmltcG9ydCBkb21haW5NbmdTZXJ2aWNlIGZyb20gXCJAL2FwcC9zZXJ2aWNlcy9kb21haW5NbmdTZXJ2aWNlXCI7XG5pbXBvcnQgeyB0b2FzdCB9IGZyb20gXCJyZWFjdC10b2FzdGlmeVwiO1xuaW1wb3J0IENoaWxkTmFtZVNlcnZlck1hbmFnZXIgZnJvbSBcIi4vQ2hpbGROYW1lU2VydmVyTWFuYWdlclwiO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBOYW1lc2VydmVyTWFuYWdlcih7IGRvbWFpbiwgb25VcGRhdGUgfSkge1xuICBjb25zdCBbbmFtZXNlcnZlcnMsIHNldE5hbWVzZXJ2ZXJzXSA9IHVzZVN0YXRlKFxuICAgIGRvbWFpbj8ubmFtZXNlcnZlcnMgfHwgW1wiXCIsIFwiXCJdXG4gICk7XG4gIGNvbnN0IFtpc1VwZGF0aW5nLCBzZXRJc1VwZGF0aW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW2Vycm9ycywgc2V0RXJyb3JzXSA9IHVzZVN0YXRlKHt9KTtcbiAgY29uc3QgW2FjdGl2ZVRhYiwgc2V0QWN0aXZlVGFiXSA9IHVzZVN0YXRlKFwibmFtZXNlcnZlcnNcIik7XG5cbiAgY29uc3QgdmFsaWRhdGVOYW1lc2VydmVyID0gKG5zKSA9PiB7XG4gICAgaWYgKCFucy50cmltKCkpIHJldHVybiBcIk5hbWVzZXJ2ZXIgY2Fubm90IGJlIGVtcHR5XCI7XG5cbiAgICAvLyBCYXNpYyBkb21haW4gdmFsaWRhdGlvblxuICAgIGNvbnN0IGRvbWFpblJlZ2V4ID1cbiAgICAgIC9eW2EtekEtWjAtOV0oW2EtekEtWjAtOS1dezAsNjF9W2EtekEtWjAtOV0pPyhcXC5bYS16QS1aMC05XShbYS16QS1aMC05LV17MCw2MX1bYS16QS1aMC05XSk/KSokLztcbiAgICBpZiAoIWRvbWFpblJlZ2V4LnRlc3QobnMudHJpbSgpKSkge1xuICAgICAgcmV0dXJuIFwiSW52YWxpZCBuYW1lc2VydmVyIGZvcm1hdFwiO1xuICAgIH1cblxuICAgIHJldHVybiBudWxsO1xuICB9O1xuXG4gIGNvbnN0IHZhbGlkYXRlQWxsTmFtZXNlcnZlcnMgPSAoKSA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzID0ge307XG4gICAgY29uc3QgdmFsaWROYW1lc2VydmVycyA9IG5hbWVzZXJ2ZXJzLmZpbHRlcigobnMpID0+IG5zLnRyaW0oKSk7XG5cbiAgICBpZiAodmFsaWROYW1lc2VydmVycy5sZW5ndGggPCAyKSB7XG4gICAgICBuZXdFcnJvcnMuZ2VuZXJhbCA9IFwiQXQgbGVhc3QgMiBuYW1lc2VydmVycyBhcmUgcmVxdWlyZWRcIjtcbiAgICB9XG5cbiAgICBpZiAodmFsaWROYW1lc2VydmVycy5sZW5ndGggPiAxMykge1xuICAgICAgbmV3RXJyb3JzLmdlbmVyYWwgPSBcIk1heGltdW0gMTMgbmFtZXNlcnZlcnMgYWxsb3dlZFwiO1xuICAgIH1cblxuICAgIG5hbWVzZXJ2ZXJzLmZvckVhY2goKG5zLCBpbmRleCkgPT4ge1xuICAgICAgaWYgKG5zLnRyaW0oKSkge1xuICAgICAgICBjb25zdCBlcnJvciA9IHZhbGlkYXRlTmFtZXNlcnZlcihucyk7XG4gICAgICAgIGlmIChlcnJvcikge1xuICAgICAgICAgIG5ld0Vycm9yc1tpbmRleF0gPSBlcnJvcjtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH0pO1xuXG4gICAgLy8gQ2hlY2sgZm9yIGR1cGxpY2F0ZXNcbiAgICBjb25zdCB0cmltbWVkTnMgPSB2YWxpZE5hbWVzZXJ2ZXJzLm1hcCgobnMpID0+IG5zLnRyaW0oKS50b0xvd2VyQ2FzZSgpKTtcbiAgICBjb25zdCBkdXBsaWNhdGVzID0gdHJpbW1lZE5zLmZpbHRlcihcbiAgICAgIChucywgaW5kZXgpID0+IHRyaW1tZWROcy5pbmRleE9mKG5zKSAhPT0gaW5kZXhcbiAgICApO1xuICAgIGlmIChkdXBsaWNhdGVzLmxlbmd0aCA+IDApIHtcbiAgICAgIG5ld0Vycm9ycy5nZW5lcmFsID0gXCJEdXBsaWNhdGUgbmFtZXNlcnZlcnMgYXJlIG5vdCBhbGxvd2VkXCI7XG4gICAgfVxuXG4gICAgc2V0RXJyb3JzKG5ld0Vycm9ycyk7XG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwO1xuICB9O1xuXG4gIGNvbnN0IGhhbmRsZU5hbWVzZXJ2ZXJDaGFuZ2UgPSAoaW5kZXgsIHZhbHVlKSA9PiB7XG4gICAgY29uc3QgbmV3TmFtZXNlcnZlcnMgPSBbLi4ubmFtZXNlcnZlcnNdO1xuICAgIG5ld05hbWVzZXJ2ZXJzW2luZGV4XSA9IHZhbHVlO1xuICAgIHNldE5hbWVzZXJ2ZXJzKG5ld05hbWVzZXJ2ZXJzKTtcblxuICAgIC8vIENsZWFyIGVycm9yIGZvciB0aGlzIGZpZWxkXG4gICAgaWYgKGVycm9yc1tpbmRleF0pIHtcbiAgICAgIGNvbnN0IG5ld0Vycm9ycyA9IHsgLi4uZXJyb3JzIH07XG4gICAgICBkZWxldGUgbmV3RXJyb3JzW2luZGV4XTtcbiAgICAgIHNldEVycm9ycyhuZXdFcnJvcnMpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBhZGROYW1lc2VydmVyID0gKCkgPT4ge1xuICAgIGlmIChuYW1lc2VydmVycy5sZW5ndGggPCAxMykge1xuICAgICAgc2V0TmFtZXNlcnZlcnMoWy4uLm5hbWVzZXJ2ZXJzLCBcIlwiXSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IHJlbW92ZU5hbWVzZXJ2ZXIgPSAoaW5kZXgpID0+IHtcbiAgICBpZiAobmFtZXNlcnZlcnMubGVuZ3RoID4gMikge1xuICAgICAgY29uc3QgbmV3TmFtZXNlcnZlcnMgPSBuYW1lc2VydmVycy5maWx0ZXIoKF8sIGkpID0+IGkgIT09IGluZGV4KTtcbiAgICAgIHNldE5hbWVzZXJ2ZXJzKG5ld05hbWVzZXJ2ZXJzKTtcblxuICAgICAgLy8gQ2xlYXIgZXJyb3IgZm9yIHRoaXMgZmllbGRcbiAgICAgIGlmIChlcnJvcnNbaW5kZXhdKSB7XG4gICAgICAgIGNvbnN0IG5ld0Vycm9ycyA9IHsgLi4uZXJyb3JzIH07XG4gICAgICAgIGRlbGV0ZSBuZXdFcnJvcnNbaW5kZXhdO1xuICAgICAgICBzZXRFcnJvcnMobmV3RXJyb3JzKTtcbiAgICAgIH1cbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlU2F2ZSA9IGFzeW5jICgpID0+IHtcbiAgICBpZiAoIXZhbGlkYXRlQWxsTmFtZXNlcnZlcnMoKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBzZXRJc1VwZGF0aW5nKHRydWUpO1xuXG4gICAgICBjb25zdCB2YWxpZE5hbWVzZXJ2ZXJzID0gbmFtZXNlcnZlcnNcbiAgICAgICAgLmZpbHRlcigobnMpID0+IG5zLnRyaW0oKSlcbiAgICAgICAgLm1hcCgobnMpID0+IG5zLnRyaW0oKSk7XG5cbiAgICAgIGNvbnNvbGUubG9nKFwi8J+UpyBEb21haW4gZGF0YTpcIiwgZG9tYWluKTtcbiAgICAgIGNvbnNvbGUubG9nKFwi8J+UpyBEb21haW4gbmFtZTpcIiwgZG9tYWluLm5hbWUpO1xuICAgICAgY29uc29sZS5sb2coXCLwn5SnIERvbWFpbiBuYW1lIHR5cGU6XCIsIHR5cGVvZiBkb21haW4ubmFtZSk7XG4gICAgICBjb25zb2xlLmxvZyhcIvCflKcgR2V0dGluZyBkb21haW4gb3JkZXIgSUQgZm9yOlwiLCBkb21haW4ubmFtZSk7XG5cbiAgICAgIGlmICghZG9tYWluLm5hbWUpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRG9tYWluIG5hbWUgaXMgbWlzc2luZyBmcm9tIGRvbWFpbiBkYXRhXCIpO1xuICAgICAgfVxuXG4gICAgICAvLyBGaXJzdCwgZ2V0IHRoZSByZWFsIGRvbWFpbiByZWdpc3RyYXRpb24gb3JkZXIgSUQgZnJvbSB0aGUgQVBJXG4gICAgICBjb25zdCBvcmRlcklkUmVzcG9uc2UgPSBhd2FpdCBkb21haW5NbmdTZXJ2aWNlLmdldERvbWFpbk9yZGVySWQoXG4gICAgICAgIGRvbWFpbi5uYW1lXG4gICAgICApO1xuXG4gICAgICBpZiAoIW9yZGVySWRSZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGdldCBkb21haW4gb3JkZXIgSURcIik7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHJlYWxPcmRlcklkID0gb3JkZXJJZFJlc3BvbnNlLmRhdGEub3JkZXJJZDtcbiAgICAgIGNvbnNvbGUubG9nKFwi4pyFIEdvdCByZWFsIGRvbWFpbiBvcmRlciBJRDpcIiwgcmVhbE9yZGVySWQpO1xuXG4gICAgICAvLyBDaGVjayBkb21haW4gc3RhdHVzIGJlZm9yZSBhdHRlbXB0aW5nIHRvIG1vZGlmeSBuYW1lc2VydmVyc1xuICAgICAgY29uc29sZS5sb2coXCLwn5SNIENoZWNraW5nIGRvbWFpbiBzdGF0dXMgYmVmb3JlIG1vZGlmaWNhdGlvbi4uLlwiKTtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNvbnN0IGRvbWFpbkRldGFpbHNSZXNwb25zZSA9XG4gICAgICAgICAgYXdhaXQgZG9tYWluTW5nU2VydmljZS5nZXREb21haW5EZXRhaWxzQnlOYW1lKGRvbWFpbi5uYW1lLCBcIkFsbFwiKTtcbiAgICAgICAgY29uc29sZS5sb2coXCLwn5OLIERvbWFpbiBkZXRhaWxzOlwiLCBkb21haW5EZXRhaWxzUmVzcG9uc2UuZGF0YSk7XG5cbiAgICAgICAgaWYgKFxuICAgICAgICAgIGRvbWFpbkRldGFpbHNSZXNwb25zZS5kYXRhLnN1Y2Nlc3MgJiZcbiAgICAgICAgICBkb21haW5EZXRhaWxzUmVzcG9uc2UuZGF0YS5kb21haW5cbiAgICAgICAgKSB7XG4gICAgICAgICAgY29uc3QgZG9tYWluSW5mbyA9IGRvbWFpbkRldGFpbHNSZXNwb25zZS5kYXRhLmRvbWFpbjtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIvCfk4ogQ3VycmVudCBzdGF0dXM6XCIsIGRvbWFpbkluZm8uY3VycmVudHN0YXR1cyk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLwn5OKIE9yZGVyIHN0YXR1czpcIiwgZG9tYWluSW5mby5vcmRlcnN0YXR1cyk7XG4gICAgICAgICAgY29uc29sZS5sb2coXCLwn5OKIERvbWFpbiBzdGF0dXM6XCIsIGRvbWFpbkluZm8uZG9tYWluc3RhdHVzKTtcbiAgICAgICAgICBjb25zb2xlLmxvZyhcIvCfk4ogT3JkZXIgSUQ6XCIsIGRvbWFpbkluZm8ub3JkZXJpZCk7XG5cbiAgICAgICAgICAvLyBDaGVjayBpZiBkb21haW4gaXMgaW4gYSBzdGF0ZSB0aGF0IGFsbG93cyBuYW1lc2VydmVyIG1vZGlmaWNhdGlvblxuICAgICAgICAgIGlmIChcbiAgICAgICAgICAgIGRvbWFpbkluZm8uY3VycmVudHN0YXR1cyAmJlxuICAgICAgICAgICAgZG9tYWluSW5mby5jdXJyZW50c3RhdHVzICE9PSBcIkFjdGl2ZVwiXG4gICAgICAgICAgKSB7XG4gICAgICAgICAgICB0aHJvdyBuZXcgRXJyb3IoXG4gICAgICAgICAgICAgIGBEb21haW4gaXMgbm90IGFjdGl2ZS4gQ3VycmVudCBzdGF0dXM6ICR7ZG9tYWluSW5mby5jdXJyZW50c3RhdHVzfS4gUGxlYXNlIHdhaXQgZm9yIHRoZSBkb21haW4gdG8gYmVjb21lIGFjdGl2ZSBiZWZvcmUgbW9kaWZ5aW5nIG5hbWVzZXJ2ZXJzLmBcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQ2hlY2sgaWYgdGhlcmUgYXJlIGFueSBvcmRlciBzdGF0dXMgbG9ja3MgdGhhdCBtaWdodCBwcmV2ZW50IG1vZGlmaWNhdGlvblxuICAgICAgICAgIGlmIChcbiAgICAgICAgICAgIGRvbWFpbkluZm8ub3JkZXJzdGF0dXMgJiZcbiAgICAgICAgICAgIEFycmF5LmlzQXJyYXkoZG9tYWluSW5mby5vcmRlcnN0YXR1cykgJiZcbiAgICAgICAgICAgIGRvbWFpbkluZm8ub3JkZXJzdGF0dXMubGVuZ3RoID4gMFxuICAgICAgICAgICkge1xuICAgICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICAgIFwi4pqg77iPIERvbWFpbiBoYXMgb3JkZXIgc3RhdHVzIGxvY2tzOlwiLFxuICAgICAgICAgICAgICBkb21haW5JbmZvLm9yZGVyc3RhdHVzXG4gICAgICAgICAgICApO1xuICAgICAgICAgICAgdG9hc3Qud2FybmluZyhcbiAgICAgICAgICAgICAgYERvbWFpbiBoYXMgc3RhdHVzIGxvY2tzOiAke2RvbWFpbkluZm8ub3JkZXJzdGF0dXMuam9pbihcbiAgICAgICAgICAgICAgICBcIiwgXCJcbiAgICAgICAgICAgICAgKX0uIE5hbWVzZXJ2ZXIgbW9kaWZpY2F0aW9uIG1heSBmYWlsLmBcbiAgICAgICAgICAgICk7XG4gICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChzdGF0dXNFcnJvcikge1xuICAgICAgICBjb25zb2xlLndhcm4oXCLimqDvuI8gQ291bGQgbm90IGNoZWNrIGRvbWFpbiBzdGF0dXM6XCIsIHN0YXR1c0Vycm9yLm1lc3NhZ2UpO1xuICAgICAgICAvLyBDb250aW51ZSB3aXRoIG5hbWVzZXJ2ZXIgbW9kaWZpY2F0aW9uIGV2ZW4gaWYgc3RhdHVzIGNoZWNrIGZhaWxzXG4gICAgICB9XG5cbiAgICAgIGNvbnN0IHVwZGF0ZURhdGEgPSB7XG4gICAgICAgIG9yZGVySWQ6IHJlYWxPcmRlcklkLCAvLyBVc2UgdGhlIHJlYWwgZG9tYWluIHJlZ2lzdHJhdGlvbiBvcmRlciBJRFxuICAgICAgICBuYW1lc2VydmVyczogdmFsaWROYW1lc2VydmVycyxcbiAgICAgIH07XG5cbiAgICAgIGNvbnNvbGUubG9nKFwi8J+UpyBVcGRhdGluZyBuYW1lc2VydmVycyB3aXRoIGRhdGE6XCIsIHVwZGF0ZURhdGEpO1xuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UubW9kaWZ5TmFtZXNlcnZlcnModXBkYXRlRGF0YSk7XG5cbiAgICAgIGlmIChyZXNwb25zZS5kYXRhLnN1Y2Nlc3MpIHtcbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIk5hbWVzZXJ2ZXJzIHVwZGF0ZWQgc3VjY2Vzc2Z1bGx5XCIpO1xuXG4gICAgICAgIC8vIENhbGwgb25VcGRhdGUgY2FsbGJhY2sgaWYgcHJvdmlkZWRcbiAgICAgICAgaWYgKG9uVXBkYXRlKSB7XG4gICAgICAgICAgb25VcGRhdGUoe1xuICAgICAgICAgICAgLi4uZG9tYWluLFxuICAgICAgICAgICAgbmFtZXNlcnZlcnM6IHZhbGlkTmFtZXNlcnZlcnMsXG4gICAgICAgICAgfSk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihyZXNwb25zZS5kYXRhLmVycm9yIHx8IFwiRmFpbGVkIHRvIHVwZGF0ZSBuYW1lc2VydmVyc1wiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIkVycm9yIHVwZGF0aW5nIG5hbWVzZXJ2ZXJzOlwiLCBlcnJvcik7XG5cbiAgICAgIC8vIFByb3ZpZGUgc3BlY2lmaWMgZXJyb3IgbWVzc2FnZXMgYmFzZWQgb24gdGhlIGVycm9yIHR5cGVcbiAgICAgIGxldCBlcnJvck1lc3NhZ2UgPSBcIkZhaWxlZCB0byB1cGRhdGUgbmFtZXNlcnZlcnNcIjtcblxuICAgICAgaWYgKGVycm9yLnJlc3BvbnNlPy5kYXRhPy5kZXRhaWxzPy5tZXNzYWdlKSB7XG4gICAgICAgIGNvbnN0IGFwaUVycm9yID0gZXJyb3IucmVzcG9uc2UuZGF0YS5kZXRhaWxzLm1lc3NhZ2U7XG4gICAgICAgIGlmIChhcGlFcnJvci5pbmNsdWRlcyhcIkludmFsaWQgT3JkZXIgc3RhdHVzXCIpKSB7XG4gICAgICAgICAgZXJyb3JNZXNzYWdlID1cbiAgICAgICAgICAgIFwiQ2Fubm90IG1vZGlmeSBuYW1lc2VydmVyczogRG9tYWluIG9yZGVyIGlzIG5vdCBpbiBhIHZhbGlkIHN0YXR1cy4gVGhlIGRvbWFpbiBtYXkgYmUgaW5hY3RpdmUsIHN1c3BlbmRlZCwgb3IgbG9ja2VkLlwiO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGVycm9yTWVzc2FnZSA9IGBBUEkgRXJyb3I6ICR7YXBpRXJyb3J9YDtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChlcnJvci5yZXNwb25zZT8uZGF0YT8uZXJyb3IpIHtcbiAgICAgICAgZXJyb3JNZXNzYWdlID0gZXJyb3IucmVzcG9uc2UuZGF0YS5lcnJvcjtcbiAgICAgIH0gZWxzZSBpZiAoZXJyb3IubWVzc2FnZSkge1xuICAgICAgICBlcnJvck1lc3NhZ2UgPSBlcnJvci5tZXNzYWdlO1xuICAgICAgfVxuXG4gICAgICB0b2FzdC5lcnJvcihlcnJvck1lc3NhZ2UpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc1VwZGF0aW5nKGZhbHNlKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgcmVzZXRUb0RlZmF1bHQgPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKFwi8J+UhCBMb2FkaW5nIGN1c3RvbWVyIGRlZmF1bHQgbmFtZXNlcnZlcnMuLi5cIik7XG5cbiAgICAgIC8vIEdldCBjdXN0b21lciBkZWZhdWx0IG5hbWVzZXJ2ZXJzIGZyb20gQVBJXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGRvbWFpbk1uZ1NlcnZpY2UuZ2V0Q3VzdG9tZXJEZWZhdWx0TmFtZXNlcnZlcnMoKTtcblxuICAgICAgY29uc29sZS5sb2coXCLwn5SNIEZ1bGwgQVBJIHJlc3BvbnNlOlwiLCByZXNwb25zZS5kYXRhKTtcblxuICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc3VjY2VzcyAmJiByZXNwb25zZS5kYXRhLm5hbWVzZXJ2ZXJzKSB7XG4gICAgICAgIGxldCBkZWZhdWx0TmFtZXNlcnZlcnMgPSByZXNwb25zZS5kYXRhLm5hbWVzZXJ2ZXJzO1xuXG4gICAgICAgIC8vIEhhbmRsZSBkaWZmZXJlbnQgcmVzcG9uc2UgZm9ybWF0c1xuICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkZWZhdWx0TmFtZXNlcnZlcnMpKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coXG4gICAgICAgICAgICBcIuKchSBMb2FkZWQgZGVmYXVsdCBuYW1lc2VydmVycyBmcm9tIEFQSTpcIixcbiAgICAgICAgICAgIGRlZmF1bHROYW1lc2VydmVyc1xuICAgICAgICAgICk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc29sZS53YXJuKFxuICAgICAgICAgICAgXCLimqDvuI8gTmFtZXNlcnZlcnMgbm90IGluIGV4cGVjdGVkIGFycmF5IGZvcm1hdDpcIixcbiAgICAgICAgICAgIGRlZmF1bHROYW1lc2VydmVyc1xuICAgICAgICAgICk7XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBuYW1lc2VydmVyIGZvcm1hdCBmcm9tIEFQSVwiKTtcbiAgICAgICAgfVxuXG4gICAgICAgIHNldE5hbWVzZXJ2ZXJzKGRlZmF1bHROYW1lc2VydmVycyk7XG4gICAgICAgIHNldEVycm9ycyh7fSk7XG5cbiAgICAgICAgdG9hc3Quc3VjY2VzcyhcIkRlZmF1bHQgbmFtZXNlcnZlcnMgbG9hZGVkIHN1Y2Nlc3NmdWxseVwiKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUud2FybihcbiAgICAgICAgICBcIuKaoO+4jyBBUEkgcmVzcG9uc2UgbWlzc2luZyBzdWNjZXNzIG9yIG5hbWVzZXJ2ZXJzOlwiLFxuICAgICAgICAgIHJlc3BvbnNlLmRhdGFcbiAgICAgICAgKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiRmFpbGVkIHRvIGxvYWQgZGVmYXVsdCBuYW1lc2VydmVyc1wiKTtcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcihcIuKdjCBFcnJvciBsb2FkaW5nIGRlZmF1bHQgbmFtZXNlcnZlcnM6XCIsIGVycm9yKTtcblxuICAgICAgLy8gRmFsbGJhY2sgdG8gY29ycmVjdCBvcmRlciBmcm9tIGRhc2hib2FyZFxuICAgICAgY29uc3QgZmFsbGJhY2tOYW1lc2VydmVycyA9IFtcbiAgICAgICAgXCJtb2hhMTI4MDAzNi5lYXJ0aC5vcmRlcmJveC1kbnMuY29tXCIsXG4gICAgICAgIFwibW9oYTEyODAwMzYubWFycy5vcmRlcmJveC1kbnMuY29tXCIsXG4gICAgICAgIFwibW9oYTEyODAwMzYubWVyY3VyeS5vcmRlcmJveC1kbnMuY29tXCIsXG4gICAgICAgIFwibW9oYTEyODAwMzYudmVudXMub3JkZXJib3gtZG5zLmNvbVwiLFxuICAgICAgXTtcblxuICAgICAgc2V0TmFtZXNlcnZlcnMoZmFsbGJhY2tOYW1lc2VydmVycyk7XG4gICAgICBzZXRFcnJvcnMoe30pO1xuXG4gICAgICB0b2FzdC53YXJuaW5nKFwiVXNpbmcgZmFsbGJhY2sgZGVmYXVsdCBuYW1lc2VydmVyc1wiKTtcbiAgICB9XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8Q2FyZD5cbiAgICAgIDxDYXJkQm9keT5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi00XCI+XG4gICAgICAgICAgPFNlcnZlciBjbGFzc05hbWU9XCJoLTUgdy01IHRleHQtYmx1ZS02MDBcIiAvPlxuICAgICAgICAgIDxUeXBvZ3JhcGh5IHZhcmlhbnQ9XCJoNVwiIGNsYXNzTmFtZT1cInRleHQtZ3JheS04MDBcIj5cbiAgICAgICAgICAgIE5hbWVzZXJ2ZXIgTWFuYWdlbWVudFxuICAgICAgICAgIDwvVHlwb2dyYXBoeT5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPFR5cG9ncmFwaHkgY2xhc3NOYW1lPVwidGV4dC1ncmF5LTYwMCBtYi02XCI+XG4gICAgICAgICAgTWFuYWdlIHRoZSBuYW1lc2VydmVycyBmb3Ige2RvbWFpbj8ubmFtZX0uIENoYW5nZXMgbWF5IHRha2UgdXAgdG9cbiAgICAgICAgICAyNC00OCBob3VycyB0byBwcm9wYWdhdGUgZ2xvYmFsbHkuXG4gICAgICAgIDwvVHlwb2dyYXBoeT5cblxuICAgICAgICB7ZG9tYWluPy5vcmRlcklkICYmIChcbiAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJ0ZXh0LXhzIHRleHQtZ3JheS01MDAgbWItNFwiPlxuICAgICAgICAgICAgT3JkZXIgSUQ6IHtkb21haW4ub3JkZXJJZH1cbiAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICl9XG5cbiAgICAgICAge2Vycm9ycy5nZW5lcmFsICYmIChcbiAgICAgICAgICA8QWxlcnQgY29sb3I9XCJyZWRcIiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgICA8QWxlcnRDaXJjbGUgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLmdlbmVyYWx9XG4gICAgICAgICAgPC9BbGVydD5cbiAgICAgICAgKX1cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktMyBtYi02XCI+XG4gICAgICAgICAge25hbWVzZXJ2ZXJzLm1hcCgobnMsIGluZGV4KSA9PiAoXG4gICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC0xXCI+XG4gICAgICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgICAgICBsYWJlbD17YE5hbWVzZXJ2ZXIgJHtpbmRleCArIDF9YH1cbiAgICAgICAgICAgICAgICAgIHZhbHVlPXtuc31cbiAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cbiAgICAgICAgICAgICAgICAgICAgaGFuZGxlTmFtZXNlcnZlckNoYW5nZShpbmRleCwgZS50YXJnZXQudmFsdWUpXG4gICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICBlcnJvcj17ISFlcnJvcnNbaW5kZXhdfVxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtlcnJvcnNbaW5kZXhdID8gXCJib3JkZXItcmVkLTUwMFwiIDogXCJcIn1cbiAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgIHtlcnJvcnNbaW5kZXhdICYmIChcbiAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5IGNsYXNzTmFtZT1cInRleHQtcmVkLTUwMCB0ZXh0LXhzIG10LTFcIj5cbiAgICAgICAgICAgICAgICAgICAge2Vycm9yc1tpbmRleF19XG4gICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XG4gICAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICAgICAge25hbWVzZXJ2ZXJzLmxlbmd0aCA+IDIgJiYgKFxuICAgICAgICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJ0ZXh0XCJcbiAgICAgICAgICAgICAgICAgIGNvbG9yPVwicmVkXCJcbiAgICAgICAgICAgICAgICAgIHNpemU9XCJzbVwiXG4gICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiByZW1vdmVOYW1lc2VydmVyKGluZGV4KX1cbiAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtMlwiXG4gICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgPFRyYXNoMiBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICA8L0J1dHRvbj5cbiAgICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICkpfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC13cmFwIGdhcC0yIG1iLTZcIj5cbiAgICAgICAgICB7bmFtZXNlcnZlcnMubGVuZ3RoIDwgMTMgJiYgKFxuICAgICAgICAgICAgPEJ1dHRvblxuICAgICAgICAgICAgICB2YXJpYW50PVwib3V0bGluZWRcIlxuICAgICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgICBvbkNsaWNrPXthZGROYW1lc2VydmVyfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxQbHVzIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgICAgICBBZGQgTmFtZXNlcnZlclxuICAgICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgICAgKX1cblxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIHZhcmlhbnQ9XCJvdXRsaW5lZFwiXG4gICAgICAgICAgICBzaXplPVwic21cIlxuICAgICAgICAgICAgb25DbGljaz17cmVzZXRUb0RlZmF1bHR9XG4gICAgICAgICAgICBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMlwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPFNlcnZlciBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgIFVzZSBEZWZhdWx0XG4gICAgICAgICAgPC9CdXR0b24+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxBbGVydCBjb2xvcj1cImJsdWVcIiBjbGFzc05hbWU9XCJtYi00XCI+XG4gICAgICAgICAgPEFsZXJ0Q2lyY2xlIGNsYXNzTmFtZT1cImgtNCB3LTRcIiAvPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8VHlwb2dyYXBoeSBjbGFzc05hbWU9XCJmb250LXNlbWlib2xkXCI+SW1wb3J0YW50IE5vdGVzOjwvVHlwb2dyYXBoeT5cbiAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJ0ZXh0LXNtIG10LTEgc3BhY2UteS0xXCI+XG4gICAgICAgICAgICAgIDxsaT7igKIgQXQgbGVhc3QgMiBuYW1lc2VydmVycyBhcmUgcmVxdWlyZWQ8L2xpPlxuICAgICAgICAgICAgICA8bGk+4oCiIE1heGltdW0gMTMgbmFtZXNlcnZlcnMgYWxsb3dlZDwvbGk+XG4gICAgICAgICAgICAgIDxsaT7igKIgQ2hhbmdlcyBtYXkgdGFrZSAyNC00OCBob3VycyB0byBwcm9wYWdhdGU8L2xpPlxuICAgICAgICAgICAgICA8bGk+4oCiIEluY29ycmVjdCBuYW1lc2VydmVycyBtYXkgY2F1c2Ugd2Vic2l0ZSBkb3dudGltZTwvbGk+XG4gICAgICAgICAgICA8L3VsPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L0FsZXJ0PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBnYXAtMlwiPlxuICAgICAgICAgIDxCdXR0b25cbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLWJsdWUtNjAwIGhvdmVyOmJnLWJsdWUtNzAwIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCJcbiAgICAgICAgICAgIG9uQ2xpY2s9e2hhbmRsZVNhdmV9XG4gICAgICAgICAgICBkaXNhYmxlZD17aXNVcGRhdGluZ31cbiAgICAgICAgICA+XG4gICAgICAgICAgICB7aXNVcGRhdGluZyA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFuaW1hdGUtc3BpbiByb3VuZGVkLWZ1bGwgaC00IHctNCBib3JkZXItYi0yIGJvcmRlci13aGl0ZVwiPjwvZGl2PlxuICAgICAgICAgICAgICAgIFVwZGF0aW5nLi4uXG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8U2F2ZSBjbGFzc05hbWU9XCJoLTQgdy00XCIgLz5cbiAgICAgICAgICAgICAgICBTYXZlIENoYW5nZXNcbiAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICApfVxuICAgICAgICAgIDwvQnV0dG9uPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvQ2FyZEJvZHk+XG4gICAgPC9DYXJkPlxuICApO1xufVxuIl0sIm5hbWVzIjpbInVzZVN0YXRlIiwiVHlwb2dyYXBoeSIsIkNhcmQiLCJDYXJkQm9keSIsIkJ1dHRvbiIsIklucHV0IiwiQWxlcnQiLCJUYWJzIiwiVGFic0hlYWRlciIsIlRhYnNCb2R5IiwiVGFiIiwiVGFiUGFuZWwiLCJTZXJ2ZXIiLCJQbHVzIiwiVHJhc2gyIiwiU2F2ZSIsIkFsZXJ0Q2lyY2xlIiwiQ2hlY2tDaXJjbGUiLCJHbG9iZSIsImRvbWFpbk1uZ1NlcnZpY2UiLCJ0b2FzdCIsIkNoaWxkTmFtZVNlcnZlck1hbmFnZXIiLCJOYW1lc2VydmVyTWFuYWdlciIsImRvbWFpbiIsIm9uVXBkYXRlIiwibmFtZXNlcnZlcnMiLCJzZXROYW1lc2VydmVycyIsImlzVXBkYXRpbmciLCJzZXRJc1VwZGF0aW5nIiwiZXJyb3JzIiwic2V0RXJyb3JzIiwiYWN0aXZlVGFiIiwic2V0QWN0aXZlVGFiIiwidmFsaWRhdGVOYW1lc2VydmVyIiwibnMiLCJ0cmltIiwiZG9tYWluUmVnZXgiLCJ0ZXN0IiwidmFsaWRhdGVBbGxOYW1lc2VydmVycyIsIm5ld0Vycm9ycyIsInZhbGlkTmFtZXNlcnZlcnMiLCJmaWx0ZXIiLCJsZW5ndGgiLCJnZW5lcmFsIiwiZm9yRWFjaCIsImluZGV4IiwiZXJyb3IiLCJ0cmltbWVkTnMiLCJtYXAiLCJ0b0xvd2VyQ2FzZSIsImR1cGxpY2F0ZXMiLCJpbmRleE9mIiwiT2JqZWN0Iiwia2V5cyIsImhhbmRsZU5hbWVzZXJ2ZXJDaGFuZ2UiLCJ2YWx1ZSIsIm5ld05hbWVzZXJ2ZXJzIiwiYWRkTmFtZXNlcnZlciIsInJlbW92ZU5hbWVzZXJ2ZXIiLCJfIiwiaSIsImhhbmRsZVNhdmUiLCJjb25zb2xlIiwibG9nIiwibmFtZSIsIkVycm9yIiwib3JkZXJJZFJlc3BvbnNlIiwiZ2V0RG9tYWluT3JkZXJJZCIsImRhdGEiLCJzdWNjZXNzIiwicmVhbE9yZGVySWQiLCJvcmRlcklkIiwiZG9tYWluRGV0YWlsc1Jlc3BvbnNlIiwiZ2V0RG9tYWluRGV0YWlsc0J5TmFtZSIsImRvbWFpbkluZm8iLCJjdXJyZW50c3RhdHVzIiwib3JkZXJzdGF0dXMiLCJkb21haW5zdGF0dXMiLCJvcmRlcmlkIiwiQXJyYXkiLCJpc0FycmF5Iiwid2FybmluZyIsImpvaW4iLCJzdGF0dXNFcnJvciIsIndhcm4iLCJtZXNzYWdlIiwidXBkYXRlRGF0YSIsInJlc3BvbnNlIiwibW9kaWZ5TmFtZXNlcnZlcnMiLCJlcnJvck1lc3NhZ2UiLCJkZXRhaWxzIiwiYXBpRXJyb3IiLCJpbmNsdWRlcyIsInJlc2V0VG9EZWZhdWx0IiwiZ2V0Q3VzdG9tZXJEZWZhdWx0TmFtZXNlcnZlcnMiLCJkZWZhdWx0TmFtZXNlcnZlcnMiLCJmYWxsYmFja05hbWVzZXJ2ZXJzIiwiZGl2IiwiY2xhc3NOYW1lIiwidmFyaWFudCIsImNvbG9yIiwibGFiZWwiLCJvbkNoYW5nZSIsImUiLCJ0YXJnZXQiLCJzaXplIiwib25DbGljayIsInVsIiwibGkiLCJkaXNhYmxlZCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});