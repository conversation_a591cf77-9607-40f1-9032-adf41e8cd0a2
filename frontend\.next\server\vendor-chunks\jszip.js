"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/jszip";
exports.ids = ["vendor-chunks/jszip"];
exports.modules = {

/***/ "(ssr)/./node_modules/jszip/lib/base64.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/base64.js ***!
  \******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n// private property\nvar _keyStr = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\n// public method for encoding\nexports.encode = function(input) {\n    var output = [];\n    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;\n    var i = 0, len = input.length, remainingBytes = len;\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    while(i < input.length){\n        remainingBytes = len - i;\n        if (!isArray) {\n            chr1 = input.charCodeAt(i++);\n            chr2 = i < len ? input.charCodeAt(i++) : 0;\n            chr3 = i < len ? input.charCodeAt(i++) : 0;\n        } else {\n            chr1 = input[i++];\n            chr2 = i < len ? input[i++] : 0;\n            chr3 = i < len ? input[i++] : 0;\n        }\n        enc1 = chr1 >> 2;\n        enc2 = (chr1 & 3) << 4 | chr2 >> 4;\n        enc3 = remainingBytes > 1 ? (chr2 & 15) << 2 | chr3 >> 6 : 64;\n        enc4 = remainingBytes > 2 ? chr3 & 63 : 64;\n        output.push(_keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4));\n    }\n    return output.join(\"\");\n};\n// public method for decoding\nexports.decode = function(input) {\n    var chr1, chr2, chr3;\n    var enc1, enc2, enc3, enc4;\n    var i = 0, resultIndex = 0;\n    var dataUrlPrefix = \"data:\";\n    if (input.substr(0, dataUrlPrefix.length) === dataUrlPrefix) {\n        // This is a common error: people give a data url\n        // (data:image/png;base64,iVBOR...) with a {base64: true} and\n        // wonders why things don't work.\n        // We can detect that the string input looks like a data url but we\n        // *can't* be sure it is one: removing everything up to the comma would\n        // be too dangerous.\n        throw new Error(\"Invalid base64 input, it looks like a data url.\");\n    }\n    input = input.replace(/[^A-Za-z0-9+/=]/g, \"\");\n    var totalLength = input.length * 3 / 4;\n    if (input.charAt(input.length - 1) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (input.charAt(input.length - 2) === _keyStr.charAt(64)) {\n        totalLength--;\n    }\n    if (totalLength % 1 !== 0) {\n        // totalLength is not an integer, the length does not match a valid\n        // base64 content. That can happen if:\n        // - the input is not a base64 content\n        // - the input is *almost* a base64 content, with a extra chars at the\n        //   beginning or at the end\n        // - the input uses a base64 variant (base64url for example)\n        throw new Error(\"Invalid base64 input, bad content length.\");\n    }\n    var output;\n    if (support.uint8array) {\n        output = new Uint8Array(totalLength | 0);\n    } else {\n        output = new Array(totalLength | 0);\n    }\n    while(i < input.length){\n        enc1 = _keyStr.indexOf(input.charAt(i++));\n        enc2 = _keyStr.indexOf(input.charAt(i++));\n        enc3 = _keyStr.indexOf(input.charAt(i++));\n        enc4 = _keyStr.indexOf(input.charAt(i++));\n        chr1 = enc1 << 2 | enc2 >> 4;\n        chr2 = (enc2 & 15) << 4 | enc3 >> 2;\n        chr3 = (enc3 & 3) << 6 | enc4;\n        output[resultIndex++] = chr1;\n        if (enc3 !== 64) {\n            output[resultIndex++] = chr2;\n        }\n        if (enc4 !== 64) {\n            output[resultIndex++] = chr3;\n        }\n    }\n    return output;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2Jhc2U2NC5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiLElBQUlBLFFBQVFDLG1CQUFPQSxDQUFDLHdEQUFTO0FBQzdCLElBQUlDLFVBQVVELG1CQUFPQSxDQUFDLDREQUFXO0FBQ2pDLG1CQUFtQjtBQUNuQixJQUFJRSxVQUFVO0FBR2QsNkJBQTZCO0FBQzdCQyxjQUFjLEdBQUcsU0FBU0UsS0FBSztJQUMzQixJQUFJQyxTQUFTLEVBQUU7SUFDZixJQUFJQyxNQUFNQyxNQUFNQyxNQUFNQyxNQUFNQyxNQUFNQyxNQUFNQztJQUN4QyxJQUFJQyxJQUFJLEdBQUdDLE1BQU1WLE1BQU1XLE1BQU0sRUFBRUMsaUJBQWlCRjtJQUVoRCxJQUFJRyxVQUFVbkIsTUFBTW9CLFNBQVMsQ0FBQ2QsV0FBVztJQUN6QyxNQUFPUyxJQUFJVCxNQUFNVyxNQUFNLENBQUU7UUFDckJDLGlCQUFpQkYsTUFBTUQ7UUFFdkIsSUFBSSxDQUFDSSxTQUFTO1lBQ1ZYLE9BQU9GLE1BQU1lLFVBQVUsQ0FBQ047WUFDeEJOLE9BQU9NLElBQUlDLE1BQU1WLE1BQU1lLFVBQVUsQ0FBQ04sT0FBTztZQUN6Q0wsT0FBT0ssSUFBSUMsTUFBTVYsTUFBTWUsVUFBVSxDQUFDTixPQUFPO1FBQzdDLE9BQU87WUFDSFAsT0FBT0YsS0FBSyxDQUFDUyxJQUFJO1lBQ2pCTixPQUFPTSxJQUFJQyxNQUFNVixLQUFLLENBQUNTLElBQUksR0FBRztZQUM5QkwsT0FBT0ssSUFBSUMsTUFBTVYsS0FBSyxDQUFDUyxJQUFJLEdBQUc7UUFDbEM7UUFFQUosT0FBT0gsUUFBUTtRQUNmSSxPQUFPLENBQUVKLE9BQU8sTUFBTSxJQUFNQyxRQUFRO1FBQ3BDSSxPQUFPSyxpQkFBaUIsSUFBSyxDQUFFVCxPQUFPLEVBQUMsS0FBTSxJQUFNQyxRQUFRLElBQU07UUFDakVJLE9BQU9JLGlCQUFpQixJQUFLUixPQUFPLEtBQU07UUFFMUNILE9BQU9lLElBQUksQ0FBQ25CLFFBQVFvQixNQUFNLENBQUNaLFFBQVFSLFFBQVFvQixNQUFNLENBQUNYLFFBQVFULFFBQVFvQixNQUFNLENBQUNWLFFBQVFWLFFBQVFvQixNQUFNLENBQUNUO0lBRXBHO0lBRUEsT0FBT1AsT0FBT2lCLElBQUksQ0FBQztBQUN2QjtBQUVBLDZCQUE2QjtBQUM3QnBCLGNBQWMsR0FBRyxTQUFTRSxLQUFLO0lBQzNCLElBQUlFLE1BQU1DLE1BQU1DO0lBQ2hCLElBQUlDLE1BQU1DLE1BQU1DLE1BQU1DO0lBQ3RCLElBQUlDLElBQUksR0FBR1csY0FBYztJQUV6QixJQUFJQyxnQkFBZ0I7SUFFcEIsSUFBSXJCLE1BQU1zQixNQUFNLENBQUMsR0FBR0QsY0FBY1YsTUFBTSxNQUFNVSxlQUFlO1FBQ3pELGlEQUFpRDtRQUNqRCw2REFBNkQ7UUFDN0QsaUNBQWlDO1FBQ2pDLG1FQUFtRTtRQUNuRSx1RUFBdUU7UUFDdkUsb0JBQW9CO1FBQ3BCLE1BQU0sSUFBSUUsTUFBTTtJQUNwQjtJQUVBdkIsUUFBUUEsTUFBTXdCLE9BQU8sQ0FBQyxvQkFBb0I7SUFFMUMsSUFBSUMsY0FBY3pCLE1BQU1XLE1BQU0sR0FBRyxJQUFJO0lBQ3JDLElBQUdYLE1BQU1pQixNQUFNLENBQUNqQixNQUFNVyxNQUFNLEdBQUcsT0FBT2QsUUFBUW9CLE1BQU0sQ0FBQyxLQUFLO1FBQ3REUTtJQUNKO0lBQ0EsSUFBR3pCLE1BQU1pQixNQUFNLENBQUNqQixNQUFNVyxNQUFNLEdBQUcsT0FBT2QsUUFBUW9CLE1BQU0sQ0FBQyxLQUFLO1FBQ3REUTtJQUNKO0lBQ0EsSUFBSUEsY0FBYyxNQUFNLEdBQUc7UUFDdkIsbUVBQW1FO1FBQ25FLHNDQUFzQztRQUN0QyxzQ0FBc0M7UUFDdEMsc0VBQXNFO1FBQ3RFLDRCQUE0QjtRQUM1Qiw0REFBNEQ7UUFDNUQsTUFBTSxJQUFJRixNQUFNO0lBQ3BCO0lBQ0EsSUFBSXRCO0lBQ0osSUFBSUwsUUFBUThCLFVBQVUsRUFBRTtRQUNwQnpCLFNBQVMsSUFBSTBCLFdBQVdGLGNBQVk7SUFDeEMsT0FBTztRQUNIeEIsU0FBUyxJQUFJMkIsTUFBTUgsY0FBWTtJQUNuQztJQUVBLE1BQU9oQixJQUFJVCxNQUFNVyxNQUFNLENBQUU7UUFFckJOLE9BQU9SLFFBQVFnQyxPQUFPLENBQUM3QixNQUFNaUIsTUFBTSxDQUFDUjtRQUNwQ0gsT0FBT1QsUUFBUWdDLE9BQU8sQ0FBQzdCLE1BQU1pQixNQUFNLENBQUNSO1FBQ3BDRixPQUFPVixRQUFRZ0MsT0FBTyxDQUFDN0IsTUFBTWlCLE1BQU0sQ0FBQ1I7UUFDcENELE9BQU9YLFFBQVFnQyxPQUFPLENBQUM3QixNQUFNaUIsTUFBTSxDQUFDUjtRQUVwQ1AsT0FBTyxRQUFTLElBQU1JLFFBQVE7UUFDOUJILE9BQU8sQ0FBRUcsT0FBTyxFQUFDLEtBQU0sSUFBTUMsUUFBUTtRQUNyQ0gsT0FBTyxDQUFFRyxPQUFPLE1BQU0sSUFBS0M7UUFFM0JQLE1BQU0sQ0FBQ21CLGNBQWMsR0FBR2xCO1FBRXhCLElBQUlLLFNBQVMsSUFBSTtZQUNiTixNQUFNLENBQUNtQixjQUFjLEdBQUdqQjtRQUM1QjtRQUNBLElBQUlLLFNBQVMsSUFBSTtZQUNiUCxNQUFNLENBQUNtQixjQUFjLEdBQUdoQjtRQUM1QjtJQUVKO0lBRUEsT0FBT0g7QUFDWCIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2Jhc2U2NC5qcz9kNjkxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4vdXRpbHNcIik7XG52YXIgc3VwcG9ydCA9IHJlcXVpcmUoXCIuL3N1cHBvcnRcIik7XG4vLyBwcml2YXRlIHByb3BlcnR5XG52YXIgX2tleVN0ciA9IFwiQUJDREVGR0hJSktMTU5PUFFSU1RVVldYWVphYmNkZWZnaGlqa2xtbm9wcXJzdHV2d3h5ejAxMjM0NTY3ODkrLz1cIjtcblxuXG4vLyBwdWJsaWMgbWV0aG9kIGZvciBlbmNvZGluZ1xuZXhwb3J0cy5lbmNvZGUgPSBmdW5jdGlvbihpbnB1dCkge1xuICAgIHZhciBvdXRwdXQgPSBbXTtcbiAgICB2YXIgY2hyMSwgY2hyMiwgY2hyMywgZW5jMSwgZW5jMiwgZW5jMywgZW5jNDtcbiAgICB2YXIgaSA9IDAsIGxlbiA9IGlucHV0Lmxlbmd0aCwgcmVtYWluaW5nQnl0ZXMgPSBsZW47XG5cbiAgICB2YXIgaXNBcnJheSA9IHV0aWxzLmdldFR5cGVPZihpbnB1dCkgIT09IFwic3RyaW5nXCI7XG4gICAgd2hpbGUgKGkgPCBpbnB1dC5sZW5ndGgpIHtcbiAgICAgICAgcmVtYWluaW5nQnl0ZXMgPSBsZW4gLSBpO1xuXG4gICAgICAgIGlmICghaXNBcnJheSkge1xuICAgICAgICAgICAgY2hyMSA9IGlucHV0LmNoYXJDb2RlQXQoaSsrKTtcbiAgICAgICAgICAgIGNocjIgPSBpIDwgbGVuID8gaW5wdXQuY2hhckNvZGVBdChpKyspIDogMDtcbiAgICAgICAgICAgIGNocjMgPSBpIDwgbGVuID8gaW5wdXQuY2hhckNvZGVBdChpKyspIDogMDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgIGNocjEgPSBpbnB1dFtpKytdO1xuICAgICAgICAgICAgY2hyMiA9IGkgPCBsZW4gPyBpbnB1dFtpKytdIDogMDtcbiAgICAgICAgICAgIGNocjMgPSBpIDwgbGVuID8gaW5wdXRbaSsrXSA6IDA7XG4gICAgICAgIH1cblxuICAgICAgICBlbmMxID0gY2hyMSA+PiAyO1xuICAgICAgICBlbmMyID0gKChjaHIxICYgMykgPDwgNCkgfCAoY2hyMiA+PiA0KTtcbiAgICAgICAgZW5jMyA9IHJlbWFpbmluZ0J5dGVzID4gMSA/ICgoKGNocjIgJiAxNSkgPDwgMikgfCAoY2hyMyA+PiA2KSkgOiA2NDtcbiAgICAgICAgZW5jNCA9IHJlbWFpbmluZ0J5dGVzID4gMiA/IChjaHIzICYgNjMpIDogNjQ7XG5cbiAgICAgICAgb3V0cHV0LnB1c2goX2tleVN0ci5jaGFyQXQoZW5jMSkgKyBfa2V5U3RyLmNoYXJBdChlbmMyKSArIF9rZXlTdHIuY2hhckF0KGVuYzMpICsgX2tleVN0ci5jaGFyQXQoZW5jNCkpO1xuXG4gICAgfVxuXG4gICAgcmV0dXJuIG91dHB1dC5qb2luKFwiXCIpO1xufTtcblxuLy8gcHVibGljIG1ldGhvZCBmb3IgZGVjb2RpbmdcbmV4cG9ydHMuZGVjb2RlID0gZnVuY3Rpb24oaW5wdXQpIHtcbiAgICB2YXIgY2hyMSwgY2hyMiwgY2hyMztcbiAgICB2YXIgZW5jMSwgZW5jMiwgZW5jMywgZW5jNDtcbiAgICB2YXIgaSA9IDAsIHJlc3VsdEluZGV4ID0gMDtcblxuICAgIHZhciBkYXRhVXJsUHJlZml4ID0gXCJkYXRhOlwiO1xuXG4gICAgaWYgKGlucHV0LnN1YnN0cigwLCBkYXRhVXJsUHJlZml4Lmxlbmd0aCkgPT09IGRhdGFVcmxQcmVmaXgpIHtcbiAgICAgICAgLy8gVGhpcyBpcyBhIGNvbW1vbiBlcnJvcjogcGVvcGxlIGdpdmUgYSBkYXRhIHVybFxuICAgICAgICAvLyAoZGF0YTppbWFnZS9wbmc7YmFzZTY0LGlWQk9SLi4uKSB3aXRoIGEge2Jhc2U2NDogdHJ1ZX0gYW5kXG4gICAgICAgIC8vIHdvbmRlcnMgd2h5IHRoaW5ncyBkb24ndCB3b3JrLlxuICAgICAgICAvLyBXZSBjYW4gZGV0ZWN0IHRoYXQgdGhlIHN0cmluZyBpbnB1dCBsb29rcyBsaWtlIGEgZGF0YSB1cmwgYnV0IHdlXG4gICAgICAgIC8vICpjYW4ndCogYmUgc3VyZSBpdCBpcyBvbmU6IHJlbW92aW5nIGV2ZXJ5dGhpbmcgdXAgdG8gdGhlIGNvbW1hIHdvdWxkXG4gICAgICAgIC8vIGJlIHRvbyBkYW5nZXJvdXMuXG4gICAgICAgIHRocm93IG5ldyBFcnJvcihcIkludmFsaWQgYmFzZTY0IGlucHV0LCBpdCBsb29rcyBsaWtlIGEgZGF0YSB1cmwuXCIpO1xuICAgIH1cblxuICAgIGlucHV0ID0gaW5wdXQucmVwbGFjZSgvW15BLVphLXowLTkrLz1dL2csIFwiXCIpO1xuXG4gICAgdmFyIHRvdGFsTGVuZ3RoID0gaW5wdXQubGVuZ3RoICogMyAvIDQ7XG4gICAgaWYoaW5wdXQuY2hhckF0KGlucHV0Lmxlbmd0aCAtIDEpID09PSBfa2V5U3RyLmNoYXJBdCg2NCkpIHtcbiAgICAgICAgdG90YWxMZW5ndGgtLTtcbiAgICB9XG4gICAgaWYoaW5wdXQuY2hhckF0KGlucHV0Lmxlbmd0aCAtIDIpID09PSBfa2V5U3RyLmNoYXJBdCg2NCkpIHtcbiAgICAgICAgdG90YWxMZW5ndGgtLTtcbiAgICB9XG4gICAgaWYgKHRvdGFsTGVuZ3RoICUgMSAhPT0gMCkge1xuICAgICAgICAvLyB0b3RhbExlbmd0aCBpcyBub3QgYW4gaW50ZWdlciwgdGhlIGxlbmd0aCBkb2VzIG5vdCBtYXRjaCBhIHZhbGlkXG4gICAgICAgIC8vIGJhc2U2NCBjb250ZW50LiBUaGF0IGNhbiBoYXBwZW4gaWY6XG4gICAgICAgIC8vIC0gdGhlIGlucHV0IGlzIG5vdCBhIGJhc2U2NCBjb250ZW50XG4gICAgICAgIC8vIC0gdGhlIGlucHV0IGlzICphbG1vc3QqIGEgYmFzZTY0IGNvbnRlbnQsIHdpdGggYSBleHRyYSBjaGFycyBhdCB0aGVcbiAgICAgICAgLy8gICBiZWdpbm5pbmcgb3IgYXQgdGhlIGVuZFxuICAgICAgICAvLyAtIHRoZSBpbnB1dCB1c2VzIGEgYmFzZTY0IHZhcmlhbnQgKGJhc2U2NHVybCBmb3IgZXhhbXBsZSlcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiSW52YWxpZCBiYXNlNjQgaW5wdXQsIGJhZCBjb250ZW50IGxlbmd0aC5cIik7XG4gICAgfVxuICAgIHZhciBvdXRwdXQ7XG4gICAgaWYgKHN1cHBvcnQudWludDhhcnJheSkge1xuICAgICAgICBvdXRwdXQgPSBuZXcgVWludDhBcnJheSh0b3RhbExlbmd0aHwwKTtcbiAgICB9IGVsc2Uge1xuICAgICAgICBvdXRwdXQgPSBuZXcgQXJyYXkodG90YWxMZW5ndGh8MCk7XG4gICAgfVxuXG4gICAgd2hpbGUgKGkgPCBpbnB1dC5sZW5ndGgpIHtcblxuICAgICAgICBlbmMxID0gX2tleVN0ci5pbmRleE9mKGlucHV0LmNoYXJBdChpKyspKTtcbiAgICAgICAgZW5jMiA9IF9rZXlTdHIuaW5kZXhPZihpbnB1dC5jaGFyQXQoaSsrKSk7XG4gICAgICAgIGVuYzMgPSBfa2V5U3RyLmluZGV4T2YoaW5wdXQuY2hhckF0KGkrKykpO1xuICAgICAgICBlbmM0ID0gX2tleVN0ci5pbmRleE9mKGlucHV0LmNoYXJBdChpKyspKTtcblxuICAgICAgICBjaHIxID0gKGVuYzEgPDwgMikgfCAoZW5jMiA+PiA0KTtcbiAgICAgICAgY2hyMiA9ICgoZW5jMiAmIDE1KSA8PCA0KSB8IChlbmMzID4+IDIpO1xuICAgICAgICBjaHIzID0gKChlbmMzICYgMykgPDwgNikgfCBlbmM0O1xuXG4gICAgICAgIG91dHB1dFtyZXN1bHRJbmRleCsrXSA9IGNocjE7XG5cbiAgICAgICAgaWYgKGVuYzMgIT09IDY0KSB7XG4gICAgICAgICAgICBvdXRwdXRbcmVzdWx0SW5kZXgrK10gPSBjaHIyO1xuICAgICAgICB9XG4gICAgICAgIGlmIChlbmM0ICE9PSA2NCkge1xuICAgICAgICAgICAgb3V0cHV0W3Jlc3VsdEluZGV4KytdID0gY2hyMztcbiAgICAgICAgfVxuXG4gICAgfVxuXG4gICAgcmV0dXJuIG91dHB1dDtcbn07XG4iXSwibmFtZXMiOlsidXRpbHMiLCJyZXF1aXJlIiwic3VwcG9ydCIsIl9rZXlTdHIiLCJleHBvcnRzIiwiZW5jb2RlIiwiaW5wdXQiLCJvdXRwdXQiLCJjaHIxIiwiY2hyMiIsImNocjMiLCJlbmMxIiwiZW5jMiIsImVuYzMiLCJlbmM0IiwiaSIsImxlbiIsImxlbmd0aCIsInJlbWFpbmluZ0J5dGVzIiwiaXNBcnJheSIsImdldFR5cGVPZiIsImNoYXJDb2RlQXQiLCJwdXNoIiwiY2hhckF0Iiwiam9pbiIsImRlY29kZSIsInJlc3VsdEluZGV4IiwiZGF0YVVybFByZWZpeCIsInN1YnN0ciIsIkVycm9yIiwicmVwbGFjZSIsInRvdGFsTGVuZ3RoIiwidWludDhhcnJheSIsIlVpbnQ4QXJyYXkiLCJBcnJheSIsImluZGV4T2YiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/base64.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressedObject.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/compressedObject.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar DataLengthProbe = __webpack_require__(/*! ./stream/DataLengthProbe */ \"(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\");\n/**\n * Represent a compressed object, with everything needed to decompress it.\n * @constructor\n * @param {number} compressedSize the size of the data compressed.\n * @param {number} uncompressedSize the size of the data after decompression.\n * @param {number} crc32 the crc32 of the decompressed file.\n * @param {object} compression the type of compression, see lib/compressions.js.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the compressed data.\n */ function CompressedObject(compressedSize, uncompressedSize, crc32, compression, data) {\n    this.compressedSize = compressedSize;\n    this.uncompressedSize = uncompressedSize;\n    this.crc32 = crc32;\n    this.compression = compression;\n    this.compressedContent = data;\n}\nCompressedObject.prototype = {\n    /**\n     * Create a worker to get the uncompressed content.\n     * @return {GenericWorker} the worker.\n     */ getContentWorker: function() {\n        var worker = new DataWorker(external.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new DataLengthProbe(\"data_length\"));\n        var that = this;\n        worker.on(\"end\", function() {\n            if (this.streamInfo[\"data_length\"] !== that.uncompressedSize) {\n                throw new Error(\"Bug : uncompressed data size mismatch\");\n            }\n        });\n        return worker;\n    },\n    /**\n     * Create a worker to get the compressed content.\n     * @return {GenericWorker} the worker.\n     */ getCompressedWorker: function() {\n        return new DataWorker(external.Promise.resolve(this.compressedContent)).withStreamInfo(\"compressedSize\", this.compressedSize).withStreamInfo(\"uncompressedSize\", this.uncompressedSize).withStreamInfo(\"crc32\", this.crc32).withStreamInfo(\"compression\", this.compression);\n    }\n};\n/**\n * Chain the given worker with other workers to compress the content with the\n * given compression.\n * @param {GenericWorker} uncompressedWorker the worker to pipe.\n * @param {Object} compression the compression object.\n * @param {Object} compressionOptions the options to use when compressing.\n * @return {GenericWorker} the new worker compressing the content.\n */ CompressedObject.createWorkerFrom = function(uncompressedWorker, compression, compressionOptions) {\n    return uncompressedWorker.pipe(new Crc32Probe()).pipe(new DataLengthProbe(\"uncompressedSize\")).pipe(compression.compressWorker(compressionOptions)).pipe(new DataLengthProbe(\"compressedSize\")).withStreamInfo(\"compression\", compression);\n};\nmodule.exports = CompressedObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressedObject.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/compressions.js":
/*!************************************************!*\
  !*** ./node_modules/jszip/lib/compressions.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nexports.STORE = {\n    magic: \"\\x00\\x00\",\n    compressWorker: function() {\n        return new GenericWorker(\"STORE compression\");\n    },\n    uncompressWorker: function() {\n        return new GenericWorker(\"STORE decompression\");\n    }\n};\nexports.DEFLATE = __webpack_require__(/*! ./flate */ \"(ssr)/./node_modules/jszip/lib/flate.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2NvbXByZXNzaW9ucy5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUViLElBQUlBLGdCQUFnQkMsbUJBQU9BLENBQUMsc0ZBQXdCO0FBRXBEQyxhQUFhLEdBQUc7SUFDWkUsT0FBTztJQUNQQyxnQkFBaUI7UUFDYixPQUFPLElBQUlMLGNBQWM7SUFDN0I7SUFDQU0sa0JBQW1CO1FBQ2YsT0FBTyxJQUFJTixjQUFjO0lBQzdCO0FBQ0o7QUFDQUUsK0ZBQW9DIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvY29tcHJlc3Npb25zLmpzP2Y1NzUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5cbnZhciBHZW5lcmljV29ya2VyID0gcmVxdWlyZShcIi4vc3RyZWFtL0dlbmVyaWNXb3JrZXJcIik7XG5cbmV4cG9ydHMuU1RPUkUgPSB7XG4gICAgbWFnaWM6IFwiXFx4MDBcXHgwMFwiLFxuICAgIGNvbXByZXNzV29ya2VyIDogZnVuY3Rpb24gKCkge1xuICAgICAgICByZXR1cm4gbmV3IEdlbmVyaWNXb3JrZXIoXCJTVE9SRSBjb21wcmVzc2lvblwiKTtcbiAgICB9LFxuICAgIHVuY29tcHJlc3NXb3JrZXIgOiBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHJldHVybiBuZXcgR2VuZXJpY1dvcmtlcihcIlNUT1JFIGRlY29tcHJlc3Npb25cIik7XG4gICAgfVxufTtcbmV4cG9ydHMuREVGTEFURSA9IHJlcXVpcmUoXCIuL2ZsYXRlXCIpO1xuIl0sIm5hbWVzIjpbIkdlbmVyaWNXb3JrZXIiLCJyZXF1aXJlIiwiZXhwb3J0cyIsIlNUT1JFIiwibWFnaWMiLCJjb21wcmVzc1dvcmtlciIsInVuY29tcHJlc3NXb3JrZXIiLCJERUZMQVRFIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/compressions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/crc32.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/crc32.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n/**\n * The following functions come from pako, from pako/lib/zlib/crc32.js\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */ // Use ordinary array, since untyped makes no boost here\nfunction makeTable() {\n    var c, table = [];\n    for(var n = 0; n < 256; n++){\n        c = n;\n        for(var k = 0; k < 8; k++){\n            c = c & 1 ? 0xEDB88320 ^ c >>> 1 : c >>> 1;\n        }\n        table[n] = c;\n    }\n    return table;\n}\n// Create table on load. Just 255 signed longs. Not a problem.\nvar crcTable = makeTable();\nfunction crc32(crc, buf, len, pos) {\n    var t = crcTable, end = pos + len;\n    crc = crc ^ -1;\n    for(var i = pos; i < end; i++){\n        crc = crc >>> 8 ^ t[(crc ^ buf[i]) & 0xFF];\n    }\n    return crc ^ -1; // >>> 0;\n}\n// That's all for the pako functions.\n/**\n * Compute the crc32 of a string.\n * This is almost the same as the function crc32, but for strings. Using the\n * same function for the two use cases leads to horrible performances.\n * @param {Number} crc the starting value of the crc.\n * @param {String} str the string to use.\n * @param {Number} len the length of the string.\n * @param {Number} pos the starting position for the crc32 computation.\n * @return {Number} the computed crc32.\n */ function crc32str(crc, str, len, pos) {\n    var t = crcTable, end = pos + len;\n    crc = crc ^ -1;\n    for(var i = pos; i < end; i++){\n        crc = crc >>> 8 ^ t[(crc ^ str.charCodeAt(i)) & 0xFF];\n    }\n    return crc ^ -1; // >>> 0;\n}\nmodule.exports = function crc32wrapper(input, crc) {\n    if (typeof input === \"undefined\" || !input.length) {\n        return 0;\n    }\n    var isArray = utils.getTypeOf(input) !== \"string\";\n    if (isArray) {\n        return crc32(crc | 0, input, input.length, 0);\n    } else {\n        return crc32str(crc | 0, input, input.length, 0);\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/crc32.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/defaults.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/defaults.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.base64 = false;\nexports.binary = false;\nexports.dir = false;\nexports.createFolders = true;\nexports.date = null;\nexports.compression = null;\nexports.compressionOptions = null;\nexports.comment = null;\nexports.unixPermissions = null;\nexports.dosPermissions = null;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2RlZmF1bHRzLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2JBLGNBQWMsR0FBRztBQUNqQkEsY0FBYyxHQUFHO0FBQ2pCQSxXQUFXLEdBQUc7QUFDZEEscUJBQXFCLEdBQUc7QUFDeEJBLFlBQVksR0FBRztBQUNmQSxtQkFBbUIsR0FBRztBQUN0QkEsMEJBQTBCLEdBQUc7QUFDN0JBLGVBQWUsR0FBRztBQUNsQkEsdUJBQXVCLEdBQUc7QUFDMUJBLHNCQUFzQixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZGVmYXVsdHMuanM/NTQxZSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbmV4cG9ydHMuYmFzZTY0ID0gZmFsc2U7XG5leHBvcnRzLmJpbmFyeSA9IGZhbHNlO1xuZXhwb3J0cy5kaXIgPSBmYWxzZTtcbmV4cG9ydHMuY3JlYXRlRm9sZGVycyA9IHRydWU7XG5leHBvcnRzLmRhdGUgPSBudWxsO1xuZXhwb3J0cy5jb21wcmVzc2lvbiA9IG51bGw7XG5leHBvcnRzLmNvbXByZXNzaW9uT3B0aW9ucyA9IG51bGw7XG5leHBvcnRzLmNvbW1lbnQgPSBudWxsO1xuZXhwb3J0cy51bml4UGVybWlzc2lvbnMgPSBudWxsO1xuZXhwb3J0cy5kb3NQZXJtaXNzaW9ucyA9IG51bGw7XG4iXSwibmFtZXMiOlsiZXhwb3J0cyIsImJhc2U2NCIsImJpbmFyeSIsImRpciIsImNyZWF0ZUZvbGRlcnMiLCJkYXRlIiwiY29tcHJlc3Npb24iLCJjb21wcmVzc2lvbk9wdGlvbnMiLCJjb21tZW50IiwidW5peFBlcm1pc3Npb25zIiwiZG9zUGVybWlzc2lvbnMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/defaults.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/external.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/external.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n// load the global object first:\n// - it should be better integrated in the system (unhandledRejection in node)\n// - the environment may have a custom Promise implementation (see zone.js)\nvar ES6Promise = null;\nif (typeof Promise !== \"undefined\") {\n    ES6Promise = Promise;\n} else {\n    ES6Promise = __webpack_require__(/*! lie */ \"(ssr)/./node_modules/lie/lib/index.js\");\n}\n/**\n * Let the user use/change some implementations.\n */ module.exports = {\n    Promise: ES6Promise\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2V4dGVybmFsLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsZ0NBQWdDO0FBQ2hDLDhFQUE4RTtBQUM5RSwyRUFBMkU7QUFDM0UsSUFBSUEsYUFBYTtBQUNqQixJQUFJLE9BQU9DLFlBQVksYUFBYTtJQUNoQ0QsYUFBYUM7QUFDakIsT0FBTztJQUNIRCxhQUFhRSxtQkFBT0EsQ0FBQyxrREFBSztBQUM5QjtBQUVBOztDQUVDLEdBQ0RDLE9BQU9DLE9BQU8sR0FBRztJQUNiSCxTQUFTRDtBQUNiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvZXh0ZXJuYWwuanM/NTBkMyJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLy8gbG9hZCB0aGUgZ2xvYmFsIG9iamVjdCBmaXJzdDpcbi8vIC0gaXQgc2hvdWxkIGJlIGJldHRlciBpbnRlZ3JhdGVkIGluIHRoZSBzeXN0ZW0gKHVuaGFuZGxlZFJlamVjdGlvbiBpbiBub2RlKVxuLy8gLSB0aGUgZW52aXJvbm1lbnQgbWF5IGhhdmUgYSBjdXN0b20gUHJvbWlzZSBpbXBsZW1lbnRhdGlvbiAoc2VlIHpvbmUuanMpXG52YXIgRVM2UHJvbWlzZSA9IG51bGw7XG5pZiAodHlwZW9mIFByb21pc2UgIT09IFwidW5kZWZpbmVkXCIpIHtcbiAgICBFUzZQcm9taXNlID0gUHJvbWlzZTtcbn0gZWxzZSB7XG4gICAgRVM2UHJvbWlzZSA9IHJlcXVpcmUoXCJsaWVcIik7XG59XG5cbi8qKlxuICogTGV0IHRoZSB1c2VyIHVzZS9jaGFuZ2Ugc29tZSBpbXBsZW1lbnRhdGlvbnMuXG4gKi9cbm1vZHVsZS5leHBvcnRzID0ge1xuICAgIFByb21pc2U6IEVTNlByb21pc2Vcbn07XG4iXSwibmFtZXMiOlsiRVM2UHJvbWlzZSIsIlByb21pc2UiLCJyZXF1aXJlIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/external.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/flate.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/flate.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar USE_TYPEDARRAY = typeof Uint8Array !== \"undefined\" && typeof Uint16Array !== \"undefined\" && typeof Uint32Array !== \"undefined\";\nvar pako = __webpack_require__(/*! pako */ \"(ssr)/./node_modules/pako/index.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar ARRAY_TYPE = USE_TYPEDARRAY ? \"uint8array\" : \"array\";\nexports.magic = \"\\b\\x00\";\n/**\n * Create a worker that uses pako to inflate/deflate.\n * @constructor\n * @param {String} action the name of the pako function to call : either \"Deflate\" or \"Inflate\".\n * @param {Object} options the options to use when (de)compressing.\n */ function FlateWorker(action, options) {\n    GenericWorker.call(this, \"FlateWorker/\" + action);\n    this._pako = null;\n    this._pakoAction = action;\n    this._pakoOptions = options;\n    // the `meta` object from the last chunk received\n    // this allow this worker to pass around metadata\n    this.meta = {};\n}\nutils.inherits(FlateWorker, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ FlateWorker.prototype.processChunk = function(chunk) {\n    this.meta = chunk.meta;\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push(utils.transformTo(ARRAY_TYPE, chunk.data), false);\n};\n/**\n * @see GenericWorker.flush\n */ FlateWorker.prototype.flush = function() {\n    GenericWorker.prototype.flush.call(this);\n    if (this._pako === null) {\n        this._createPako();\n    }\n    this._pako.push([], true);\n};\n/**\n * @see GenericWorker.cleanUp\n */ FlateWorker.prototype.cleanUp = function() {\n    GenericWorker.prototype.cleanUp.call(this);\n    this._pako = null;\n};\n/**\n * Create the _pako object.\n * TODO: lazy-loading this object isn't the best solution but it's the\n * quickest. The best solution is to lazy-load the worker list. See also the\n * issue #446.\n */ FlateWorker.prototype._createPako = function() {\n    this._pako = new pako[this._pakoAction]({\n        raw: true,\n        level: this._pakoOptions.level || -1 // default compression\n    });\n    var self = this;\n    this._pako.onData = function(data) {\n        self.push({\n            data: data,\n            meta: self.meta\n        });\n    };\n};\nexports.compressWorker = function(compressionOptions) {\n    return new FlateWorker(\"Deflate\", compressionOptions);\n};\nexports.uncompressWorker = function() {\n    return new FlateWorker(\"Inflate\", {});\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/flate.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/generate/ZipFileWorker.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utf8 = __webpack_require__(/*! ../utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar signature = __webpack_require__(/*! ../signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\n/**\n * Transform an integer into a string in hexadecimal.\n * @private\n * @param {number} dec the number to convert.\n * @param {number} bytes the number of bytes to generate.\n * @returns {string} the result.\n */ var decToHex = function(dec, bytes) {\n    var hex = \"\", i;\n    for(i = 0; i < bytes; i++){\n        hex += String.fromCharCode(dec & 0xff);\n        dec = dec >>> 8;\n    }\n    return hex;\n};\n/**\n * Generate the UNIX part of the external file attributes.\n * @param {Object} unixPermissions the unix permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * adapted from http://unix.stackexchange.com/questions/14705/the-zip-formats-external-file-attribute :\n *\n * TTTTsstrwxrwxrwx0000000000ADVSHR\n * ^^^^____________________________ file type, see zipinfo.c (UNX_*)\n *     ^^^_________________________ setuid, setgid, sticky\n *        ^^^^^^^^^________________ permissions\n *                 ^^^^^^^^^^______ not used ?\n *                           ^^^^^^ DOS attribute bits : Archive, Directory, Volume label, System file, Hidden, Read only\n */ var generateUnixExternalFileAttr = function(unixPermissions, isDir) {\n    var result = unixPermissions;\n    if (!unixPermissions) {\n        // I can't use octal values in strict mode, hence the hexa.\n        //  040775 => 0x41fd\n        // 0100664 => 0x81b4\n        result = isDir ? 0x41fd : 0x81b4;\n    }\n    return (result & 0xFFFF) << 16;\n};\n/**\n * Generate the DOS part of the external file attributes.\n * @param {Object} dosPermissions the dos permissions or null.\n * @param {Boolean} isDir true if the entry is a directory, false otherwise.\n * @return {Number} a 32 bit integer.\n *\n * Bit 0     Read-Only\n * Bit 1     Hidden\n * Bit 2     System\n * Bit 3     Volume Label\n * Bit 4     Directory\n * Bit 5     Archive\n */ var generateDosExternalFileAttr = function(dosPermissions) {\n    // the dir flag is already set for compatibility\n    return (dosPermissions || 0) & 0x3F;\n};\n/**\n * Generate the various parts used in the construction of the final zip file.\n * @param {Object} streamInfo the hash with information about the compressed file.\n * @param {Boolean} streamedContent is the content streamed ?\n * @param {Boolean} streamingEnded is the stream finished ?\n * @param {number} offset the current offset from the start of the zip file.\n * @param {String} platform let's pretend we are this platform (change platform dependents fields)\n * @param {Function} encodeFileName the function to encode the file name / comment.\n * @return {Object} the zip parts.\n */ var generateZipParts = function(streamInfo, streamedContent, streamingEnded, offset, platform, encodeFileName) {\n    var file = streamInfo[\"file\"], compression = streamInfo[\"compression\"], useCustomEncoding = encodeFileName !== utf8.utf8encode, encodedFileName = utils.transformTo(\"string\", encodeFileName(file.name)), utfEncodedFileName = utils.transformTo(\"string\", utf8.utf8encode(file.name)), comment = file.comment, encodedComment = utils.transformTo(\"string\", encodeFileName(comment)), utfEncodedComment = utils.transformTo(\"string\", utf8.utf8encode(comment)), useUTF8ForFileName = utfEncodedFileName.length !== file.name.length, useUTF8ForComment = utfEncodedComment.length !== comment.length, dosTime, dosDate, extraFields = \"\", unicodePathExtraField = \"\", unicodeCommentExtraField = \"\", dir = file.dir, date = file.date;\n    var dataInfo = {\n        crc32: 0,\n        compressedSize: 0,\n        uncompressedSize: 0\n    };\n    // if the content is streamed, the sizes/crc32 are only available AFTER\n    // the end of the stream.\n    if (!streamedContent || streamingEnded) {\n        dataInfo.crc32 = streamInfo[\"crc32\"];\n        dataInfo.compressedSize = streamInfo[\"compressedSize\"];\n        dataInfo.uncompressedSize = streamInfo[\"uncompressedSize\"];\n    }\n    var bitflag = 0;\n    if (streamedContent) {\n        // Bit 3: the sizes/crc32 are set to zero in the local header.\n        // The correct values are put in the data descriptor immediately\n        // following the compressed data.\n        bitflag |= 0x0008;\n    }\n    if (!useCustomEncoding && (useUTF8ForFileName || useUTF8ForComment)) {\n        // Bit 11: Language encoding flag (EFS).\n        bitflag |= 0x0800;\n    }\n    var extFileAttr = 0;\n    var versionMadeBy = 0;\n    if (dir) {\n        // dos or unix, we set the dos dir flag\n        extFileAttr |= 0x00010;\n    }\n    if (platform === \"UNIX\") {\n        versionMadeBy = 0x031E; // UNIX, version 3.0\n        extFileAttr |= generateUnixExternalFileAttr(file.unixPermissions, dir);\n    } else {\n        versionMadeBy = 0x0014; // DOS, version 2.0\n        extFileAttr |= generateDosExternalFileAttr(file.dosPermissions, dir);\n    }\n    // date\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/52/13.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/65/16.html\n    // @see http://www.delorie.com/djgpp/doc/rbinter/it/66/16.html\n    dosTime = date.getUTCHours();\n    dosTime = dosTime << 6;\n    dosTime = dosTime | date.getUTCMinutes();\n    dosTime = dosTime << 5;\n    dosTime = dosTime | date.getUTCSeconds() / 2;\n    dosDate = date.getUTCFullYear() - 1980;\n    dosDate = dosDate << 4;\n    dosDate = dosDate | date.getUTCMonth() + 1;\n    dosDate = dosDate << 5;\n    dosDate = dosDate | date.getUTCDate();\n    if (useUTF8ForFileName) {\n        // set the unicode path extra field. unzip needs at least one extra\n        // field to correctly handle unicode path, so using the path is as good\n        // as any other information. This could improve the situation with\n        // other archive managers too.\n        // This field is usually used without the utf8 flag, with a non\n        // unicode path in the header (winrar, winzip). This helps (a bit)\n        // with the messy Windows' default compressed folders feature but\n        // breaks on p7zip which doesn't seek the unicode path extra field.\n        // So for now, UTF-8 everywhere !\n        unicodePathExtraField = // Version\n        decToHex(1, 1) + // NameCRC32\n        decToHex(crc32(encodedFileName), 4) + // UnicodeName\n        utfEncodedFileName;\n        extraFields += // Info-ZIP Unicode Path Extra Field\n        \"up\" + // size\n        decToHex(unicodePathExtraField.length, 2) + // content\n        unicodePathExtraField;\n    }\n    if (useUTF8ForComment) {\n        unicodeCommentExtraField = // Version\n        decToHex(1, 1) + // CommentCRC32\n        decToHex(crc32(encodedComment), 4) + // UnicodeName\n        utfEncodedComment;\n        extraFields += // Info-ZIP Unicode Path Extra Field\n        \"uc\" + // size\n        decToHex(unicodeCommentExtraField.length, 2) + // content\n        unicodeCommentExtraField;\n    }\n    var header = \"\";\n    // version needed to extract\n    header += \"\\n\\x00\";\n    // general purpose bit flag\n    header += decToHex(bitflag, 2);\n    // compression method\n    header += compression.magic;\n    // last mod file time\n    header += decToHex(dosTime, 2);\n    // last mod file date\n    header += decToHex(dosDate, 2);\n    // crc-32\n    header += decToHex(dataInfo.crc32, 4);\n    // compressed size\n    header += decToHex(dataInfo.compressedSize, 4);\n    // uncompressed size\n    header += decToHex(dataInfo.uncompressedSize, 4);\n    // file name length\n    header += decToHex(encodedFileName.length, 2);\n    // extra field length\n    header += decToHex(extraFields.length, 2);\n    var fileRecord = signature.LOCAL_FILE_HEADER + header + encodedFileName + extraFields;\n    var dirRecord = signature.CENTRAL_FILE_HEADER + // version made by (00: DOS)\n    decToHex(versionMadeBy, 2) + // file header (common to file and central directory)\n    header + // file comment length\n    decToHex(encodedComment.length, 2) + // disk number start\n    \"\\x00\\x00\" + // internal file attributes TODO\n    \"\\x00\\x00\" + // external file attributes\n    decToHex(extFileAttr, 4) + // relative offset of local header\n    decToHex(offset, 4) + // file name\n    encodedFileName + // extra field\n    extraFields + // file comment\n    encodedComment;\n    return {\n        fileRecord: fileRecord,\n        dirRecord: dirRecord\n    };\n};\n/**\n * Generate the EOCD record.\n * @param {Number} entriesCount the number of entries in the zip file.\n * @param {Number} centralDirLength the length (in bytes) of the central dir.\n * @param {Number} localDirLength the length (in bytes) of the local dir.\n * @param {String} comment the zip file comment as a binary string.\n * @param {Function} encodeFileName the function to encode the comment.\n * @return {String} the EOCD record.\n */ var generateCentralDirectoryEnd = function(entriesCount, centralDirLength, localDirLength, comment, encodeFileName) {\n    var dirEnd = \"\";\n    var encodedComment = utils.transformTo(\"string\", encodeFileName(comment));\n    // end of central dir signature\n    dirEnd = signature.CENTRAL_DIRECTORY_END + // number of this disk\n    \"\\x00\\x00\" + // number of the disk with the start of the central directory\n    \"\\x00\\x00\" + // total number of entries in the central directory on this disk\n    decToHex(entriesCount, 2) + // total number of entries in the central directory\n    decToHex(entriesCount, 2) + // size of the central directory   4 bytes\n    decToHex(centralDirLength, 4) + // offset of start of central directory with respect to the starting disk number\n    decToHex(localDirLength, 4) + // .ZIP file comment length\n    decToHex(encodedComment.length, 2) + // .ZIP file comment\n    encodedComment;\n    return dirEnd;\n};\n/**\n * Generate data descriptors for a file entry.\n * @param {Object} streamInfo the hash generated by a worker, containing information\n * on the file entry.\n * @return {String} the data descriptors.\n */ var generateDataDescriptors = function(streamInfo) {\n    var descriptor = \"\";\n    descriptor = signature.DATA_DESCRIPTOR + // crc-32                          4 bytes\n    decToHex(streamInfo[\"crc32\"], 4) + // compressed size                 4 bytes\n    decToHex(streamInfo[\"compressedSize\"], 4) + // uncompressed size               4 bytes\n    decToHex(streamInfo[\"uncompressedSize\"], 4);\n    return descriptor;\n};\n/**\n * A worker to concatenate other workers to create a zip file.\n * @param {Boolean} streamFiles `true` to stream the content of the files,\n * `false` to accumulate it.\n * @param {String} comment the comment to use.\n * @param {String} platform the platform to use, \"UNIX\" or \"DOS\".\n * @param {Function} encodeFileName the function to encode file names and comments.\n */ function ZipFileWorker(streamFiles, comment, platform, encodeFileName) {\n    GenericWorker.call(this, \"ZipFileWorker\");\n    // The number of bytes written so far. This doesn't count accumulated chunks.\n    this.bytesWritten = 0;\n    // The comment of the zip file\n    this.zipComment = comment;\n    // The platform \"generating\" the zip file.\n    this.zipPlatform = platform;\n    // the function to encode file names and comments.\n    this.encodeFileName = encodeFileName;\n    // Should we stream the content of the files ?\n    this.streamFiles = streamFiles;\n    // If `streamFiles` is false, we will need to accumulate the content of the\n    // files to calculate sizes / crc32 (and write them *before* the content).\n    // This boolean indicates if we are accumulating chunks (it will change a lot\n    // during the lifetime of this worker).\n    this.accumulate = false;\n    // The buffer receiving chunks when accumulating content.\n    this.contentBuffer = [];\n    // The list of generated directory records.\n    this.dirRecords = [];\n    // The offset (in bytes) from the beginning of the zip file for the current source.\n    this.currentSourceOffset = 0;\n    // The total number of entries in this zip file.\n    this.entriesCount = 0;\n    // the name of the file currently being added, null when handling the end of the zip file.\n    // Used for the emitted metadata.\n    this.currentFile = null;\n    this._sources = [];\n}\nutils.inherits(ZipFileWorker, GenericWorker);\n/**\n * @see GenericWorker.push\n */ ZipFileWorker.prototype.push = function(chunk) {\n    var currentFilePercent = chunk.meta.percent || 0;\n    var entriesCount = this.entriesCount;\n    var remainingFiles = this._sources.length;\n    if (this.accumulate) {\n        this.contentBuffer.push(chunk);\n    } else {\n        this.bytesWritten += chunk.data.length;\n        GenericWorker.prototype.push.call(this, {\n            data: chunk.data,\n            meta: {\n                currentFile: this.currentFile,\n                percent: entriesCount ? (currentFilePercent + 100 * (entriesCount - remainingFiles - 1)) / entriesCount : 100\n            }\n        });\n    }\n};\n/**\n * The worker started a new source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the new source.\n */ ZipFileWorker.prototype.openedSource = function(streamInfo) {\n    this.currentSourceOffset = this.bytesWritten;\n    this.currentFile = streamInfo[\"file\"].name;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    // don't stream folders (because they don't have any content)\n    if (streamedContent) {\n        var record = generateZipParts(streamInfo, streamedContent, false, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n        this.push({\n            data: record.fileRecord,\n            meta: {\n                percent: 0\n            }\n        });\n    } else {\n        // we need to wait for the whole file before pushing anything\n        this.accumulate = true;\n    }\n};\n/**\n * The worker finished a source (an other worker).\n * @param {Object} streamInfo the streamInfo object from the finished source.\n */ ZipFileWorker.prototype.closedSource = function(streamInfo) {\n    this.accumulate = false;\n    var streamedContent = this.streamFiles && !streamInfo[\"file\"].dir;\n    var record = generateZipParts(streamInfo, streamedContent, true, this.currentSourceOffset, this.zipPlatform, this.encodeFileName);\n    this.dirRecords.push(record.dirRecord);\n    if (streamedContent) {\n        // after the streamed file, we put data descriptors\n        this.push({\n            data: generateDataDescriptors(streamInfo),\n            meta: {\n                percent: 100\n            }\n        });\n    } else {\n        // the content wasn't streamed, we need to push everything now\n        // first the file record, then the content\n        this.push({\n            data: record.fileRecord,\n            meta: {\n                percent: 0\n            }\n        });\n        while(this.contentBuffer.length){\n            this.push(this.contentBuffer.shift());\n        }\n    }\n    this.currentFile = null;\n};\n/**\n * @see GenericWorker.flush\n */ ZipFileWorker.prototype.flush = function() {\n    var localDirLength = this.bytesWritten;\n    for(var i = 0; i < this.dirRecords.length; i++){\n        this.push({\n            data: this.dirRecords[i],\n            meta: {\n                percent: 100\n            }\n        });\n    }\n    var centralDirLength = this.bytesWritten - localDirLength;\n    var dirEnd = generateCentralDirectoryEnd(this.dirRecords.length, centralDirLength, localDirLength, this.zipComment, this.encodeFileName);\n    this.push({\n        data: dirEnd,\n        meta: {\n            percent: 100\n        }\n    });\n};\n/**\n * Prepare the next source to be read.\n */ ZipFileWorker.prototype.prepareNextSource = function() {\n    this.previous = this._sources.shift();\n    this.openedSource(this.previous.streamInfo);\n    if (this.isPaused) {\n        this.previous.pause();\n    } else {\n        this.previous.resume();\n    }\n};\n/**\n * @see GenericWorker.registerPrevious\n */ ZipFileWorker.prototype.registerPrevious = function(previous) {\n    this._sources.push(previous);\n    var self = this;\n    previous.on(\"data\", function(chunk) {\n        self.processChunk(chunk);\n    });\n    previous.on(\"end\", function() {\n        self.closedSource(self.previous.streamInfo);\n        if (self._sources.length) {\n            self.prepareNextSource();\n        } else {\n            self.end();\n        }\n    });\n    previous.on(\"error\", function(e) {\n        self.error(e);\n    });\n    return this;\n};\n/**\n * @see GenericWorker.resume\n */ ZipFileWorker.prototype.resume = function() {\n    if (!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n    if (!this.previous && this._sources.length) {\n        this.prepareNextSource();\n        return true;\n    }\n    if (!this.previous && !this._sources.length && !this.generatedError) {\n        this.end();\n        return true;\n    }\n};\n/**\n * @see GenericWorker.error\n */ ZipFileWorker.prototype.error = function(e) {\n    var sources = this._sources;\n    if (!GenericWorker.prototype.error.call(this, e)) {\n        return false;\n    }\n    for(var i = 0; i < sources.length; i++){\n        try {\n            sources[i].error(e);\n        } catch (e) {\n        // the `error` exploded, nothing to do\n        }\n    }\n    return true;\n};\n/**\n * @see GenericWorker.lock\n */ ZipFileWorker.prototype.lock = function() {\n    GenericWorker.prototype.lock.call(this);\n    var sources = this._sources;\n    for(var i = 0; i < sources.length; i++){\n        sources[i].lock();\n    }\n};\nmodule.exports = ZipFileWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/generate/index.js":
/*!**************************************************!*\
  !*** ./node_modules/jszip/lib/generate/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar compressions = __webpack_require__(/*! ../compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar ZipFileWorker = __webpack_require__(/*! ./ZipFileWorker */ \"(ssr)/./node_modules/jszip/lib/generate/ZipFileWorker.js\");\n/**\n * Find the compression to use.\n * @param {String} fileCompression the compression defined at the file level, if any.\n * @param {String} zipCompression the compression defined at the load() level.\n * @return {Object} the compression object to use.\n */ var getCompression = function(fileCompression, zipCompression) {\n    var compressionName = fileCompression || zipCompression;\n    var compression = compressions[compressionName];\n    if (!compression) {\n        throw new Error(compressionName + \" is not a valid compression method !\");\n    }\n    return compression;\n};\n/**\n * Create a worker to generate a zip file.\n * @param {JSZip} zip the JSZip instance at the right root level.\n * @param {Object} options to generate the zip file.\n * @param {String} comment the comment to use.\n */ exports.generateWorker = function(zip, options, comment) {\n    var zipFileWorker = new ZipFileWorker(options.streamFiles, comment, options.platform, options.encodeFileName);\n    var entriesCount = 0;\n    try {\n        zip.forEach(function(relativePath, file) {\n            entriesCount++;\n            var compression = getCompression(file.options.compression, options.compression);\n            var compressionOptions = file.options.compressionOptions || options.compressionOptions || {};\n            var dir = file.dir, date = file.date;\n            file._compressWorker(compression, compressionOptions).withStreamInfo(\"file\", {\n                name: relativePath,\n                dir: dir,\n                date: date,\n                comment: file.comment || \"\",\n                unixPermissions: file.unixPermissions,\n                dosPermissions: file.dosPermissions\n            }).pipe(zipFileWorker);\n        });\n        zipFileWorker.entriesCount = entriesCount;\n    } catch (e) {\n        zipFileWorker.error(e);\n    }\n    return zipFileWorker;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/generate/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/index.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/index.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n/**\n * Representation a of zip file in js\n * @constructor\n */ function JSZip() {\n    // if this constructor is used without `new`, it adds `new` before itself:\n    if (!(this instanceof JSZip)) {\n        return new JSZip();\n    }\n    if (arguments.length) {\n        throw new Error(\"The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.\");\n    }\n    // object containing the files :\n    // {\n    //   \"folder/\" : {...},\n    //   \"folder/data.txt\" : {...}\n    // }\n    // NOTE: we use a null prototype because we do not\n    // want filenames like \"toString\" coming from a zip file\n    // to overwrite methods and attributes in a normal Object.\n    this.files = Object.create(null);\n    this.comment = null;\n    // Where we are in the hierarchy\n    this.root = \"\";\n    this.clone = function() {\n        var newObj = new JSZip();\n        for(var i in this){\n            if (typeof this[i] !== \"function\") {\n                newObj[i] = this[i];\n            }\n        }\n        return newObj;\n    };\n}\nJSZip.prototype = __webpack_require__(/*! ./object */ \"(ssr)/./node_modules/jszip/lib/object.js\");\nJSZip.prototype.loadAsync = __webpack_require__(/*! ./load */ \"(ssr)/./node_modules/jszip/lib/load.js\");\nJSZip.support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nJSZip.defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\n// TODO find a better way to handle this version,\n// a require('package.json').version doesn't work with webpack, see #327\nJSZip.version = \"3.10.1\";\nJSZip.loadAsync = function(content, options) {\n    return new JSZip().loadAsync(content, options);\n};\nJSZip.external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nmodule.exports = JSZip;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWI7OztDQUdDLEdBQ0QsU0FBU0E7SUFDTCwwRUFBMEU7SUFDMUUsSUFBRyxDQUFFLEtBQUksWUFBWUEsS0FBSSxHQUFJO1FBQ3pCLE9BQU8sSUFBSUE7SUFDZjtJQUVBLElBQUdDLFVBQVVDLE1BQU0sRUFBRTtRQUNqQixNQUFNLElBQUlDLE1BQU07SUFDcEI7SUFFQSxnQ0FBZ0M7SUFDaEMsSUFBSTtJQUNKLHVCQUF1QjtJQUN2Qiw4QkFBOEI7SUFDOUIsSUFBSTtJQUNKLGtEQUFrRDtJQUNsRCx3REFBd0Q7SUFDeEQsMERBQTBEO0lBQzFELElBQUksQ0FBQ0MsS0FBSyxHQUFHQyxPQUFPQyxNQUFNLENBQUM7SUFFM0IsSUFBSSxDQUFDQyxPQUFPLEdBQUc7SUFFZixnQ0FBZ0M7SUFDaEMsSUFBSSxDQUFDQyxJQUFJLEdBQUc7SUFDWixJQUFJLENBQUNDLEtBQUssR0FBRztRQUNULElBQUlDLFNBQVMsSUFBSVY7UUFDakIsSUFBSyxJQUFJVyxLQUFLLElBQUksQ0FBRTtZQUNoQixJQUFJLE9BQU8sSUFBSSxDQUFDQSxFQUFFLEtBQUssWUFBWTtnQkFDL0JELE1BQU0sQ0FBQ0MsRUFBRSxHQUFHLElBQUksQ0FBQ0EsRUFBRTtZQUN2QjtRQUNKO1FBQ0EsT0FBT0Q7SUFDWDtBQUNKO0FBQ0FWLE1BQU1ZLFNBQVMsR0FBR0MsbUJBQU9BLENBQUMsMERBQVU7QUFDcENiLE1BQU1ZLFNBQVMsQ0FBQ0UsU0FBUyxHQUFHRCxtQkFBT0EsQ0FBQyxzREFBUTtBQUM1Q2IsTUFBTWUsT0FBTyxHQUFHRixtQkFBT0EsQ0FBQyw0REFBVztBQUNuQ2IsTUFBTWdCLFFBQVEsR0FBR0gsbUJBQU9BLENBQUMsOERBQVk7QUFFckMsaURBQWlEO0FBQ2pELHdFQUF3RTtBQUN4RWIsTUFBTWlCLE9BQU8sR0FBRztBQUVoQmpCLE1BQU1jLFNBQVMsR0FBRyxTQUFVSSxPQUFPLEVBQUVDLE9BQU87SUFDeEMsT0FBTyxJQUFJbkIsUUFBUWMsU0FBUyxDQUFDSSxTQUFTQztBQUMxQztBQUVBbkIsTUFBTW9CLFFBQVEsR0FBR1AsbUJBQU9BLENBQUMsOERBQVk7QUFDckNRLE9BQU9DLE9BQU8sR0FBR3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvaW5kZXguanM/NGVmNSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxuLyoqXG4gKiBSZXByZXNlbnRhdGlvbiBhIG9mIHppcCBmaWxlIGluIGpzXG4gKiBAY29uc3RydWN0b3JcbiAqL1xuZnVuY3Rpb24gSlNaaXAoKSB7XG4gICAgLy8gaWYgdGhpcyBjb25zdHJ1Y3RvciBpcyB1c2VkIHdpdGhvdXQgYG5ld2AsIGl0IGFkZHMgYG5ld2AgYmVmb3JlIGl0c2VsZjpcbiAgICBpZighKHRoaXMgaW5zdGFuY2VvZiBKU1ppcCkpIHtcbiAgICAgICAgcmV0dXJuIG5ldyBKU1ppcCgpO1xuICAgIH1cblxuICAgIGlmKGFyZ3VtZW50cy5sZW5ndGgpIHtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhlIGNvbnN0cnVjdG9yIHdpdGggcGFyYW1ldGVycyBoYXMgYmVlbiByZW1vdmVkIGluIEpTWmlwIDMuMCwgcGxlYXNlIGNoZWNrIHRoZSB1cGdyYWRlIGd1aWRlLlwiKTtcbiAgICB9XG5cbiAgICAvLyBvYmplY3QgY29udGFpbmluZyB0aGUgZmlsZXMgOlxuICAgIC8vIHtcbiAgICAvLyAgIFwiZm9sZGVyL1wiIDogey4uLn0sXG4gICAgLy8gICBcImZvbGRlci9kYXRhLnR4dFwiIDogey4uLn1cbiAgICAvLyB9XG4gICAgLy8gTk9URTogd2UgdXNlIGEgbnVsbCBwcm90b3R5cGUgYmVjYXVzZSB3ZSBkbyBub3RcbiAgICAvLyB3YW50IGZpbGVuYW1lcyBsaWtlIFwidG9TdHJpbmdcIiBjb21pbmcgZnJvbSBhIHppcCBmaWxlXG4gICAgLy8gdG8gb3ZlcndyaXRlIG1ldGhvZHMgYW5kIGF0dHJpYnV0ZXMgaW4gYSBub3JtYWwgT2JqZWN0LlxuICAgIHRoaXMuZmlsZXMgPSBPYmplY3QuY3JlYXRlKG51bGwpO1xuXG4gICAgdGhpcy5jb21tZW50ID0gbnVsbDtcblxuICAgIC8vIFdoZXJlIHdlIGFyZSBpbiB0aGUgaGllcmFyY2h5XG4gICAgdGhpcy5yb290ID0gXCJcIjtcbiAgICB0aGlzLmNsb25lID0gZnVuY3Rpb24oKSB7XG4gICAgICAgIHZhciBuZXdPYmogPSBuZXcgSlNaaXAoKTtcbiAgICAgICAgZm9yICh2YXIgaSBpbiB0aGlzKSB7XG4gICAgICAgICAgICBpZiAodHlwZW9mIHRoaXNbaV0gIT09IFwiZnVuY3Rpb25cIikge1xuICAgICAgICAgICAgICAgIG5ld09ialtpXSA9IHRoaXNbaV07XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIG5ld09iajtcbiAgICB9O1xufVxuSlNaaXAucHJvdG90eXBlID0gcmVxdWlyZShcIi4vb2JqZWN0XCIpO1xuSlNaaXAucHJvdG90eXBlLmxvYWRBc3luYyA9IHJlcXVpcmUoXCIuL2xvYWRcIik7XG5KU1ppcC5zdXBwb3J0ID0gcmVxdWlyZShcIi4vc3VwcG9ydFwiKTtcbkpTWmlwLmRlZmF1bHRzID0gcmVxdWlyZShcIi4vZGVmYXVsdHNcIik7XG5cbi8vIFRPRE8gZmluZCBhIGJldHRlciB3YXkgdG8gaGFuZGxlIHRoaXMgdmVyc2lvbixcbi8vIGEgcmVxdWlyZSgncGFja2FnZS5qc29uJykudmVyc2lvbiBkb2Vzbid0IHdvcmsgd2l0aCB3ZWJwYWNrLCBzZWUgIzMyN1xuSlNaaXAudmVyc2lvbiA9IFwiMy4xMC4xXCI7XG5cbkpTWmlwLmxvYWRBc3luYyA9IGZ1bmN0aW9uIChjb250ZW50LCBvcHRpb25zKSB7XG4gICAgcmV0dXJuIG5ldyBKU1ppcCgpLmxvYWRBc3luYyhjb250ZW50LCBvcHRpb25zKTtcbn07XG5cbkpTWmlwLmV4dGVybmFsID0gcmVxdWlyZShcIi4vZXh0ZXJuYWxcIik7XG5tb2R1bGUuZXhwb3J0cyA9IEpTWmlwO1xuIl0sIm5hbWVzIjpbIkpTWmlwIiwiYXJndW1lbnRzIiwibGVuZ3RoIiwiRXJyb3IiLCJmaWxlcyIsIk9iamVjdCIsImNyZWF0ZSIsImNvbW1lbnQiLCJyb290IiwiY2xvbmUiLCJuZXdPYmoiLCJpIiwicHJvdG90eXBlIiwicmVxdWlyZSIsImxvYWRBc3luYyIsInN1cHBvcnQiLCJkZWZhdWx0cyIsInZlcnNpb24iLCJjb250ZW50Iiwib3B0aW9ucyIsImV4dGVybmFsIiwibW9kdWxlIiwiZXhwb3J0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/load.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/load.js ***!
  \****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar ZipEntries = __webpack_require__(/*! ./zipEntries */ \"(ssr)/./node_modules/jszip/lib/zipEntries.js\");\nvar Crc32Probe = __webpack_require__(/*! ./stream/Crc32Probe */ \"(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\n/**\n * Check the CRC32 of an entry.\n * @param {ZipEntry} zipEntry the zip entry to check.\n * @return {Promise} the result.\n */ function checkEntryCRC32(zipEntry) {\n    return new external.Promise(function(resolve, reject) {\n        var worker = zipEntry.decompressed.getContentWorker().pipe(new Crc32Probe());\n        worker.on(\"error\", function(e) {\n            reject(e);\n        }).on(\"end\", function() {\n            if (worker.streamInfo.crc32 !== zipEntry.decompressed.crc32) {\n                reject(new Error(\"Corrupted zip : CRC32 mismatch\"));\n            } else {\n                resolve();\n            }\n        }).resume();\n    });\n}\nmodule.exports = function(data, options) {\n    var zip = this;\n    options = utils.extend(options || {}, {\n        base64: false,\n        checkCRC32: false,\n        optimizedBinaryString: false,\n        createFolders: false,\n        decodeFileName: utf8.utf8decode\n    });\n    if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        return external.Promise.reject(new Error(\"JSZip can't accept a stream when loading a zip file.\"));\n    }\n    return utils.prepareContent(\"the loaded zip file\", data, true, options.optimizedBinaryString, options.base64).then(function(data) {\n        var zipEntries = new ZipEntries(options);\n        zipEntries.load(data);\n        return zipEntries;\n    }).then(function checkCRC32(zipEntries) {\n        var promises = [\n            external.Promise.resolve(zipEntries)\n        ];\n        var files = zipEntries.files;\n        if (options.checkCRC32) {\n            for(var i = 0; i < files.length; i++){\n                promises.push(checkEntryCRC32(files[i]));\n            }\n        }\n        return external.Promise.all(promises);\n    }).then(function addFiles(results) {\n        var zipEntries = results.shift();\n        var files = zipEntries.files;\n        for(var i = 0; i < files.length; i++){\n            var input = files[i];\n            var unsafeName = input.fileNameStr;\n            var safeName = utils.resolve(input.fileNameStr);\n            zip.file(safeName, input.decompressed, {\n                binary: true,\n                optimizedBinaryString: true,\n                date: input.date,\n                dir: input.dir,\n                comment: input.fileCommentStr.length ? input.fileCommentStr : null,\n                unixPermissions: input.unixPermissions,\n                dosPermissions: input.dosPermissions,\n                createFolders: options.createFolders\n            });\n            if (!input.dir) {\n                zip.file(safeName).unsafeOriginalName = unsafeName;\n            }\n        }\n        if (zipEntries.zipComment.length) {\n            zip.comment = zipEntries.zipComment;\n        }\n        return zip;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/load.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejsUtils.js":
/*!***********************************************!*\
  !*** ./node_modules/jszip/lib/nodejsUtils.js ***!
  \***********************************************/
/***/ ((module) => {

eval("\nmodule.exports = {\n    /**\n     * True if this is running in Nodejs, will be undefined in a browser.\n     * In a browser, browserify won't include this file and the whole module\n     * will be resolved an empty object.\n     */ isNode: typeof Buffer !== \"undefined\",\n    /**\n     * Create a new nodejs Buffer from an existing content.\n     * @param {Object} data the data to pass to the constructor.\n     * @param {String} encoding the encoding to use.\n     * @return {Buffer} a new Buffer.\n     */ newBufferFrom: function(data, encoding) {\n        if (Buffer.from && Buffer.from !== Uint8Array.from) {\n            return Buffer.from(data, encoding);\n        } else {\n            if (typeof data === \"number\") {\n                // Safeguard for old Node.js versions. On newer versions,\n                // Buffer.from(number) / Buffer(number, encoding) already throw.\n                throw new Error('The \"data\" argument must not be a number');\n            }\n            return new Buffer(data, encoding);\n        }\n    },\n    /**\n     * Create a new nodejs Buffer with the specified size.\n     * @param {Integer} size the size of the buffer.\n     * @return {Buffer} a new Buffer.\n     */ allocBuffer: function(size) {\n        if (Buffer.alloc) {\n            return Buffer.alloc(size);\n        } else {\n            var buf = new Buffer(size);\n            buf.fill(0);\n            return buf;\n        }\n    },\n    /**\n     * Find out if an object is a Buffer.\n     * @param {Object} b the object to test.\n     * @return {Boolean} true if the object is a Buffer, false otherwise.\n     */ isBuffer: function(b) {\n        return Buffer.isBuffer(b);\n    },\n    isStream: function(obj) {\n        return obj && typeof obj.on === \"function\" && typeof obj.pause === \"function\" && typeof obj.resume === \"function\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejsUtils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js":
/*!*******************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js ***!
  \*******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ../stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n/**\n * A worker that use a nodejs stream as source.\n * @constructor\n * @param {String} filename the name of the file entry for this stream.\n * @param {Readable} stream the nodejs stream.\n */ function NodejsStreamInputAdapter(filename, stream) {\n    GenericWorker.call(this, \"Nodejs stream input adapter for \" + filename);\n    this._upstreamEnded = false;\n    this._bindStream(stream);\n}\nutils.inherits(NodejsStreamInputAdapter, GenericWorker);\n/**\n * Prepare the stream and bind the callbacks on it.\n * Do this ASAP on node 0.10 ! A lazy binding doesn't always work.\n * @param {Stream} stream the nodejs stream to use.\n */ NodejsStreamInputAdapter.prototype._bindStream = function(stream) {\n    var self = this;\n    this._stream = stream;\n    stream.pause();\n    stream.on(\"data\", function(chunk) {\n        self.push({\n            data: chunk,\n            meta: {\n                percent: 0\n            }\n        });\n    }).on(\"error\", function(e) {\n        if (self.isPaused) {\n            this.generatedError = e;\n        } else {\n            self.error(e);\n        }\n    }).on(\"end\", function() {\n        if (self.isPaused) {\n            self._upstreamEnded = true;\n        } else {\n            self.end();\n        }\n    });\n};\nNodejsStreamInputAdapter.prototype.pause = function() {\n    if (!GenericWorker.prototype.pause.call(this)) {\n        return false;\n    }\n    this._stream.pause();\n    return true;\n};\nNodejsStreamInputAdapter.prototype.resume = function() {\n    if (!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n    if (this._upstreamEnded) {\n        this.end();\n    } else {\n        this._stream.resume();\n    }\n    return true;\n};\nmodule.exports = NodejsStreamInputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js":
/*!********************************************************************!*\
  !*** ./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js ***!
  \********************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Readable = (__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nutils.inherits(NodejsStreamOutputAdapter, Readable);\n/**\n* A nodejs stream using a worker as source.\n* @see the SourceWrapper in http://nodejs.org/api/stream.html\n* @constructor\n* @param {StreamHelper} helper the helper wrapping the worker\n* @param {Object} options the nodejs stream options\n* @param {Function} updateCb the update callback.\n*/ function NodejsStreamOutputAdapter(helper, options, updateCb) {\n    Readable.call(this, options);\n    this._helper = helper;\n    var self = this;\n    helper.on(\"data\", function(data, meta) {\n        if (!self.push(data)) {\n            self._helper.pause();\n        }\n        if (updateCb) {\n            updateCb(meta);\n        }\n    }).on(\"error\", function(e) {\n        self.emit(\"error\", e);\n    }).on(\"end\", function() {\n        self.push(null);\n    });\n}\nNodejsStreamOutputAdapter.prototype._read = function() {\n    this._helper.resume();\n};\nmodule.exports = NodejsStreamOutputAdapter;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/object.js":
/*!******************************************!*\
  !*** ./node_modules/jszip/lib/object.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar defaults = __webpack_require__(/*! ./defaults */ \"(ssr)/./node_modules/jszip/lib/defaults.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar ZipObject = __webpack_require__(/*! ./zipObject */ \"(ssr)/./node_modules/jszip/lib/zipObject.js\");\nvar generate = __webpack_require__(/*! ./generate */ \"(ssr)/./node_modules/jszip/lib/generate/index.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar NodejsStreamInputAdapter = __webpack_require__(/*! ./nodejs/NodejsStreamInputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamInputAdapter.js\");\n/**\n * Add a file in the current folder.\n * @private\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data of the file\n * @param {Object} originalOptions the options of the file\n * @return {Object} the new file.\n */ var fileAdd = function(name, data, originalOptions) {\n    // be sure sub folders exist\n    var dataType = utils.getTypeOf(data), parent;\n    /*\n     * Correct options.\n     */ var o = utils.extend(originalOptions || {}, defaults);\n    o.date = o.date || new Date();\n    if (o.compression !== null) {\n        o.compression = o.compression.toUpperCase();\n    }\n    if (typeof o.unixPermissions === \"string\") {\n        o.unixPermissions = parseInt(o.unixPermissions, 8);\n    }\n    // UNX_IFDIR  0040000 see zipinfo.c\n    if (o.unixPermissions && o.unixPermissions & 0x4000) {\n        o.dir = true;\n    }\n    // Bit 4    Directory\n    if (o.dosPermissions && o.dosPermissions & 0x0010) {\n        o.dir = true;\n    }\n    if (o.dir) {\n        name = forceTrailingSlash(name);\n    }\n    if (o.createFolders && (parent = parentFolder(name))) {\n        folderAdd.call(this, parent, true);\n    }\n    var isUnicodeString = dataType === \"string\" && o.binary === false && o.base64 === false;\n    if (!originalOptions || typeof originalOptions.binary === \"undefined\") {\n        o.binary = !isUnicodeString;\n    }\n    var isCompressedEmpty = data instanceof CompressedObject && data.uncompressedSize === 0;\n    if (isCompressedEmpty || o.dir || !data || data.length === 0) {\n        o.base64 = false;\n        o.binary = true;\n        data = \"\";\n        o.compression = \"STORE\";\n        dataType = \"string\";\n    }\n    /*\n     * Convert content to fit.\n     */ var zipObjectContent = null;\n    if (data instanceof CompressedObject || data instanceof GenericWorker) {\n        zipObjectContent = data;\n    } else if (nodejsUtils.isNode && nodejsUtils.isStream(data)) {\n        zipObjectContent = new NodejsStreamInputAdapter(name, data);\n    } else {\n        zipObjectContent = utils.prepareContent(name, data, o.binary, o.optimizedBinaryString, o.base64);\n    }\n    var object = new ZipObject(name, zipObjectContent, o);\n    this.files[name] = object;\n/*\n    TODO: we can't throw an exception because we have async promises\n    (we can have a promise of a Date() for example) but returning a\n    promise is useless because file(name, data) returns the JSZip\n    object for chaining. Should we break that to allow the user\n    to catch the error ?\n\n    return external.Promise.resolve(zipObjectContent)\n    .then(function () {\n        return object;\n    });\n    */ };\n/**\n * Find the parent folder of the path.\n * @private\n * @param {string} path the path to use\n * @return {string} the parent folder, or \"\"\n */ var parentFolder = function(path) {\n    if (path.slice(-1) === \"/\") {\n        path = path.substring(0, path.length - 1);\n    }\n    var lastSlash = path.lastIndexOf(\"/\");\n    return lastSlash > 0 ? path.substring(0, lastSlash) : \"\";\n};\n/**\n * Returns the path with a slash at the end.\n * @private\n * @param {String} path the path to check.\n * @return {String} the path with a trailing slash.\n */ var forceTrailingSlash = function(path) {\n    // Check the name ends with a /\n    if (path.slice(-1) !== \"/\") {\n        path += \"/\"; // IE doesn't like substr(-1)\n    }\n    return path;\n};\n/**\n * Add a (sub) folder in the current folder.\n * @private\n * @param {string} name the folder's name\n * @param {boolean=} [createFolders] If true, automatically create sub\n *  folders. Defaults to false.\n * @return {Object} the new folder.\n */ var folderAdd = function(name, createFolders) {\n    createFolders = typeof createFolders !== \"undefined\" ? createFolders : defaults.createFolders;\n    name = forceTrailingSlash(name);\n    // Does this folder already exist?\n    if (!this.files[name]) {\n        fileAdd.call(this, name, null, {\n            dir: true,\n            createFolders: createFolders\n        });\n    }\n    return this.files[name];\n};\n/**\n* Cross-window, cross-Node-context regular expression detection\n* @param  {Object}  object Anything\n* @return {Boolean}        true if the object is a regular expression,\n* false otherwise\n*/ function isRegExp(object) {\n    return Object.prototype.toString.call(object) === \"[object RegExp]\";\n}\n// return the actual prototype of JSZip\nvar out = {\n    /**\n     * @see loadAsync\n     */ load: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n    /**\n     * Call a callback function for each entry at this folder level.\n     * @param {Function} cb the callback function:\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     */ forEach: function(cb) {\n        var filename, relativePath, file;\n        // ignore warning about unwanted properties because this.files is a null prototype object\n        /* eslint-disable-next-line guard-for-in */ for(filename in this.files){\n            file = this.files[filename];\n            relativePath = filename.slice(this.root.length, filename.length);\n            if (relativePath && filename.slice(0, this.root.length) === this.root) {\n                cb(relativePath, file); // TODO reverse the parameters ? need to be clean AND consistent with the filter search fn...\n            }\n        }\n    },\n    /**\n     * Filter nested files/folders with the specified function.\n     * @param {Function} search the predicate to use :\n     * function (relativePath, file) {...}\n     * It takes 2 arguments : the relative path and the file.\n     * @return {Array} An array of matching elements.\n     */ filter: function(search) {\n        var result = [];\n        this.forEach(function(relativePath, entry) {\n            if (search(relativePath, entry)) {\n                result.push(entry);\n            }\n        });\n        return result;\n    },\n    /**\n     * Add a file to the zip file, or search a file.\n     * @param   {string|RegExp} name The name of the file to add (if data is defined),\n     * the name of the file to find (if no data) or a regex to match files.\n     * @param   {String|ArrayBuffer|Uint8Array|Buffer} data  The file data, either raw or base64 encoded\n     * @param   {Object} o     File options\n     * @return  {JSZip|Object|Array} this JSZip object (when adding a file),\n     * a file (when searching by string) or an array of files (when searching by regex).\n     */ file: function(name, data, o) {\n        if (arguments.length === 1) {\n            if (isRegExp(name)) {\n                var regexp = name;\n                return this.filter(function(relativePath, file) {\n                    return !file.dir && regexp.test(relativePath);\n                });\n            } else {\n                var obj = this.files[this.root + name];\n                if (obj && !obj.dir) {\n                    return obj;\n                } else {\n                    return null;\n                }\n            }\n        } else {\n            name = this.root + name;\n            fileAdd.call(this, name, data, o);\n        }\n        return this;\n    },\n    /**\n     * Add a directory to the zip file, or search.\n     * @param   {String|RegExp} arg The name of the directory to add, or a regex to search folders.\n     * @return  {JSZip} an object with the new directory as the root, or an array containing matching folders.\n     */ folder: function(arg) {\n        if (!arg) {\n            return this;\n        }\n        if (isRegExp(arg)) {\n            return this.filter(function(relativePath, file) {\n                return file.dir && arg.test(relativePath);\n            });\n        }\n        // else, name is a new folder\n        var name = this.root + arg;\n        var newFolder = folderAdd.call(this, name);\n        // Allow chaining by returning a new object with this folder as the root\n        var ret = this.clone();\n        ret.root = newFolder.name;\n        return ret;\n    },\n    /**\n     * Delete a file, or a directory and all sub-files, from the zip\n     * @param {string} name the name of the file to delete\n     * @return {JSZip} this JSZip object\n     */ remove: function(name) {\n        name = this.root + name;\n        var file = this.files[name];\n        if (!file) {\n            // Look for any folders\n            if (name.slice(-1) !== \"/\") {\n                name += \"/\";\n            }\n            file = this.files[name];\n        }\n        if (file && !file.dir) {\n            // file\n            delete this.files[name];\n        } else {\n            // maybe a folder, delete recursively\n            var kids = this.filter(function(relativePath, file) {\n                return file.name.slice(0, name.length) === name;\n            });\n            for(var i = 0; i < kids.length; i++){\n                delete this.files[kids[i].name];\n            }\n        }\n        return this;\n    },\n    /**\n     * @deprecated This method has been removed in JSZip 3.0, please check the upgrade guide.\n     */ generate: function() {\n        throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n    },\n    /**\n     * Generate the complete zip file as an internal stream.\n     * @param {Object} options the options to generate the zip file :\n     * - compression, \"STORE\" by default.\n     * - type, \"base64\" by default. Values are : string, base64, uint8array, arraybuffer, blob.\n     * @return {StreamHelper} the streamed zip file.\n     */ generateInternalStream: function(options) {\n        var worker, opts = {};\n        try {\n            opts = utils.extend(options || {}, {\n                streamFiles: false,\n                compression: \"STORE\",\n                compressionOptions: null,\n                type: \"\",\n                platform: \"DOS\",\n                comment: null,\n                mimeType: \"application/zip\",\n                encodeFileName: utf8.utf8encode\n            });\n            opts.type = opts.type.toLowerCase();\n            opts.compression = opts.compression.toUpperCase();\n            // \"binarystring\" is preferred but the internals use \"string\".\n            if (opts.type === \"binarystring\") {\n                opts.type = \"string\";\n            }\n            if (!opts.type) {\n                throw new Error(\"No output type specified.\");\n            }\n            utils.checkSupport(opts.type);\n            // accept nodejs `process.platform`\n            if (opts.platform === \"darwin\" || opts.platform === \"freebsd\" || opts.platform === \"linux\" || opts.platform === \"sunos\") {\n                opts.platform = \"UNIX\";\n            }\n            if (opts.platform === \"win32\") {\n                opts.platform = \"DOS\";\n            }\n            var comment = opts.comment || this.comment || \"\";\n            worker = generate.generateWorker(this, opts, comment);\n        } catch (e) {\n            worker = new GenericWorker(\"error\");\n            worker.error(e);\n        }\n        return new StreamHelper(worker, opts.type || \"string\", opts.mimeType);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */ generateAsync: function(options, onUpdate) {\n        return this.generateInternalStream(options).accumulate(onUpdate);\n    },\n    /**\n     * Generate the complete zip file asynchronously.\n     * @see generateInternalStream\n     */ generateNodeStream: function(options, onUpdate) {\n        options = options || {};\n        if (!options.type) {\n            options.type = \"nodebuffer\";\n        }\n        return this.generateInternalStream(options).toNodejsStream(onUpdate);\n    }\n};\nmodule.exports = out;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/object.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js":
/*!******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/ArrayReader.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nfunction ArrayReader(data) {\n    DataReader.call(this, data);\n    for(var i = 0; i < this.data.length; i++){\n        data[i] = data[i] & 0xFF;\n    }\n}\nutils.inherits(ArrayReader, DataReader);\n/**\n * @see DataReader.byteAt\n */ ArrayReader.prototype.byteAt = function(i) {\n    return this.data[this.zero + i];\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */ ArrayReader.prototype.lastIndexOfSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0), sig1 = sig.charCodeAt(1), sig2 = sig.charCodeAt(2), sig3 = sig.charCodeAt(3);\n    for(var i = this.length - 4; i >= 0; --i){\n        if (this.data[i] === sig0 && this.data[i + 1] === sig1 && this.data[i + 2] === sig2 && this.data[i + 3] === sig3) {\n            return i - this.zero;\n        }\n    }\n    return -1;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */ ArrayReader.prototype.readAndCheckSignature = function(sig) {\n    var sig0 = sig.charCodeAt(0), sig1 = sig.charCodeAt(1), sig2 = sig.charCodeAt(2), sig3 = sig.charCodeAt(3), data = this.readData(4);\n    return sig0 === data[0] && sig1 === data[1] && sig2 === data[2] && sig3 === data[3];\n};\n/**\n * @see DataReader.readData\n */ ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if (size === 0) {\n        return [];\n    }\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/DataReader.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/DataReader.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nfunction DataReader(data) {\n    this.data = data; // type : see implementation\n    this.length = data.length;\n    this.index = 0;\n    this.zero = 0;\n}\nDataReader.prototype = {\n    /**\n     * Check that the offset will not go too far.\n     * @param {string} offset the additional offset to check.\n     * @throws {Error} an Error if the offset is out of bounds.\n     */ checkOffset: function(offset) {\n        this.checkIndex(this.index + offset);\n    },\n    /**\n     * Check that the specified index will not be too far.\n     * @param {string} newIndex the index to check.\n     * @throws {Error} an Error if the index is out of bounds.\n     */ checkIndex: function(newIndex) {\n        if (this.length < this.zero + newIndex || newIndex < 0) {\n            throw new Error(\"End of data reached (data length = \" + this.length + \", asked index = \" + newIndex + \"). Corrupted zip ?\");\n        }\n    },\n    /**\n     * Change the index.\n     * @param {number} newIndex The new index.\n     * @throws {Error} if the new index is out of the data.\n     */ setIndex: function(newIndex) {\n        this.checkIndex(newIndex);\n        this.index = newIndex;\n    },\n    /**\n     * Skip the next n bytes.\n     * @param {number} n the number of bytes to skip.\n     * @throws {Error} if the new index is out of the data.\n     */ skip: function(n) {\n        this.setIndex(this.index + n);\n    },\n    /**\n     * Get the byte at the specified index.\n     * @param {number} i the index to use.\n     * @return {number} a byte.\n     */ byteAt: function() {\n    // see implementations\n    },\n    /**\n     * Get the next number with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {number} the corresponding number.\n     */ readInt: function(size) {\n        var result = 0, i;\n        this.checkOffset(size);\n        for(i = this.index + size - 1; i >= this.index; i--){\n            result = (result << 8) + this.byteAt(i);\n        }\n        this.index += size;\n        return result;\n    },\n    /**\n     * Get the next string with a given byte size.\n     * @param {number} size the number of bytes to read.\n     * @return {string} the corresponding string.\n     */ readString: function(size) {\n        return utils.transformTo(\"string\", this.readData(size));\n    },\n    /**\n     * Get raw data without conversion, <size> bytes.\n     * @param {number} size the number of bytes to read.\n     * @return {Object} the raw data, implementation specific.\n     */ readData: function() {\n    // see implementations\n    },\n    /**\n     * Find the last occurrence of a zip signature (4 bytes).\n     * @param {string} sig the signature to find.\n     * @return {number} the index of the last occurrence, -1 if not found.\n     */ lastIndexOfSignature: function() {\n    // see implementations\n    },\n    /**\n     * Read the signature (4 bytes) at the current position and compare it with sig.\n     * @param {string} sig the expected signature\n     * @return {boolean} true if the signature matches, false otherwise.\n     */ readAndCheckSignature: function() {\n    // see implementations\n    },\n    /**\n     * Get the next date.\n     * @return {Date} the date.\n     */ readDate: function() {\n        var dostime = this.readInt(4);\n        return new Date(Date.UTC((dostime >> 25 & 0x7f) + 1980, (dostime >> 21 & 0x0f) - 1, dostime >> 16 & 0x1f, dostime >> 11 & 0x1f, dostime >> 5 & 0x3f, (dostime & 0x1f) << 1)); // second\n    }\n};\nmodule.exports = DataReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/DataReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/NodeBufferReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nfunction NodeBufferReader(data) {\n    Uint8ArrayReader.call(this, data);\n}\nutils.inherits(NodeBufferReader, Uint8ArrayReader);\n/**\n * @see DataReader.readData\n */ NodeBufferReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = NodeBufferReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9Ob2RlQnVmZmVyUmVhZGVyLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsSUFBSUEsbUJBQW1CQyxtQkFBT0EsQ0FBQyxxRkFBb0I7QUFDbkQsSUFBSUMsUUFBUUQsbUJBQU9BLENBQUMseURBQVU7QUFFOUIsU0FBU0UsaUJBQWlCQyxJQUFJO0lBQzFCSixpQkFBaUJLLElBQUksQ0FBQyxJQUFJLEVBQUVEO0FBQ2hDO0FBQ0FGLE1BQU1JLFFBQVEsQ0FBQ0gsa0JBQWtCSDtBQUVqQzs7Q0FFQyxHQUNERyxpQkFBaUJJLFNBQVMsQ0FBQ0MsUUFBUSxHQUFHLFNBQVNDLElBQUk7SUFDL0MsSUFBSSxDQUFDQyxXQUFXLENBQUNEO0lBQ2pCLElBQUlFLFNBQVMsSUFBSSxDQUFDUCxJQUFJLENBQUNRLEtBQUssQ0FBQyxJQUFJLENBQUNDLElBQUksR0FBRyxJQUFJLENBQUNDLEtBQUssRUFBRSxJQUFJLENBQUNELElBQUksR0FBRyxJQUFJLENBQUNDLEtBQUssR0FBR0w7SUFDOUUsSUFBSSxDQUFDSyxLQUFLLElBQUlMO0lBQ2QsT0FBT0U7QUFDWDtBQUNBSSxPQUFPQyxPQUFPLEdBQUdiIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvcmVhZGVyL05vZGVCdWZmZXJSZWFkZXIuanM/NTM2NSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBVaW50OEFycmF5UmVhZGVyID0gcmVxdWlyZShcIi4vVWludDhBcnJheVJlYWRlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuZnVuY3Rpb24gTm9kZUJ1ZmZlclJlYWRlcihkYXRhKSB7XG4gICAgVWludDhBcnJheVJlYWRlci5jYWxsKHRoaXMsIGRhdGEpO1xufVxudXRpbHMuaW5oZXJpdHMoTm9kZUJ1ZmZlclJlYWRlciwgVWludDhBcnJheVJlYWRlcik7XG5cbi8qKlxuICogQHNlZSBEYXRhUmVhZGVyLnJlYWREYXRhXG4gKi9cbk5vZGVCdWZmZXJSZWFkZXIucHJvdG90eXBlLnJlYWREYXRhID0gZnVuY3Rpb24oc2l6ZSkge1xuICAgIHRoaXMuY2hlY2tPZmZzZXQoc2l6ZSk7XG4gICAgdmFyIHJlc3VsdCA9IHRoaXMuZGF0YS5zbGljZSh0aGlzLnplcm8gKyB0aGlzLmluZGV4LCB0aGlzLnplcm8gKyB0aGlzLmluZGV4ICsgc2l6ZSk7XG4gICAgdGhpcy5pbmRleCArPSBzaXplO1xuICAgIHJldHVybiByZXN1bHQ7XG59O1xubW9kdWxlLmV4cG9ydHMgPSBOb2RlQnVmZmVyUmVhZGVyO1xuIl0sIm5hbWVzIjpbIlVpbnQ4QXJyYXlSZWFkZXIiLCJyZXF1aXJlIiwidXRpbHMiLCJOb2RlQnVmZmVyUmVhZGVyIiwiZGF0YSIsImNhbGwiLCJpbmhlcml0cyIsInByb3RvdHlwZSIsInJlYWREYXRhIiwic2l6ZSIsImNoZWNrT2Zmc2V0IiwicmVzdWx0Iiwic2xpY2UiLCJ6ZXJvIiwiaW5kZXgiLCJtb2R1bGUiLCJleHBvcnRzIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/StringReader.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/reader/StringReader.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar DataReader = __webpack_require__(/*! ./DataReader */ \"(ssr)/./node_modules/jszip/lib/reader/DataReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nfunction StringReader(data) {\n    DataReader.call(this, data);\n}\nutils.inherits(StringReader, DataReader);\n/**\n * @see DataReader.byteAt\n */ StringReader.prototype.byteAt = function(i) {\n    return this.data.charCodeAt(this.zero + i);\n};\n/**\n * @see DataReader.lastIndexOfSignature\n */ StringReader.prototype.lastIndexOfSignature = function(sig) {\n    return this.data.lastIndexOf(sig) - this.zero;\n};\n/**\n * @see DataReader.readAndCheckSignature\n */ StringReader.prototype.readAndCheckSignature = function(sig) {\n    var data = this.readData(4);\n    return sig === data;\n};\n/**\n * @see DataReader.readData\n */ StringReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    // this will work because the constructor applied the \"& 0xff\" mask.\n    var result = this.data.slice(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = StringReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9TdHJpbmdSZWFkZXIuanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYixJQUFJQSxhQUFhQyxtQkFBT0EsQ0FBQyx5RUFBYztBQUN2QyxJQUFJQyxRQUFRRCxtQkFBT0EsQ0FBQyx5REFBVTtBQUU5QixTQUFTRSxhQUFhQyxJQUFJO0lBQ3RCSixXQUFXSyxJQUFJLENBQUMsSUFBSSxFQUFFRDtBQUMxQjtBQUNBRixNQUFNSSxRQUFRLENBQUNILGNBQWNIO0FBQzdCOztDQUVDLEdBQ0RHLGFBQWFJLFNBQVMsQ0FBQ0MsTUFBTSxHQUFHLFNBQVNDLENBQUM7SUFDdEMsT0FBTyxJQUFJLENBQUNMLElBQUksQ0FBQ00sVUFBVSxDQUFDLElBQUksQ0FBQ0MsSUFBSSxHQUFHRjtBQUM1QztBQUNBOztDQUVDLEdBQ0ROLGFBQWFJLFNBQVMsQ0FBQ0ssb0JBQW9CLEdBQUcsU0FBU0MsR0FBRztJQUN0RCxPQUFPLElBQUksQ0FBQ1QsSUFBSSxDQUFDVSxXQUFXLENBQUNELE9BQU8sSUFBSSxDQUFDRixJQUFJO0FBQ2pEO0FBQ0E7O0NBRUMsR0FDRFIsYUFBYUksU0FBUyxDQUFDUSxxQkFBcUIsR0FBRyxTQUFVRixHQUFHO0lBQ3hELElBQUlULE9BQU8sSUFBSSxDQUFDWSxRQUFRLENBQUM7SUFDekIsT0FBT0gsUUFBUVQ7QUFDbkI7QUFDQTs7Q0FFQyxHQUNERCxhQUFhSSxTQUFTLENBQUNTLFFBQVEsR0FBRyxTQUFTQyxJQUFJO0lBQzNDLElBQUksQ0FBQ0MsV0FBVyxDQUFDRDtJQUNqQixvRUFBb0U7SUFDcEUsSUFBSUUsU0FBUyxJQUFJLENBQUNmLElBQUksQ0FBQ2dCLEtBQUssQ0FBQyxJQUFJLENBQUNULElBQUksR0FBRyxJQUFJLENBQUNVLEtBQUssRUFBRSxJQUFJLENBQUNWLElBQUksR0FBRyxJQUFJLENBQUNVLEtBQUssR0FBR0o7SUFDOUUsSUFBSSxDQUFDSSxLQUFLLElBQUlKO0lBQ2QsT0FBT0U7QUFDWDtBQUNBRyxPQUFPQyxPQUFPLEdBQUdwQiIsInNvdXJjZXMiOlsid2VicGFjazovL3p0ZWNoZW5naW5lZXJpbmcvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3JlYWRlci9TdHJpbmdSZWFkZXIuanM/NDAwYiJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcbnZhciBEYXRhUmVhZGVyID0gcmVxdWlyZShcIi4vRGF0YVJlYWRlclwiKTtcbnZhciB1dGlscyA9IHJlcXVpcmUoXCIuLi91dGlsc1wiKTtcblxuZnVuY3Rpb24gU3RyaW5nUmVhZGVyKGRhdGEpIHtcbiAgICBEYXRhUmVhZGVyLmNhbGwodGhpcywgZGF0YSk7XG59XG51dGlscy5pbmhlcml0cyhTdHJpbmdSZWFkZXIsIERhdGFSZWFkZXIpO1xuLyoqXG4gKiBAc2VlIERhdGFSZWFkZXIuYnl0ZUF0XG4gKi9cblN0cmluZ1JlYWRlci5wcm90b3R5cGUuYnl0ZUF0ID0gZnVuY3Rpb24oaSkge1xuICAgIHJldHVybiB0aGlzLmRhdGEuY2hhckNvZGVBdCh0aGlzLnplcm8gKyBpKTtcbn07XG4vKipcbiAqIEBzZWUgRGF0YVJlYWRlci5sYXN0SW5kZXhPZlNpZ25hdHVyZVxuICovXG5TdHJpbmdSZWFkZXIucHJvdG90eXBlLmxhc3RJbmRleE9mU2lnbmF0dXJlID0gZnVuY3Rpb24oc2lnKSB7XG4gICAgcmV0dXJuIHRoaXMuZGF0YS5sYXN0SW5kZXhPZihzaWcpIC0gdGhpcy56ZXJvO1xufTtcbi8qKlxuICogQHNlZSBEYXRhUmVhZGVyLnJlYWRBbmRDaGVja1NpZ25hdHVyZVxuICovXG5TdHJpbmdSZWFkZXIucHJvdG90eXBlLnJlYWRBbmRDaGVja1NpZ25hdHVyZSA9IGZ1bmN0aW9uIChzaWcpIHtcbiAgICB2YXIgZGF0YSA9IHRoaXMucmVhZERhdGEoNCk7XG4gICAgcmV0dXJuIHNpZyA9PT0gZGF0YTtcbn07XG4vKipcbiAqIEBzZWUgRGF0YVJlYWRlci5yZWFkRGF0YVxuICovXG5TdHJpbmdSZWFkZXIucHJvdG90eXBlLnJlYWREYXRhID0gZnVuY3Rpb24oc2l6ZSkge1xuICAgIHRoaXMuY2hlY2tPZmZzZXQoc2l6ZSk7XG4gICAgLy8gdGhpcyB3aWxsIHdvcmsgYmVjYXVzZSB0aGUgY29uc3RydWN0b3IgYXBwbGllZCB0aGUgXCImIDB4ZmZcIiBtYXNrLlxuICAgIHZhciByZXN1bHQgPSB0aGlzLmRhdGEuc2xpY2UodGhpcy56ZXJvICsgdGhpcy5pbmRleCwgdGhpcy56ZXJvICsgdGhpcy5pbmRleCArIHNpemUpO1xuICAgIHRoaXMuaW5kZXggKz0gc2l6ZTtcbiAgICByZXR1cm4gcmVzdWx0O1xufTtcbm1vZHVsZS5leHBvcnRzID0gU3RyaW5nUmVhZGVyO1xuIl0sIm5hbWVzIjpbIkRhdGFSZWFkZXIiLCJyZXF1aXJlIiwidXRpbHMiLCJTdHJpbmdSZWFkZXIiLCJkYXRhIiwiY2FsbCIsImluaGVyaXRzIiwicHJvdG90eXBlIiwiYnl0ZUF0IiwiaSIsImNoYXJDb2RlQXQiLCJ6ZXJvIiwibGFzdEluZGV4T2ZTaWduYXR1cmUiLCJzaWciLCJsYXN0SW5kZXhPZiIsInJlYWRBbmRDaGVja1NpZ25hdHVyZSIsInJlYWREYXRhIiwic2l6ZSIsImNoZWNrT2Zmc2V0IiwicmVzdWx0Iiwic2xpY2UiLCJpbmRleCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/StringReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js":
/*!***********************************************************!*\
  !*** ./node_modules/jszip/lib/reader/Uint8ArrayReader.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nfunction Uint8ArrayReader(data) {\n    ArrayReader.call(this, data);\n}\nutils.inherits(Uint8ArrayReader, ArrayReader);\n/**\n * @see DataReader.readData\n */ Uint8ArrayReader.prototype.readData = function(size) {\n    this.checkOffset(size);\n    if (size === 0) {\n        // in IE10, when using subarray(idx, idx), we get the array [0x00] instead of [].\n        return new Uint8Array(0);\n    }\n    var result = this.data.subarray(this.zero + this.index, this.zero + this.index + size);\n    this.index += size;\n    return result;\n};\nmodule.exports = Uint8ArrayReader;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/reader/readerFor.js":
/*!****************************************************!*\
  !*** ./node_modules/jszip/lib/reader/readerFor.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar ArrayReader = __webpack_require__(/*! ./ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/ArrayReader.js\");\nvar StringReader = __webpack_require__(/*! ./StringReader */ \"(ssr)/./node_modules/jszip/lib/reader/StringReader.js\");\nvar NodeBufferReader = __webpack_require__(/*! ./NodeBufferReader */ \"(ssr)/./node_modules/jszip/lib/reader/NodeBufferReader.js\");\nvar Uint8ArrayReader = __webpack_require__(/*! ./Uint8ArrayReader */ \"(ssr)/./node_modules/jszip/lib/reader/Uint8ArrayReader.js\");\n/**\n * Create a reader adapted to the data.\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data to read.\n * @return {DataReader} the data reader.\n */ module.exports = function(data) {\n    var type = utils.getTypeOf(data);\n    utils.checkSupport(type);\n    if (type === \"string\" && !support.uint8array) {\n        return new StringReader(data);\n    }\n    if (type === \"nodebuffer\") {\n        return new NodeBufferReader(data);\n    }\n    if (support.uint8array) {\n        return new Uint8ArrayReader(utils.transformTo(\"uint8array\", data));\n    }\n    return new ArrayReader(utils.transformTo(\"array\", data));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/reader/readerFor.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/signature.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/signature.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nexports.LOCAL_FILE_HEADER = \"PK\\x03\\x04\";\nexports.CENTRAL_FILE_HEADER = \"PK\\x01\\x02\";\nexports.CENTRAL_DIRECTORY_END = \"PK\\x05\\x06\";\nexports.ZIP64_CENTRAL_DIRECTORY_LOCATOR = \"PK\\x06\\x07\";\nexports.ZIP64_CENTRAL_DIRECTORY_END = \"PK\\x06\\x06\";\nexports.DATA_DESCRIPTOR = \"PK\\x07\\b\";\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3NpZ25hdHVyZS5qcyIsIm1hcHBpbmdzIjoiQUFBYTtBQUNiQSx5QkFBeUIsR0FBRztBQUM1QkEsMkJBQTJCLEdBQUc7QUFDOUJBLDZCQUE2QixHQUFHO0FBQ2hDQSx1Q0FBdUMsR0FBRztBQUMxQ0EsbUNBQW1DLEdBQUc7QUFDdENBLHVCQUF1QixHQUFHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc2lnbmF0dXJlLmpzPzAyY2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2Ugc3RyaWN0XCI7XG5leHBvcnRzLkxPQ0FMX0ZJTEVfSEVBREVSID0gXCJQS1xceDAzXFx4MDRcIjtcbmV4cG9ydHMuQ0VOVFJBTF9GSUxFX0hFQURFUiA9IFwiUEtcXHgwMVxceDAyXCI7XG5leHBvcnRzLkNFTlRSQUxfRElSRUNUT1JZX0VORCA9IFwiUEtcXHgwNVxceDA2XCI7XG5leHBvcnRzLlpJUDY0X0NFTlRSQUxfRElSRUNUT1JZX0xPQ0FUT1IgPSBcIlBLXFx4MDZcXHgwN1wiO1xuZXhwb3J0cy5aSVA2NF9DRU5UUkFMX0RJUkVDVE9SWV9FTkQgPSBcIlBLXFx4MDZcXHgwNlwiO1xuZXhwb3J0cy5EQVRBX0RFU0NSSVBUT1IgPSBcIlBLXFx4MDdcXHgwOFwiO1xuIl0sIm5hbWVzIjpbImV4cG9ydHMiLCJMT0NBTF9GSUxFX0hFQURFUiIsIkNFTlRSQUxfRklMRV9IRUFERVIiLCJDRU5UUkFMX0RJUkVDVE9SWV9FTkQiLCJaSVA2NF9DRU5UUkFMX0RJUkVDVE9SWV9MT0NBVE9SIiwiWklQNjRfQ0VOVFJBTF9ESVJFQ1RPUllfRU5EIiwiREFUQV9ERVNDUklQVE9SIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/signature.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/ConvertWorker.js ***!
  \********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n/**\n * A worker which convert chunks to a specified type.\n * @constructor\n * @param {String} destType the destination type.\n */ function ConvertWorker(destType) {\n    GenericWorker.call(this, \"ConvertWorker to \" + destType);\n    this.destType = destType;\n}\nutils.inherits(ConvertWorker, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ ConvertWorker.prototype.processChunk = function(chunk) {\n    this.push({\n        data: utils.transformTo(this.destType, chunk.data),\n        meta: chunk.meta\n    });\n};\nmodule.exports = ConvertWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/Crc32Probe.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar crc32 = __webpack_require__(/*! ../crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\n/**\n * A worker which calculate the crc32 of the data flowing through.\n * @constructor\n */ function Crc32Probe() {\n    GenericWorker.call(this, \"Crc32Probe\");\n    this.withStreamInfo(\"crc32\", 0);\n}\nutils.inherits(Crc32Probe, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ Crc32Probe.prototype.processChunk = function(chunk) {\n    this.streamInfo.crc32 = crc32(chunk.data, this.streamInfo.crc32 || 0);\n    this.push(chunk);\n};\nmodule.exports = Crc32Probe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvanN6aXAvbGliL3N0cmVhbS9DcmMzMlByb2JlLmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBRWIsSUFBSUEsZ0JBQWdCQyxtQkFBT0EsQ0FBQywrRUFBaUI7QUFDN0MsSUFBSUMsUUFBUUQsbUJBQU9BLENBQUMseURBQVU7QUFDOUIsSUFBSUUsUUFBUUYsbUJBQU9BLENBQUMseURBQVU7QUFFOUI7OztDQUdDLEdBQ0QsU0FBU0c7SUFDTEosY0FBY0ssSUFBSSxDQUFDLElBQUksRUFBRTtJQUN6QixJQUFJLENBQUNDLGNBQWMsQ0FBQyxTQUFTO0FBQ2pDO0FBQ0FILE1BQU1JLFFBQVEsQ0FBQ0gsWUFBWUo7QUFFM0I7O0NBRUMsR0FDREksV0FBV0ksU0FBUyxDQUFDQyxZQUFZLEdBQUcsU0FBVUMsS0FBSztJQUMvQyxJQUFJLENBQUNDLFVBQVUsQ0FBQ1QsS0FBSyxHQUFHQSxNQUFNUSxNQUFNRSxJQUFJLEVBQUUsSUFBSSxDQUFDRCxVQUFVLENBQUNULEtBQUssSUFBSTtJQUNuRSxJQUFJLENBQUNXLElBQUksQ0FBQ0g7QUFDZDtBQUNBSSxPQUFPQyxPQUFPLEdBQUdYIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8venRlY2hlbmdpbmVlcmluZy8uL25vZGVfbW9kdWxlcy9qc3ppcC9saWIvc3RyZWFtL0NyYzMyUHJvYmUuanM/MjE4OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBzdHJpY3RcIjtcblxudmFyIEdlbmVyaWNXb3JrZXIgPSByZXF1aXJlKFwiLi9HZW5lcmljV29ya2VyXCIpO1xudmFyIGNyYzMyID0gcmVxdWlyZShcIi4uL2NyYzMyXCIpO1xudmFyIHV0aWxzID0gcmVxdWlyZShcIi4uL3V0aWxzXCIpO1xuXG4vKipcbiAqIEEgd29ya2VyIHdoaWNoIGNhbGN1bGF0ZSB0aGUgY3JjMzIgb2YgdGhlIGRhdGEgZmxvd2luZyB0aHJvdWdoLlxuICogQGNvbnN0cnVjdG9yXG4gKi9cbmZ1bmN0aW9uIENyYzMyUHJvYmUoKSB7XG4gICAgR2VuZXJpY1dvcmtlci5jYWxsKHRoaXMsIFwiQ3JjMzJQcm9iZVwiKTtcbiAgICB0aGlzLndpdGhTdHJlYW1JbmZvKFwiY3JjMzJcIiwgMCk7XG59XG51dGlscy5pbmhlcml0cyhDcmMzMlByb2JlLCBHZW5lcmljV29ya2VyKTtcblxuLyoqXG4gKiBAc2VlIEdlbmVyaWNXb3JrZXIucHJvY2Vzc0NodW5rXG4gKi9cbkNyYzMyUHJvYmUucHJvdG90eXBlLnByb2Nlc3NDaHVuayA9IGZ1bmN0aW9uIChjaHVuaykge1xuICAgIHRoaXMuc3RyZWFtSW5mby5jcmMzMiA9IGNyYzMyKGNodW5rLmRhdGEsIHRoaXMuc3RyZWFtSW5mby5jcmMzMiB8fCAwKTtcbiAgICB0aGlzLnB1c2goY2h1bmspO1xufTtcbm1vZHVsZS5leHBvcnRzID0gQ3JjMzJQcm9iZTtcbiJdLCJuYW1lcyI6WyJHZW5lcmljV29ya2VyIiwicmVxdWlyZSIsImNyYzMyIiwidXRpbHMiLCJDcmMzMlByb2JlIiwiY2FsbCIsIndpdGhTdHJlYW1JbmZvIiwiaW5oZXJpdHMiLCJwcm90b3R5cGUiLCJwcm9jZXNzQ2h1bmsiLCJjaHVuayIsInN0cmVhbUluZm8iLCJkYXRhIiwicHVzaCIsIm1vZHVsZSIsImV4cG9ydHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/Crc32Probe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js":
/*!**********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataLengthProbe.js ***!
  \**********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n/**\n * A worker which calculate the total length of the data flowing through.\n * @constructor\n * @param {String} propName the name used to expose the length\n */ function DataLengthProbe(propName) {\n    GenericWorker.call(this, \"DataLengthProbe for \" + propName);\n    this.propName = propName;\n    this.withStreamInfo(propName, 0);\n}\nutils.inherits(DataLengthProbe, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ DataLengthProbe.prototype.processChunk = function(chunk) {\n    if (chunk) {\n        var length = this.streamInfo[this.propName] || 0;\n        this.streamInfo[this.propName] = length + chunk.data.length;\n    }\n    GenericWorker.prototype.processChunk.call(this, chunk);\n};\nmodule.exports = DataLengthProbe;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataLengthProbe.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/DataWorker.js":
/*!*****************************************************!*\
  !*** ./node_modules/jszip/lib/stream/DataWorker.js ***!
  \*****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n// the size of the generated chunks\n// TODO expose this as a public variable\nvar DEFAULT_BLOCK_SIZE = 16 * 1024;\n/**\n * A worker that reads a content and emits chunks.\n * @constructor\n * @param {Promise} dataP the promise of the data to split\n */ function DataWorker(dataP) {\n    GenericWorker.call(this, \"DataWorker\");\n    var self = this;\n    this.dataIsReady = false;\n    this.index = 0;\n    this.max = 0;\n    this.data = null;\n    this.type = \"\";\n    this._tickScheduled = false;\n    dataP.then(function(data) {\n        self.dataIsReady = true;\n        self.data = data;\n        self.max = data && data.length || 0;\n        self.type = utils.getTypeOf(data);\n        if (!self.isPaused) {\n            self._tickAndRepeat();\n        }\n    }, function(e) {\n        self.error(e);\n    });\n}\nutils.inherits(DataWorker, GenericWorker);\n/**\n * @see GenericWorker.cleanUp\n */ DataWorker.prototype.cleanUp = function() {\n    GenericWorker.prototype.cleanUp.call(this);\n    this.data = null;\n};\n/**\n * @see GenericWorker.resume\n */ DataWorker.prototype.resume = function() {\n    if (!GenericWorker.prototype.resume.call(this)) {\n        return false;\n    }\n    if (!this._tickScheduled && this.dataIsReady) {\n        this._tickScheduled = true;\n        utils.delay(this._tickAndRepeat, [], this);\n    }\n    return true;\n};\n/**\n * Trigger a tick a schedule an other call to this function.\n */ DataWorker.prototype._tickAndRepeat = function() {\n    this._tickScheduled = false;\n    if (this.isPaused || this.isFinished) {\n        return;\n    }\n    this._tick();\n    if (!this.isFinished) {\n        utils.delay(this._tickAndRepeat, [], this);\n        this._tickScheduled = true;\n    }\n};\n/**\n * Read and push a chunk.\n */ DataWorker.prototype._tick = function() {\n    if (this.isPaused || this.isFinished) {\n        return false;\n    }\n    var size = DEFAULT_BLOCK_SIZE;\n    var data = null, nextIndex = Math.min(this.max, this.index + size);\n    if (this.index >= this.max) {\n        // EOF\n        return this.end();\n    } else {\n        switch(this.type){\n            case \"string\":\n                data = this.data.substring(this.index, nextIndex);\n                break;\n            case \"uint8array\":\n                data = this.data.subarray(this.index, nextIndex);\n                break;\n            case \"array\":\n            case \"nodebuffer\":\n                data = this.data.slice(this.index, nextIndex);\n                break;\n        }\n        this.index = nextIndex;\n        return this.push({\n            data: data,\n            meta: {\n                percent: this.max ? this.index / this.max * 100 : 0\n            }\n        });\n    }\n};\nmodule.exports = DataWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js":
/*!********************************************************!*\
  !*** ./node_modules/jszip/lib/stream/GenericWorker.js ***!
  \********************************************************/
/***/ ((module) => {

eval("\n/**\n * A worker that does nothing but passing chunks to the next one. This is like\n * a nodejs stream but with some differences. On the good side :\n * - it works on IE 6-9 without any issue / polyfill\n * - it weights less than the full dependencies bundled with browserify\n * - it forwards errors (no need to declare an error handler EVERYWHERE)\n *\n * A chunk is an object with 2 attributes : `meta` and `data`. The former is an\n * object containing anything (`percent` for example), see each worker for more\n * details. The latter is the real data (String, Uint8Array, etc).\n *\n * @constructor\n * @param {String} name the name of the stream (mainly used for debugging purposes)\n */ function GenericWorker(name) {\n    // the name of the worker\n    this.name = name || \"default\";\n    // an object containing metadata about the workers chain\n    this.streamInfo = {};\n    // an error which happened when the worker was paused\n    this.generatedError = null;\n    // an object containing metadata to be merged by this worker into the general metadata\n    this.extraStreamInfo = {};\n    // true if the stream is paused (and should not do anything), false otherwise\n    this.isPaused = true;\n    // true if the stream is finished (and should not do anything), false otherwise\n    this.isFinished = false;\n    // true if the stream is locked to prevent further structure updates (pipe), false otherwise\n    this.isLocked = false;\n    // the event listeners\n    this._listeners = {\n        \"data\": [],\n        \"end\": [],\n        \"error\": []\n    };\n    // the previous worker, if any\n    this.previous = null;\n}\nGenericWorker.prototype = {\n    /**\n     * Push a chunk to the next workers.\n     * @param {Object} chunk the chunk to push\n     */ push: function(chunk) {\n        this.emit(\"data\", chunk);\n    },\n    /**\n     * End the stream.\n     * @return {Boolean} true if this call ended the worker, false otherwise.\n     */ end: function() {\n        if (this.isFinished) {\n            return false;\n        }\n        this.flush();\n        try {\n            this.emit(\"end\");\n            this.cleanUp();\n            this.isFinished = true;\n        } catch (e) {\n            this.emit(\"error\", e);\n        }\n        return true;\n    },\n    /**\n     * End the stream with an error.\n     * @param {Error} e the error which caused the premature end.\n     * @return {Boolean} true if this call ended the worker with an error, false otherwise.\n     */ error: function(e) {\n        if (this.isFinished) {\n            return false;\n        }\n        if (this.isPaused) {\n            this.generatedError = e;\n        } else {\n            this.isFinished = true;\n            this.emit(\"error\", e);\n            // in the workers chain exploded in the middle of the chain,\n            // the error event will go downward but we also need to notify\n            // workers upward that there has been an error.\n            if (this.previous) {\n                this.previous.error(e);\n            }\n            this.cleanUp();\n        }\n        return true;\n    },\n    /**\n     * Add a callback on an event.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Function} listener the function to call when the event is triggered\n     * @return {GenericWorker} the current object for chainability\n     */ on: function(name, listener) {\n        this._listeners[name].push(listener);\n        return this;\n    },\n    /**\n     * Clean any references when a worker is ending.\n     */ cleanUp: function() {\n        this.streamInfo = this.generatedError = this.extraStreamInfo = null;\n        this._listeners = [];\n    },\n    /**\n     * Trigger an event. This will call registered callback with the provided arg.\n     * @param {String} name the name of the event (data, end, error)\n     * @param {Object} arg the argument to call the callback with.\n     */ emit: function(name, arg) {\n        if (this._listeners[name]) {\n            for(var i = 0; i < this._listeners[name].length; i++){\n                this._listeners[name][i].call(this, arg);\n            }\n        }\n    },\n    /**\n     * Chain a worker with an other.\n     * @param {Worker} next the worker receiving events from the current one.\n     * @return {worker} the next worker for chainability\n     */ pipe: function(next) {\n        return next.registerPrevious(this);\n    },\n    /**\n     * Same as `pipe` in the other direction.\n     * Using an API with `pipe(next)` is very easy.\n     * Implementing the API with the point of view of the next one registering\n     * a source is easier, see the ZipFileWorker.\n     * @param {Worker} previous the previous worker, sending events to this one\n     * @return {Worker} the current worker for chainability\n     */ registerPrevious: function(previous) {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        // sharing the streamInfo...\n        this.streamInfo = previous.streamInfo;\n        // ... and adding our own bits\n        this.mergeStreamInfo();\n        this.previous = previous;\n        var self = this;\n        previous.on(\"data\", function(chunk) {\n            self.processChunk(chunk);\n        });\n        previous.on(\"end\", function() {\n            self.end();\n        });\n        previous.on(\"error\", function(e) {\n            self.error(e);\n        });\n        return this;\n    },\n    /**\n     * Pause the stream so it doesn't send events anymore.\n     * @return {Boolean} true if this call paused the worker, false otherwise.\n     */ pause: function() {\n        if (this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = true;\n        if (this.previous) {\n            this.previous.pause();\n        }\n        return true;\n    },\n    /**\n     * Resume a paused stream.\n     * @return {Boolean} true if this call resumed the worker, false otherwise.\n     */ resume: function() {\n        if (!this.isPaused || this.isFinished) {\n            return false;\n        }\n        this.isPaused = false;\n        // if true, the worker tried to resume but failed\n        var withError = false;\n        if (this.generatedError) {\n            this.error(this.generatedError);\n            withError = true;\n        }\n        if (this.previous) {\n            this.previous.resume();\n        }\n        return !withError;\n    },\n    /**\n     * Flush any remaining bytes as the stream is ending.\n     */ flush: function() {},\n    /**\n     * Process a chunk. This is usually the method overridden.\n     * @param {Object} chunk the chunk to process.\n     */ processChunk: function(chunk) {\n        this.push(chunk);\n    },\n    /**\n     * Add a key/value to be added in the workers chain streamInfo once activated.\n     * @param {String} key the key to use\n     * @param {Object} value the associated value\n     * @return {Worker} the current worker for chainability\n     */ withStreamInfo: function(key, value) {\n        this.extraStreamInfo[key] = value;\n        this.mergeStreamInfo();\n        return this;\n    },\n    /**\n     * Merge this worker's streamInfo into the chain's streamInfo.\n     */ mergeStreamInfo: function() {\n        for(var key in this.extraStreamInfo){\n            if (!Object.prototype.hasOwnProperty.call(this.extraStreamInfo, key)) {\n                continue;\n            }\n            this.streamInfo[key] = this.extraStreamInfo[key];\n        }\n    },\n    /**\n     * Lock the stream to prevent further updates on the workers chain.\n     * After calling this method, all calls to pipe will fail.\n     */ lock: function() {\n        if (this.isLocked) {\n            throw new Error(\"The stream '\" + this + \"' has already been used.\");\n        }\n        this.isLocked = true;\n        if (this.previous) {\n            this.previous.lock();\n        }\n    },\n    /**\n     *\n     * Pretty print the workers chain.\n     */ toString: function() {\n        var me = \"Worker \" + this.name;\n        if (this.previous) {\n            return this.previous + \" -> \" + me;\n        } else {\n            return me;\n        }\n    }\n};\nmodule.exports = GenericWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js":
/*!*******************************************************!*\
  !*** ./node_modules/jszip/lib/stream/StreamHelper.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ../utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar ConvertWorker = __webpack_require__(/*! ./ConvertWorker */ \"(ssr)/./node_modules/jszip/lib/stream/ConvertWorker.js\");\nvar GenericWorker = __webpack_require__(/*! ./GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\nvar base64 = __webpack_require__(/*! ../base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar support = __webpack_require__(/*! ../support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar external = __webpack_require__(/*! ../external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\nvar NodejsStreamOutputAdapter = null;\nif (support.nodestream) {\n    try {\n        NodejsStreamOutputAdapter = __webpack_require__(/*! ../nodejs/NodejsStreamOutputAdapter */ \"(ssr)/./node_modules/jszip/lib/nodejs/NodejsStreamOutputAdapter.js\");\n    } catch (e) {\n    // ignore\n    }\n}\n/**\n * Apply the final transformation of the data. If the user wants a Blob for\n * example, it's easier to work with an U8intArray and finally do the\n * ArrayBuffer/Blob conversion.\n * @param {String} type the name of the final type\n * @param {String|Uint8Array|Buffer} content the content to transform\n * @param {String} mimeType the mime type of the content, if applicable.\n * @return {String|Uint8Array|ArrayBuffer|Buffer|Blob} the content in the right format.\n */ function transformZipOutput(type, content, mimeType) {\n    switch(type){\n        case \"blob\":\n            return utils.newBlob(utils.transformTo(\"arraybuffer\", content), mimeType);\n        case \"base64\":\n            return base64.encode(content);\n        default:\n            return utils.transformTo(type, content);\n    }\n}\n/**\n * Concatenate an array of data of the given type.\n * @param {String} type the type of the data in the given array.\n * @param {Array} dataArray the array containing the data chunks to concatenate\n * @return {String|Uint8Array|Buffer} the concatenated data\n * @throws Error if the asked type is unsupported\n */ function concat(type, dataArray) {\n    var i, index = 0, res = null, totalLength = 0;\n    for(i = 0; i < dataArray.length; i++){\n        totalLength += dataArray[i].length;\n    }\n    switch(type){\n        case \"string\":\n            return dataArray.join(\"\");\n        case \"array\":\n            return Array.prototype.concat.apply([], dataArray);\n        case \"uint8array\":\n            res = new Uint8Array(totalLength);\n            for(i = 0; i < dataArray.length; i++){\n                res.set(dataArray[i], index);\n                index += dataArray[i].length;\n            }\n            return res;\n        case \"nodebuffer\":\n            return Buffer.concat(dataArray);\n        default:\n            throw new Error(\"concat : unsupported type '\" + type + \"'\");\n    }\n}\n/**\n * Listen a StreamHelper, accumulate its content and concatenate it into a\n * complete block.\n * @param {StreamHelper} helper the helper to use.\n * @param {Function} updateCallback a callback called on each update. Called\n * with one arg :\n * - the metadata linked to the update received.\n * @return Promise the promise for the accumulation.\n */ function accumulate(helper, updateCallback) {\n    return new external.Promise(function(resolve, reject) {\n        var dataArray = [];\n        var chunkType = helper._internalType, resultType = helper._outputType, mimeType = helper._mimeType;\n        helper.on(\"data\", function(data, meta) {\n            dataArray.push(data);\n            if (updateCallback) {\n                updateCallback(meta);\n            }\n        }).on(\"error\", function(err) {\n            dataArray = [];\n            reject(err);\n        }).on(\"end\", function() {\n            try {\n                var result = transformZipOutput(resultType, concat(chunkType, dataArray), mimeType);\n                resolve(result);\n            } catch (e) {\n                reject(e);\n            }\n            dataArray = [];\n        }).resume();\n    });\n}\n/**\n * An helper to easily use workers outside of JSZip.\n * @constructor\n * @param {Worker} worker the worker to wrap\n * @param {String} outputType the type of data expected by the use\n * @param {String} mimeType the mime type of the content, if applicable.\n */ function StreamHelper(worker, outputType, mimeType) {\n    var internalType = outputType;\n    switch(outputType){\n        case \"blob\":\n        case \"arraybuffer\":\n            internalType = \"uint8array\";\n            break;\n        case \"base64\":\n            internalType = \"string\";\n            break;\n    }\n    try {\n        // the type used internally\n        this._internalType = internalType;\n        // the type used to output results\n        this._outputType = outputType;\n        // the mime type\n        this._mimeType = mimeType;\n        utils.checkSupport(internalType);\n        this._worker = worker.pipe(new ConvertWorker(internalType));\n        // the last workers can be rewired without issues but we need to\n        // prevent any updates on previous workers.\n        worker.lock();\n    } catch (e) {\n        this._worker = new GenericWorker(\"error\");\n        this._worker.error(e);\n    }\n}\nStreamHelper.prototype = {\n    /**\n     * Listen a StreamHelper, accumulate its content and concatenate it into a\n     * complete block.\n     * @param {Function} updateCb the update callback.\n     * @return Promise the promise for the accumulation.\n     */ accumulate: function(updateCb) {\n        return accumulate(this, updateCb);\n    },\n    /**\n     * Add a listener on an event triggered on a stream.\n     * @param {String} evt the name of the event\n     * @param {Function} fn the listener\n     * @return {StreamHelper} the current helper.\n     */ on: function(evt, fn) {\n        var self = this;\n        if (evt === \"data\") {\n            this._worker.on(evt, function(chunk) {\n                fn.call(self, chunk.data, chunk.meta);\n            });\n        } else {\n            this._worker.on(evt, function() {\n                utils.delay(fn, arguments, self);\n            });\n        }\n        return this;\n    },\n    /**\n     * Resume the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */ resume: function() {\n        utils.delay(this._worker.resume, [], this._worker);\n        return this;\n    },\n    /**\n     * Pause the flow of chunks.\n     * @return {StreamHelper} the current helper.\n     */ pause: function() {\n        this._worker.pause();\n        return this;\n    },\n    /**\n     * Return a nodejs stream for this helper.\n     * @param {Function} updateCb the update callback.\n     * @return {NodejsStreamOutputAdapter} the nodejs stream.\n     */ toNodejsStream: function(updateCb) {\n        utils.checkSupport(\"nodestream\");\n        if (this._outputType !== \"nodebuffer\") {\n            // an object stream containing blob/arraybuffer/uint8array/string\n            // is strange and I don't know if it would be useful.\n            // I you find this comment and have a good usecase, please open a\n            // bug report !\n            throw new Error(this._outputType + \" is not supported by this method\");\n        }\n        return new NodejsStreamOutputAdapter(this, {\n            objectMode: this._outputType !== \"nodebuffer\"\n        }, updateCb);\n    }\n};\nmodule.exports = StreamHelper;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/support.js":
/*!*******************************************!*\
  !*** ./node_modules/jszip/lib/support.js ***!
  \*******************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nexports.base64 = true;\nexports.array = true;\nexports.string = true;\nexports.arraybuffer = typeof ArrayBuffer !== \"undefined\" && typeof Uint8Array !== \"undefined\";\nexports.nodebuffer = typeof Buffer !== \"undefined\";\n// contains true if JSZip can read/generate Uint8Array, false otherwise.\nexports.uint8array = typeof Uint8Array !== \"undefined\";\nif (typeof ArrayBuffer === \"undefined\") {\n    exports.blob = false;\n} else {\n    var buffer = new ArrayBuffer(0);\n    try {\n        exports.blob = new Blob([\n            buffer\n        ], {\n            type: \"application/zip\"\n        }).size === 0;\n    } catch (e) {\n        try {\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(buffer);\n            exports.blob = builder.getBlob(\"application/zip\").size === 0;\n        } catch (e) {\n            exports.blob = false;\n        }\n    }\n}\ntry {\n    exports.nodestream = !!(__webpack_require__(/*! readable-stream */ \"(ssr)/./node_modules/readable-stream/readable.js\").Readable);\n} catch (e) {\n    exports.nodestream = false;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/support.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utf8.js":
/*!****************************************!*\
  !*** ./node_modules/jszip/lib/utf8.js ***!
  \****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n/**\n * The following functions come from pako, from pako/lib/utils/strings\n * released under the MIT license, see pako https://github.com/nodeca/pako/\n */ // Table with utf8 lengths (calculated by first byte of sequence)\n// Note, that 5 & 6-byte values and some 4-byte values can not be represented in JS,\n// because max possible codepoint is 0x10ffff\nvar _utf8len = new Array(256);\nfor(var i = 0; i < 256; i++){\n    _utf8len[i] = i >= 252 ? 6 : i >= 248 ? 5 : i >= 240 ? 4 : i >= 224 ? 3 : i >= 192 ? 2 : 1;\n}\n_utf8len[254] = _utf8len[254] = 1; // Invalid sequence start\n// convert string to array (typed, when possible)\nvar string2buf = function(str) {\n    var buf, c, c2, m_pos, i, str_len = str.length, buf_len = 0;\n    // count binary size\n    for(m_pos = 0; m_pos < str_len; m_pos++){\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && m_pos + 1 < str_len) {\n            c2 = str.charCodeAt(m_pos + 1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        buf_len += c < 0x80 ? 1 : c < 0x800 ? 2 : c < 0x10000 ? 3 : 4;\n    }\n    // allocate buffer\n    if (support.uint8array) {\n        buf = new Uint8Array(buf_len);\n    } else {\n        buf = new Array(buf_len);\n    }\n    // convert\n    for(i = 0, m_pos = 0; i < buf_len; m_pos++){\n        c = str.charCodeAt(m_pos);\n        if ((c & 0xfc00) === 0xd800 && m_pos + 1 < str_len) {\n            c2 = str.charCodeAt(m_pos + 1);\n            if ((c2 & 0xfc00) === 0xdc00) {\n                c = 0x10000 + (c - 0xd800 << 10) + (c2 - 0xdc00);\n                m_pos++;\n            }\n        }\n        if (c < 0x80) {\n            /* one byte */ buf[i++] = c;\n        } else if (c < 0x800) {\n            /* two bytes */ buf[i++] = 0xC0 | c >>> 6;\n            buf[i++] = 0x80 | c & 0x3f;\n        } else if (c < 0x10000) {\n            /* three bytes */ buf[i++] = 0xE0 | c >>> 12;\n            buf[i++] = 0x80 | c >>> 6 & 0x3f;\n            buf[i++] = 0x80 | c & 0x3f;\n        } else {\n            /* four bytes */ buf[i++] = 0xf0 | c >>> 18;\n            buf[i++] = 0x80 | c >>> 12 & 0x3f;\n            buf[i++] = 0x80 | c >>> 6 & 0x3f;\n            buf[i++] = 0x80 | c & 0x3f;\n        }\n    }\n    return buf;\n};\n// Calculate max possible position in utf8 buffer,\n// that will not break sequence. If that's not possible\n// - (very small limits) return max size as is.\n//\n// buf[] - utf8 bytes array\n// max   - length limit (mandatory);\nvar utf8border = function(buf, max) {\n    var pos;\n    max = max || buf.length;\n    if (max > buf.length) {\n        max = buf.length;\n    }\n    // go back from last position, until start of sequence found\n    pos = max - 1;\n    while(pos >= 0 && (buf[pos] & 0xC0) === 0x80){\n        pos--;\n    }\n    // Fuckup - very small and broken sequence,\n    // return max, because we should return something anyway.\n    if (pos < 0) {\n        return max;\n    }\n    // If we came to start of buffer - that means vuffer is too small,\n    // return max too.\n    if (pos === 0) {\n        return max;\n    }\n    return pos + _utf8len[buf[pos]] > max ? pos : max;\n};\n// convert array to string\nvar buf2string = function(buf) {\n    var i, out, c, c_len;\n    var len = buf.length;\n    // Reserve max possible length (2 words per char)\n    // NB: by unknown reasons, Array is significantly faster for\n    //     String.fromCharCode.apply than Uint16Array.\n    var utf16buf = new Array(len * 2);\n    for(out = 0, i = 0; i < len;){\n        c = buf[i++];\n        // quick process ascii\n        if (c < 0x80) {\n            utf16buf[out++] = c;\n            continue;\n        }\n        c_len = _utf8len[c];\n        // skip 5 & 6 byte codes\n        if (c_len > 4) {\n            utf16buf[out++] = 0xfffd;\n            i += c_len - 1;\n            continue;\n        }\n        // apply mask on first byte\n        c &= c_len === 2 ? 0x1f : c_len === 3 ? 0x0f : 0x07;\n        // join the rest\n        while(c_len > 1 && i < len){\n            c = c << 6 | buf[i++] & 0x3f;\n            c_len--;\n        }\n        // terminated by end of string?\n        if (c_len > 1) {\n            utf16buf[out++] = 0xfffd;\n            continue;\n        }\n        if (c < 0x10000) {\n            utf16buf[out++] = c;\n        } else {\n            c -= 0x10000;\n            utf16buf[out++] = 0xd800 | c >> 10 & 0x3ff;\n            utf16buf[out++] = 0xdc00 | c & 0x3ff;\n        }\n    }\n    // shrinkBuf(utf16buf, out)\n    if (utf16buf.length !== out) {\n        if (utf16buf.subarray) {\n            utf16buf = utf16buf.subarray(0, out);\n        } else {\n            utf16buf.length = out;\n        }\n    }\n    // return String.fromCharCode.apply(null, utf16buf);\n    return utils.applyFromCharCode(utf16buf);\n};\n// That's all for the pako functions.\n/**\n * Transform a javascript string into an array (typed if possible) of bytes,\n * UTF-8 encoded.\n * @param {String} str the string to encode\n * @return {Array|Uint8Array|Buffer} the UTF-8 encoded string.\n */ exports.utf8encode = function utf8encode(str) {\n    if (support.nodebuffer) {\n        return nodejsUtils.newBufferFrom(str, \"utf-8\");\n    }\n    return string2buf(str);\n};\n/**\n * Transform a bytes array (or a representation) representing an UTF-8 encoded\n * string into a javascript string.\n * @param {Array|Uint8Array|Buffer} buf the data de decode\n * @return {String} the decoded string.\n */ exports.utf8decode = function utf8decode(buf) {\n    if (support.nodebuffer) {\n        return utils.transformTo(\"nodebuffer\", buf).toString(\"utf-8\");\n    }\n    buf = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", buf);\n    return buf2string(buf);\n};\n/**\n * A worker to decode utf8 encoded binary chunks into string chunks.\n * @constructor\n */ function Utf8DecodeWorker() {\n    GenericWorker.call(this, \"utf-8 decode\");\n    // the last bytes if a chunk didn't end with a complete codepoint.\n    this.leftOver = null;\n}\nutils.inherits(Utf8DecodeWorker, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ Utf8DecodeWorker.prototype.processChunk = function(chunk) {\n    var data = utils.transformTo(support.uint8array ? \"uint8array\" : \"array\", chunk.data);\n    // 1st step, re-use what's left of the previous chunk\n    if (this.leftOver && this.leftOver.length) {\n        if (support.uint8array) {\n            var previousData = data;\n            data = new Uint8Array(previousData.length + this.leftOver.length);\n            data.set(this.leftOver, 0);\n            data.set(previousData, this.leftOver.length);\n        } else {\n            data = this.leftOver.concat(data);\n        }\n        this.leftOver = null;\n    }\n    var nextBoundary = utf8border(data);\n    var usableData = data;\n    if (nextBoundary !== data.length) {\n        if (support.uint8array) {\n            usableData = data.subarray(0, nextBoundary);\n            this.leftOver = data.subarray(nextBoundary, data.length);\n        } else {\n            usableData = data.slice(0, nextBoundary);\n            this.leftOver = data.slice(nextBoundary, data.length);\n        }\n    }\n    this.push({\n        data: exports.utf8decode(usableData),\n        meta: chunk.meta\n    });\n};\n/**\n * @see GenericWorker.flush\n */ Utf8DecodeWorker.prototype.flush = function() {\n    if (this.leftOver && this.leftOver.length) {\n        this.push({\n            data: exports.utf8decode(this.leftOver),\n            meta: {}\n        });\n        this.leftOver = null;\n    }\n};\nexports.Utf8DecodeWorker = Utf8DecodeWorker;\n/**\n * A worker to endcode string chunks into utf8 encoded binary chunks.\n * @constructor\n */ function Utf8EncodeWorker() {\n    GenericWorker.call(this, \"utf-8 encode\");\n}\nutils.inherits(Utf8EncodeWorker, GenericWorker);\n/**\n * @see GenericWorker.processChunk\n */ Utf8EncodeWorker.prototype.processChunk = function(chunk) {\n    this.push({\n        data: exports.utf8encode(chunk.data),\n        meta: chunk.meta\n    });\n};\nexports.Utf8EncodeWorker = Utf8EncodeWorker;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utf8.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/utils.js":
/*!*****************************************!*\
  !*** ./node_modules/jszip/lib/utils.js ***!
  \*****************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar base64 = __webpack_require__(/*! ./base64 */ \"(ssr)/./node_modules/jszip/lib/base64.js\");\nvar nodejsUtils = __webpack_require__(/*! ./nodejsUtils */ \"(ssr)/./node_modules/jszip/lib/nodejsUtils.js\");\nvar external = __webpack_require__(/*! ./external */ \"(ssr)/./node_modules/jszip/lib/external.js\");\n__webpack_require__(/*! setimmediate */ \"(ssr)/./node_modules/next/dist/compiled/setimmediate/setImmediate.js\");\n/**\n * Convert a string that pass as a \"binary string\": it should represent a byte\n * array but may have > 255 char codes. Be sure to take only the first byte\n * and returns the byte array.\n * @param {String} str the string to transform.\n * @return {Array|Uint8Array} the string in a binary format.\n */ function string2binary(str) {\n    var result = null;\n    if (support.uint8array) {\n        result = new Uint8Array(str.length);\n    } else {\n        result = new Array(str.length);\n    }\n    return stringToArrayLike(str, result);\n}\n/**\n * Create a new blob with the given content and the given type.\n * @param {String|ArrayBuffer} part the content to put in the blob. DO NOT use\n * an Uint8Array because the stock browser of android 4 won't accept it (it\n * will be silently converted to a string, \"[object Uint8Array]\").\n *\n * Use only ONE part to build the blob to avoid a memory leak in IE11 / Edge:\n * when a large amount of Array is used to create the Blob, the amount of\n * memory consumed is nearly 100 times the original data amount.\n *\n * @param {String} type the mime type of the blob.\n * @return {Blob} the created blob.\n */ exports.newBlob = function(part, type) {\n    exports.checkSupport(\"blob\");\n    try {\n        // Blob constructor\n        return new Blob([\n            part\n        ], {\n            type: type\n        });\n    } catch (e) {\n        try {\n            // deprecated, browser only, old way\n            var Builder = self.BlobBuilder || self.WebKitBlobBuilder || self.MozBlobBuilder || self.MSBlobBuilder;\n            var builder = new Builder();\n            builder.append(part);\n            return builder.getBlob(type);\n        } catch (e) {\n            // well, fuck ?!\n            throw new Error(\"Bug : can't construct the Blob.\");\n        }\n    }\n};\n/**\n * The identity function.\n * @param {Object} input the input.\n * @return {Object} the same input.\n */ function identity(input) {\n    return input;\n}\n/**\n * Fill in an array with a string.\n * @param {String} str the string to use.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to fill in (will be mutated).\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated array.\n */ function stringToArrayLike(str, array) {\n    for(var i = 0; i < str.length; ++i){\n        array[i] = str.charCodeAt(i) & 0xFF;\n    }\n    return array;\n}\n/**\n * An helper for the function arrayLikeToString.\n * This contains static information and functions that\n * can be optimized by the browser JIT compiler.\n */ var arrayToStringHelper = {\n    /**\n     * Transform an array of int into a string, chunk by chunk.\n     * See the performances notes on arrayLikeToString.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @param {String} type the type of the array.\n     * @param {Integer} chunk the chunk size.\n     * @return {String} the resulting string.\n     * @throws Error if the chunk is too big for the stack.\n     */ stringifyByChunk: function(array, type, chunk) {\n        var result = [], k = 0, len = array.length;\n        // shortcut\n        if (len <= chunk) {\n            return String.fromCharCode.apply(null, array);\n        }\n        while(k < len){\n            if (type === \"array\" || type === \"nodebuffer\") {\n                result.push(String.fromCharCode.apply(null, array.slice(k, Math.min(k + chunk, len))));\n            } else {\n                result.push(String.fromCharCode.apply(null, array.subarray(k, Math.min(k + chunk, len))));\n            }\n            k += chunk;\n        }\n        return result.join(\"\");\n    },\n    /**\n     * Call String.fromCharCode on every item in the array.\n     * This is the naive implementation, which generate A LOT of intermediate string.\n     * This should be used when everything else fail.\n     * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n     * @return {String} the result.\n     */ stringifyByChar: function(array) {\n        var resultStr = \"\";\n        for(var i = 0; i < array.length; i++){\n            resultStr += String.fromCharCode(array[i]);\n        }\n        return resultStr;\n    },\n    applyCanBeUsed: {\n        /**\n         * true if the browser accepts to use String.fromCharCode on Uint8Array\n         */ uint8array: function() {\n            try {\n                return support.uint8array && String.fromCharCode.apply(null, new Uint8Array(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        }(),\n        /**\n         * true if the browser accepts to use String.fromCharCode on nodejs Buffer.\n         */ nodebuffer: function() {\n            try {\n                return support.nodebuffer && String.fromCharCode.apply(null, nodejsUtils.allocBuffer(1)).length === 1;\n            } catch (e) {\n                return false;\n            }\n        }()\n    }\n};\n/**\n * Transform an array-like object to a string.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} array the array to transform.\n * @return {String} the result.\n */ function arrayLikeToString(array) {\n    // Performances notes :\n    // --------------------\n    // String.fromCharCode.apply(null, array) is the fastest, see\n    // see http://jsperf.com/converting-a-uint8array-to-a-string/2\n    // but the stack is limited (and we can get huge arrays !).\n    //\n    // result += String.fromCharCode(array[i]); generate too many strings !\n    //\n    // This code is inspired by http://jsperf.com/arraybuffer-to-string-apply-performance/2\n    // TODO : we now have workers that split the work. Do we still need that ?\n    var chunk = 65536, type = exports.getTypeOf(array), canUseApply = true;\n    if (type === \"uint8array\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.uint8array;\n    } else if (type === \"nodebuffer\") {\n        canUseApply = arrayToStringHelper.applyCanBeUsed.nodebuffer;\n    }\n    if (canUseApply) {\n        while(chunk > 1){\n            try {\n                return arrayToStringHelper.stringifyByChunk(array, type, chunk);\n            } catch (e) {\n                chunk = Math.floor(chunk / 2);\n            }\n        }\n    }\n    // no apply or chunk error : slow and painful algorithm\n    // default browser on android 4.*\n    return arrayToStringHelper.stringifyByChar(array);\n}\nexports.applyFromCharCode = arrayLikeToString;\n/**\n * Copy the data from an array-like to an other array-like.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayFrom the origin array.\n * @param {Array|ArrayBuffer|Uint8Array|Buffer} arrayTo the destination array which will be mutated.\n * @return {Array|ArrayBuffer|Uint8Array|Buffer} the updated destination array.\n */ function arrayLikeToArrayLike(arrayFrom, arrayTo) {\n    for(var i = 0; i < arrayFrom.length; i++){\n        arrayTo[i] = arrayFrom[i];\n    }\n    return arrayTo;\n}\n// a matrix containing functions to transform everything into everything.\nvar transform = {};\n// string to ?\ntransform[\"string\"] = {\n    \"string\": identity,\n    \"array\": function(input) {\n        return stringToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"string\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return stringToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": function(input) {\n        return stringToArrayLike(input, nodejsUtils.allocBuffer(input.length));\n    }\n};\n// array to ?\ntransform[\"array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": identity,\n    \"arraybuffer\": function(input) {\n        return new Uint8Array(input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n// arraybuffer to ?\ntransform[\"arraybuffer\"] = {\n    \"string\": function(input) {\n        return arrayLikeToString(new Uint8Array(input));\n    },\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(new Uint8Array(input), new Array(input.byteLength));\n    },\n    \"arraybuffer\": identity,\n    \"uint8array\": function(input) {\n        return new Uint8Array(input);\n    },\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(new Uint8Array(input));\n    }\n};\n// uint8array to ?\ntransform[\"uint8array\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return input.buffer;\n    },\n    \"uint8array\": identity,\n    \"nodebuffer\": function(input) {\n        return nodejsUtils.newBufferFrom(input);\n    }\n};\n// nodebuffer to ?\ntransform[\"nodebuffer\"] = {\n    \"string\": arrayLikeToString,\n    \"array\": function(input) {\n        return arrayLikeToArrayLike(input, new Array(input.length));\n    },\n    \"arraybuffer\": function(input) {\n        return transform[\"nodebuffer\"][\"uint8array\"](input).buffer;\n    },\n    \"uint8array\": function(input) {\n        return arrayLikeToArrayLike(input, new Uint8Array(input.length));\n    },\n    \"nodebuffer\": identity\n};\n/**\n * Transform an input into any type.\n * The supported output type are : string, array, uint8array, arraybuffer, nodebuffer.\n * If no output type is specified, the unmodified input will be returned.\n * @param {String} outputType the output type.\n * @param {String|Array|ArrayBuffer|Uint8Array|Buffer} input the input to convert.\n * @throws {Error} an Error if the browser doesn't support the requested output type.\n */ exports.transformTo = function(outputType, input) {\n    if (!input) {\n        // undefined, null, etc\n        // an empty string won't harm.\n        input = \"\";\n    }\n    if (!outputType) {\n        return input;\n    }\n    exports.checkSupport(outputType);\n    var inputType = exports.getTypeOf(input);\n    var result = transform[inputType][outputType](input);\n    return result;\n};\n/**\n * Resolve all relative path components, \".\" and \"..\", in a path. If these relative components\n * traverse above the root then the resulting path will only contain the final path component.\n *\n * All empty components, e.g. \"//\", are removed.\n * @param {string} path A path with / or \\ separators\n * @returns {string} The path with all relative path components resolved.\n */ exports.resolve = function(path) {\n    var parts = path.split(\"/\");\n    var result = [];\n    for(var index = 0; index < parts.length; index++){\n        var part = parts[index];\n        // Allow the first and last component to be empty for trailing slashes.\n        if (part === \".\" || part === \"\" && index !== 0 && index !== parts.length - 1) {\n            continue;\n        } else if (part === \"..\") {\n            result.pop();\n        } else {\n            result.push(part);\n        }\n    }\n    return result.join(\"/\");\n};\n/**\n * Return the type of the input.\n * The type will be in a format valid for JSZip.utils.transformTo : string, array, uint8array, arraybuffer.\n * @param {Object} input the input to identify.\n * @return {String} the (lowercase) type of the input.\n */ exports.getTypeOf = function(input) {\n    if (typeof input === \"string\") {\n        return \"string\";\n    }\n    if (Object.prototype.toString.call(input) === \"[object Array]\") {\n        return \"array\";\n    }\n    if (support.nodebuffer && nodejsUtils.isBuffer(input)) {\n        return \"nodebuffer\";\n    }\n    if (support.uint8array && input instanceof Uint8Array) {\n        return \"uint8array\";\n    }\n    if (support.arraybuffer && input instanceof ArrayBuffer) {\n        return \"arraybuffer\";\n    }\n};\n/**\n * Throw an exception if the type is not supported.\n * @param {String} type the type to check.\n * @throws {Error} an Error if the browser doesn't support the requested type.\n */ exports.checkSupport = function(type) {\n    var supported = support[type.toLowerCase()];\n    if (!supported) {\n        throw new Error(type + \" is not supported by this platform\");\n    }\n};\nexports.MAX_VALUE_16BITS = 65535;\nexports.MAX_VALUE_32BITS = -1; // well, \"\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\\xFF\" is parsed as -1\n/**\n * Prettify a string read as binary.\n * @param {string} str the string to prettify.\n * @return {string} a pretty string.\n */ exports.pretty = function(str) {\n    var res = \"\", code, i;\n    for(i = 0; i < (str || \"\").length; i++){\n        code = str.charCodeAt(i);\n        res += \"\\\\x\" + (code < 16 ? \"0\" : \"\") + code.toString(16).toUpperCase();\n    }\n    return res;\n};\n/**\n * Defer the call of a function.\n * @param {Function} callback the function to call asynchronously.\n * @param {Array} args the arguments to give to the callback.\n */ exports.delay = function(callback, args, self1) {\n    setImmediate(function() {\n        callback.apply(self1 || null, args || []);\n    });\n};\n/**\n * Extends a prototype with an other, without calling a constructor with\n * side effects. Inspired by nodejs' `utils.inherits`\n * @param {Function} ctor the constructor to augment\n * @param {Function} superCtor the parent constructor to use\n */ exports.inherits = function(ctor, superCtor) {\n    var Obj = function() {};\n    Obj.prototype = superCtor.prototype;\n    ctor.prototype = new Obj();\n};\n/**\n * Merge the objects passed as parameters into a new one.\n * @private\n * @param {...Object} var_args All objects to merge.\n * @return {Object} a new object with the data of the others.\n */ exports.extend = function() {\n    var result = {}, i, attr;\n    for(i = 0; i < arguments.length; i++){\n        for(attr in arguments[i]){\n            if (Object.prototype.hasOwnProperty.call(arguments[i], attr) && typeof result[attr] === \"undefined\") {\n                result[attr] = arguments[i][attr];\n            }\n        }\n    }\n    return result;\n};\n/**\n * Transform arbitrary content into a Promise.\n * @param {String} name a name for the content being processed.\n * @param {Object} inputData the content to process.\n * @param {Boolean} isBinary true if the content is not an unicode string\n * @param {Boolean} isOptimizedBinaryString true if the string content only has one byte per character.\n * @param {Boolean} isBase64 true if the string content is encoded with base64.\n * @return {Promise} a promise in a format usable by JSZip.\n */ exports.prepareContent = function(name, inputData, isBinary, isOptimizedBinaryString, isBase64) {\n    // if inputData is already a promise, this flatten it.\n    var promise = external.Promise.resolve(inputData).then(function(data) {\n        var isBlob = support.blob && (data instanceof Blob || [\n            \"[object File]\",\n            \"[object Blob]\"\n        ].indexOf(Object.prototype.toString.call(data)) !== -1);\n        if (isBlob && typeof FileReader !== \"undefined\") {\n            return new external.Promise(function(resolve, reject) {\n                var reader = new FileReader();\n                reader.onload = function(e) {\n                    resolve(e.target.result);\n                };\n                reader.onerror = function(e) {\n                    reject(e.target.error);\n                };\n                reader.readAsArrayBuffer(data);\n            });\n        } else {\n            return data;\n        }\n    });\n    return promise.then(function(data) {\n        var dataType = exports.getTypeOf(data);\n        if (!dataType) {\n            return external.Promise.reject(new Error(\"Can't read the data of '\" + name + \"'. Is it \" + \"in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?\"));\n        }\n        // special case : it's way easier to work with Uint8Array than with ArrayBuffer\n        if (dataType === \"arraybuffer\") {\n            data = exports.transformTo(\"uint8array\", data);\n        } else if (dataType === \"string\") {\n            if (isBase64) {\n                data = base64.decode(data);\n            } else if (isBinary) {\n                // optimizedBinaryString === true means that the file has already been filtered with a 0xFF mask\n                if (isOptimizedBinaryString !== true) {\n                    // this is a string, not in a base64 format.\n                    // Be sure that this is a correct \"binary string\"\n                    data = string2binary(data);\n                }\n            }\n        }\n        return data;\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/utils.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntries.js":
/*!**********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntries.js ***!
  \**********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar sig = __webpack_require__(/*! ./signature */ \"(ssr)/./node_modules/jszip/lib/signature.js\");\nvar ZipEntry = __webpack_require__(/*! ./zipEntry */ \"(ssr)/./node_modules/jszip/lib/zipEntry.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\n//  class ZipEntries {{{\n/**\n * All the entries in the zip file.\n * @constructor\n * @param {Object} loadOptions Options for loading the stream.\n */ function ZipEntries(loadOptions) {\n    this.files = [];\n    this.loadOptions = loadOptions;\n}\nZipEntries.prototype = {\n    /**\n     * Check that the reader is on the specified signature.\n     * @param {string} expectedSignature the expected signature.\n     * @throws {Error} if it is an other signature.\n     */ checkSignature: function(expectedSignature) {\n        if (!this.reader.readAndCheckSignature(expectedSignature)) {\n            this.reader.index -= 4;\n            var signature = this.reader.readString(4);\n            throw new Error(\"Corrupted zip or bug: unexpected signature \" + \"(\" + utils.pretty(signature) + \", expected \" + utils.pretty(expectedSignature) + \")\");\n        }\n    },\n    /**\n     * Check if the given signature is at the given index.\n     * @param {number} askedIndex the index to check.\n     * @param {string} expectedSignature the signature to expect.\n     * @return {boolean} true if the signature is here, false otherwise.\n     */ isSignature: function(askedIndex, expectedSignature) {\n        var currentIndex = this.reader.index;\n        this.reader.setIndex(askedIndex);\n        var signature = this.reader.readString(4);\n        var result = signature === expectedSignature;\n        this.reader.setIndex(currentIndex);\n        return result;\n    },\n    /**\n     * Read the end of the central directory.\n     */ readBlockEndOfCentral: function() {\n        this.diskNumber = this.reader.readInt(2);\n        this.diskWithCentralDirStart = this.reader.readInt(2);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(2);\n        this.centralDirRecords = this.reader.readInt(2);\n        this.centralDirSize = this.reader.readInt(4);\n        this.centralDirOffset = this.reader.readInt(4);\n        this.zipCommentLength = this.reader.readInt(2);\n        // warning : the encoding depends of the system locale\n        // On a linux machine with LANG=en_US.utf8, this field is utf8 encoded.\n        // On a windows machine, this field is encoded with the localized windows code page.\n        var zipComment = this.reader.readData(this.zipCommentLength);\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        // To get consistent behavior with the generation part, we will assume that\n        // this is utf8 encoded unless specified otherwise.\n        var decodeContent = utils.transformTo(decodeParamType, zipComment);\n        this.zipComment = this.loadOptions.decodeFileName(decodeContent);\n    },\n    /**\n     * Read the end of the Zip 64 central directory.\n     * Not merged with the method readEndOfCentral :\n     * The end of central can coexist with its Zip64 brother,\n     * I don't want to read the wrong number of bytes !\n     */ readBlockZip64EndOfCentral: function() {\n        this.zip64EndOfCentralSize = this.reader.readInt(8);\n        this.reader.skip(4);\n        // this.versionMadeBy = this.reader.readString(2);\n        // this.versionNeeded = this.reader.readInt(2);\n        this.diskNumber = this.reader.readInt(4);\n        this.diskWithCentralDirStart = this.reader.readInt(4);\n        this.centralDirRecordsOnThisDisk = this.reader.readInt(8);\n        this.centralDirRecords = this.reader.readInt(8);\n        this.centralDirSize = this.reader.readInt(8);\n        this.centralDirOffset = this.reader.readInt(8);\n        this.zip64ExtensibleData = {};\n        var extraDataSize = this.zip64EndOfCentralSize - 44, index = 0, extraFieldId, extraFieldLength, extraFieldValue;\n        while(index < extraDataSize){\n            extraFieldId = this.reader.readInt(2);\n            extraFieldLength = this.reader.readInt(4);\n            extraFieldValue = this.reader.readData(extraFieldLength);\n            this.zip64ExtensibleData[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n    },\n    /**\n     * Read the end of the Zip 64 central directory locator.\n     */ readBlockZip64EndOfCentralLocator: function() {\n        this.diskWithZip64CentralDirStart = this.reader.readInt(4);\n        this.relativeOffsetEndOfZip64CentralDir = this.reader.readInt(8);\n        this.disksCount = this.reader.readInt(4);\n        if (this.disksCount > 1) {\n            throw new Error(\"Multi-volumes zip are not supported\");\n        }\n    },\n    /**\n     * Read the local files, based on the offset read in the central part.\n     */ readLocalFiles: function() {\n        var i, file;\n        for(i = 0; i < this.files.length; i++){\n            file = this.files[i];\n            this.reader.setIndex(file.localHeaderOffset);\n            this.checkSignature(sig.LOCAL_FILE_HEADER);\n            file.readLocalPart(this.reader);\n            file.handleUTF8();\n            file.processAttributes();\n        }\n    },\n    /**\n     * Read the central directory.\n     */ readCentralDir: function() {\n        var file;\n        this.reader.setIndex(this.centralDirOffset);\n        while(this.reader.readAndCheckSignature(sig.CENTRAL_FILE_HEADER)){\n            file = new ZipEntry({\n                zip64: this.zip64\n            }, this.loadOptions);\n            file.readCentralPart(this.reader);\n            this.files.push(file);\n        }\n        if (this.centralDirRecords !== this.files.length) {\n            if (this.centralDirRecords !== 0 && this.files.length === 0) {\n                // We expected some records but couldn't find ANY.\n                // This is really suspicious, as if something went wrong.\n                throw new Error(\"Corrupted zip or bug: expected \" + this.centralDirRecords + \" records in central dir, got \" + this.files.length);\n            } else {\n            // We found some records but not all.\n            // Something is wrong but we got something for the user: no error here.\n            // console.warn(\"expected\", this.centralDirRecords, \"records in central dir, got\", this.files.length);\n            }\n        }\n    },\n    /**\n     * Read the end of central directory.\n     */ readEndOfCentral: function() {\n        var offset = this.reader.lastIndexOfSignature(sig.CENTRAL_DIRECTORY_END);\n        if (offset < 0) {\n            // Check if the content is a truncated zip or complete garbage.\n            // A \"LOCAL_FILE_HEADER\" is not required at the beginning (auto\n            // extractible zip for example) but it can give a good hint.\n            // If an ajax request was used without responseType, we will also\n            // get unreadable data.\n            var isGarbage = !this.isSignature(0, sig.LOCAL_FILE_HEADER);\n            if (isGarbage) {\n                throw new Error(\"Can't find end of central directory : is this a zip file ? \" + \"If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html\");\n            } else {\n                throw new Error(\"Corrupted zip: can't find end of central directory\");\n            }\n        }\n        this.reader.setIndex(offset);\n        var endOfCentralDirOffset = offset;\n        this.checkSignature(sig.CENTRAL_DIRECTORY_END);\n        this.readBlockEndOfCentral();\n        /* extract from the zip spec :\n            4)  If one of the fields in the end of central directory\n                record is too small to hold required data, the field\n                should be set to -1 (0xFFFF or 0xFFFFFFFF) and the\n                ZIP64 format record should be created.\n            5)  The end of central directory record and the\n                Zip64 end of central directory locator record must\n                reside on the same disk when splitting or spanning\n                an archive.\n         */ if (this.diskNumber === utils.MAX_VALUE_16BITS || this.diskWithCentralDirStart === utils.MAX_VALUE_16BITS || this.centralDirRecordsOnThisDisk === utils.MAX_VALUE_16BITS || this.centralDirRecords === utils.MAX_VALUE_16BITS || this.centralDirSize === utils.MAX_VALUE_32BITS || this.centralDirOffset === utils.MAX_VALUE_32BITS) {\n            this.zip64 = true;\n            /*\n            Warning : the zip64 extension is supported, but ONLY if the 64bits integer read from\n            the zip file can fit into a 32bits integer. This cannot be solved : JavaScript represents\n            all numbers as 64-bit double precision IEEE 754 floating point numbers.\n            So, we have 53bits for integers and bitwise operations treat everything as 32bits.\n            see https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators\n            and http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf section 8.5\n            */ // should look for a zip64 EOCD locator\n            offset = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            if (offset < 0) {\n                throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory locator\");\n            }\n            this.reader.setIndex(offset);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_LOCATOR);\n            this.readBlockZip64EndOfCentralLocator();\n            // now the zip64 EOCD record\n            if (!this.isSignature(this.relativeOffsetEndOfZip64CentralDir, sig.ZIP64_CENTRAL_DIRECTORY_END)) {\n                // console.warn(\"ZIP64 end of central directory not where expected.\");\n                this.relativeOffsetEndOfZip64CentralDir = this.reader.lastIndexOfSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n                if (this.relativeOffsetEndOfZip64CentralDir < 0) {\n                    throw new Error(\"Corrupted zip: can't find the ZIP64 end of central directory\");\n                }\n            }\n            this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir);\n            this.checkSignature(sig.ZIP64_CENTRAL_DIRECTORY_END);\n            this.readBlockZip64EndOfCentral();\n        }\n        var expectedEndOfCentralDirOffset = this.centralDirOffset + this.centralDirSize;\n        if (this.zip64) {\n            expectedEndOfCentralDirOffset += 20; // end of central dir 64 locator\n            expectedEndOfCentralDirOffset += 12 /* should not include the leading 12 bytes */  + this.zip64EndOfCentralSize;\n        }\n        var extraBytes = endOfCentralDirOffset - expectedEndOfCentralDirOffset;\n        if (extraBytes > 0) {\n            // console.warn(extraBytes, \"extra bytes at beginning or within zipfile\");\n            if (this.isSignature(endOfCentralDirOffset, sig.CENTRAL_FILE_HEADER)) {\n            // The offsets seem wrong, but we have something at the specified offset.\n            // So… we keep it.\n            } else {\n                // the offset is wrong, update the \"zero\" of the reader\n                // this happens if data has been prepended (crx files for example)\n                this.reader.zero = extraBytes;\n            }\n        } else if (extraBytes < 0) {\n            throw new Error(\"Corrupted zip: missing \" + Math.abs(extraBytes) + \" bytes.\");\n        }\n    },\n    prepareReader: function(data) {\n        this.reader = readerFor(data);\n    },\n    /**\n     * Read a zip file and create ZipEntries.\n     * @param {String|ArrayBuffer|Uint8Array|Buffer} data the binary string representing a zip file.\n     */ load: function(data) {\n        this.prepareReader(data);\n        this.readEndOfCentral();\n        this.readCentralDir();\n        this.readLocalFiles();\n    }\n};\n// }}} end of ZipEntries\nmodule.exports = ZipEntries;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntries.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipEntry.js":
/*!********************************************!*\
  !*** ./node_modules/jszip/lib/zipEntry.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar readerFor = __webpack_require__(/*! ./reader/readerFor */ \"(ssr)/./node_modules/jszip/lib/reader/readerFor.js\");\nvar utils = __webpack_require__(/*! ./utils */ \"(ssr)/./node_modules/jszip/lib/utils.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar crc32fn = __webpack_require__(/*! ./crc32 */ \"(ssr)/./node_modules/jszip/lib/crc32.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar compressions = __webpack_require__(/*! ./compressions */ \"(ssr)/./node_modules/jszip/lib/compressions.js\");\nvar support = __webpack_require__(/*! ./support */ \"(ssr)/./node_modules/jszip/lib/support.js\");\nvar MADE_BY_DOS = 0x00;\nvar MADE_BY_UNIX = 0x03;\n/**\n * Find a compression registered in JSZip.\n * @param {string} compressionMethod the method magic to find.\n * @return {Object|null} the JSZip compression object, null if none found.\n */ var findCompression = function(compressionMethod) {\n    for(var method in compressions){\n        if (!Object.prototype.hasOwnProperty.call(compressions, method)) {\n            continue;\n        }\n        if (compressions[method].magic === compressionMethod) {\n            return compressions[method];\n        }\n    }\n    return null;\n};\n// class ZipEntry {{{\n/**\n * An entry in the zip file.\n * @constructor\n * @param {Object} options Options of the current file.\n * @param {Object} loadOptions Options for loading the stream.\n */ function ZipEntry(options, loadOptions) {\n    this.options = options;\n    this.loadOptions = loadOptions;\n}\nZipEntry.prototype = {\n    /**\n     * say if the file is encrypted.\n     * @return {boolean} true if the file is encrypted, false otherwise.\n     */ isEncrypted: function() {\n        // bit 1 is set\n        return (this.bitFlag & 0x0001) === 0x0001;\n    },\n    /**\n     * say if the file has utf-8 filename/comment.\n     * @return {boolean} true if the filename/comment is in utf-8, false otherwise.\n     */ useUTF8: function() {\n        // bit 11 is set\n        return (this.bitFlag & 0x0800) === 0x0800;\n    },\n    /**\n     * Read the local part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */ readLocalPart: function(reader) {\n        var compression, localExtraFieldsLength;\n        // we already know everything from the central dir !\n        // If the central dir data are false, we are doomed.\n        // On the bright side, the local part is scary  : zip64, data descriptors, both, etc.\n        // The less data we get here, the more reliable this should be.\n        // Let's skip the whole header and dash to the data !\n        reader.skip(22);\n        // in some zip created on windows, the filename stored in the central dir contains \\ instead of /.\n        // Strangely, the filename here is OK.\n        // I would love to treat these zip files as corrupted (see http://www.info-zip.org/FAQ.html#backslashes\n        // or APPNOTE#********, \"All slashes MUST be forward slashes '/'\") but there are a lot of bad zip generators...\n        // Search \"unzip mismatching \"local\" filename continuing with \"central\" filename version\" on\n        // the internet.\n        //\n        // I think I see the logic here : the central directory is used to display\n        // content and the local directory is used to extract the files. Mixing / and \\\n        // may be used to display \\ to windows users and use / when extracting the files.\n        // Unfortunately, this lead also to some issues : http://seclists.org/fulldisclosure/2009/Sep/394\n        this.fileNameLength = reader.readInt(2);\n        localExtraFieldsLength = reader.readInt(2); // can't be sure this will be the same as the central dir\n        // the fileName is stored as binary data, the handleUTF8 method will take care of the encoding.\n        this.fileName = reader.readData(this.fileNameLength);\n        reader.skip(localExtraFieldsLength);\n        if (this.compressedSize === -1 || this.uncompressedSize === -1) {\n            throw new Error(\"Bug or corrupted zip : didn't get enough information from the central directory \" + \"(compressedSize === -1 || uncompressedSize === -1)\");\n        }\n        compression = findCompression(this.compressionMethod);\n        if (compression === null) {\n            throw new Error(\"Corrupted zip : compression \" + utils.pretty(this.compressionMethod) + \" unknown (inner file : \" + utils.transformTo(\"string\", this.fileName) + \")\");\n        }\n        this.decompressed = new CompressedObject(this.compressedSize, this.uncompressedSize, this.crc32, compression, reader.readData(this.compressedSize));\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */ readCentralPart: function(reader) {\n        this.versionMadeBy = reader.readInt(2);\n        reader.skip(2);\n        // this.versionNeeded = reader.readInt(2);\n        this.bitFlag = reader.readInt(2);\n        this.compressionMethod = reader.readString(2);\n        this.date = reader.readDate();\n        this.crc32 = reader.readInt(4);\n        this.compressedSize = reader.readInt(4);\n        this.uncompressedSize = reader.readInt(4);\n        var fileNameLength = reader.readInt(2);\n        this.extraFieldsLength = reader.readInt(2);\n        this.fileCommentLength = reader.readInt(2);\n        this.diskNumberStart = reader.readInt(2);\n        this.internalFileAttributes = reader.readInt(2);\n        this.externalFileAttributes = reader.readInt(4);\n        this.localHeaderOffset = reader.readInt(4);\n        if (this.isEncrypted()) {\n            throw new Error(\"Encrypted zip are not supported\");\n        }\n        // will be read in the local part, see the comments there\n        reader.skip(fileNameLength);\n        this.readExtraFields(reader);\n        this.parseZIP64ExtraField(reader);\n        this.fileComment = reader.readData(this.fileCommentLength);\n    },\n    /**\n     * Parse the external file attributes and get the unix/dos permissions.\n     */ processAttributes: function() {\n        this.unixPermissions = null;\n        this.dosPermissions = null;\n        var madeBy = this.versionMadeBy >> 8;\n        // Check if we have the DOS directory flag set.\n        // We look for it in the DOS and UNIX permissions\n        // but some unknown platform could set it as a compatibility flag.\n        this.dir = this.externalFileAttributes & 0x0010 ? true : false;\n        if (madeBy === MADE_BY_DOS) {\n            // first 6 bits (0 to 5)\n            this.dosPermissions = this.externalFileAttributes & 0x3F;\n        }\n        if (madeBy === MADE_BY_UNIX) {\n            this.unixPermissions = this.externalFileAttributes >> 16 & 0xFFFF;\n        // the octal permissions are in (this.unixPermissions & 0x01FF).toString(8);\n        }\n        // fail safe : if the name ends with a / it probably means a folder\n        if (!this.dir && this.fileNameStr.slice(-1) === \"/\") {\n            this.dir = true;\n        }\n    },\n    /**\n     * Parse the ZIP64 extra field and merge the info in the current ZipEntry.\n     * @param {DataReader} reader the reader to use.\n     */ parseZIP64ExtraField: function() {\n        if (!this.extraFields[0x0001]) {\n            return;\n        }\n        // should be something, preparing the extra reader\n        var extraReader = readerFor(this.extraFields[0x0001].value);\n        // I really hope that these 64bits integer can fit in 32 bits integer, because js\n        // won't let us have more.\n        if (this.uncompressedSize === utils.MAX_VALUE_32BITS) {\n            this.uncompressedSize = extraReader.readInt(8);\n        }\n        if (this.compressedSize === utils.MAX_VALUE_32BITS) {\n            this.compressedSize = extraReader.readInt(8);\n        }\n        if (this.localHeaderOffset === utils.MAX_VALUE_32BITS) {\n            this.localHeaderOffset = extraReader.readInt(8);\n        }\n        if (this.diskNumberStart === utils.MAX_VALUE_32BITS) {\n            this.diskNumberStart = extraReader.readInt(4);\n        }\n    },\n    /**\n     * Read the central part of a zip file and add the info in this object.\n     * @param {DataReader} reader the reader to use.\n     */ readExtraFields: function(reader) {\n        var end = reader.index + this.extraFieldsLength, extraFieldId, extraFieldLength, extraFieldValue;\n        if (!this.extraFields) {\n            this.extraFields = {};\n        }\n        while(reader.index + 4 < end){\n            extraFieldId = reader.readInt(2);\n            extraFieldLength = reader.readInt(2);\n            extraFieldValue = reader.readData(extraFieldLength);\n            this.extraFields[extraFieldId] = {\n                id: extraFieldId,\n                length: extraFieldLength,\n                value: extraFieldValue\n            };\n        }\n        reader.setIndex(end);\n    },\n    /**\n     * Apply an UTF8 transformation if needed.\n     */ handleUTF8: function() {\n        var decodeParamType = support.uint8array ? \"uint8array\" : \"array\";\n        if (this.useUTF8()) {\n            this.fileNameStr = utf8.utf8decode(this.fileName);\n            this.fileCommentStr = utf8.utf8decode(this.fileComment);\n        } else {\n            var upath = this.findExtraFieldUnicodePath();\n            if (upath !== null) {\n                this.fileNameStr = upath;\n            } else {\n                // ASCII text or unsupported code page\n                var fileNameByteArray = utils.transformTo(decodeParamType, this.fileName);\n                this.fileNameStr = this.loadOptions.decodeFileName(fileNameByteArray);\n            }\n            var ucomment = this.findExtraFieldUnicodeComment();\n            if (ucomment !== null) {\n                this.fileCommentStr = ucomment;\n            } else {\n                // ASCII text or unsupported code page\n                var commentByteArray = utils.transformTo(decodeParamType, this.fileComment);\n                this.fileCommentStr = this.loadOptions.decodeFileName(commentByteArray);\n            }\n        }\n    },\n    /**\n     * Find the unicode path declared in the extra field, if any.\n     * @return {String} the unicode path, null otherwise.\n     */ findExtraFieldUnicodePath: function() {\n        var upathField = this.extraFields[0x7075];\n        if (upathField) {\n            var extraReader = readerFor(upathField.value);\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n            // the crc of the filename changed, this field is out of date.\n            if (crc32fn(this.fileName) !== extraReader.readInt(4)) {\n                return null;\n            }\n            return utf8.utf8decode(extraReader.readData(upathField.length - 5));\n        }\n        return null;\n    },\n    /**\n     * Find the unicode comment declared in the extra field, if any.\n     * @return {String} the unicode comment, null otherwise.\n     */ findExtraFieldUnicodeComment: function() {\n        var ucommentField = this.extraFields[0x6375];\n        if (ucommentField) {\n            var extraReader = readerFor(ucommentField.value);\n            // wrong version\n            if (extraReader.readInt(1) !== 1) {\n                return null;\n            }\n            // the crc of the comment changed, this field is out of date.\n            if (crc32fn(this.fileComment) !== extraReader.readInt(4)) {\n                return null;\n            }\n            return utf8.utf8decode(extraReader.readData(ucommentField.length - 5));\n        }\n        return null;\n    }\n};\nmodule.exports = ZipEntry;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipEntry.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/jszip/lib/zipObject.js":
/*!*********************************************!*\
  !*** ./node_modules/jszip/lib/zipObject.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\nvar StreamHelper = __webpack_require__(/*! ./stream/StreamHelper */ \"(ssr)/./node_modules/jszip/lib/stream/StreamHelper.js\");\nvar DataWorker = __webpack_require__(/*! ./stream/DataWorker */ \"(ssr)/./node_modules/jszip/lib/stream/DataWorker.js\");\nvar utf8 = __webpack_require__(/*! ./utf8 */ \"(ssr)/./node_modules/jszip/lib/utf8.js\");\nvar CompressedObject = __webpack_require__(/*! ./compressedObject */ \"(ssr)/./node_modules/jszip/lib/compressedObject.js\");\nvar GenericWorker = __webpack_require__(/*! ./stream/GenericWorker */ \"(ssr)/./node_modules/jszip/lib/stream/GenericWorker.js\");\n/**\n * A simple object representing a file in the zip file.\n * @constructor\n * @param {string} name the name of the file\n * @param {String|ArrayBuffer|Uint8Array|Buffer} data the data\n * @param {Object} options the options of the file\n */ var ZipObject = function(name, data, options) {\n    this.name = name;\n    this.dir = options.dir;\n    this.date = options.date;\n    this.comment = options.comment;\n    this.unixPermissions = options.unixPermissions;\n    this.dosPermissions = options.dosPermissions;\n    this._data = data;\n    this._dataBinary = options.binary;\n    // keep only the compression\n    this.options = {\n        compression: options.compression,\n        compressionOptions: options.compressionOptions\n    };\n};\nZipObject.prototype = {\n    /**\n     * Create an internal stream for the content of this object.\n     * @param {String} type the type of each chunk.\n     * @return StreamHelper the stream.\n     */ internalStream: function(type) {\n        var result = null, outputType = \"string\";\n        try {\n            if (!type) {\n                throw new Error(\"No output type specified.\");\n            }\n            outputType = type.toLowerCase();\n            var askUnicodeString = outputType === \"string\" || outputType === \"text\";\n            if (outputType === \"binarystring\" || outputType === \"text\") {\n                outputType = \"string\";\n            }\n            result = this._decompressWorker();\n            var isUnicodeString = !this._dataBinary;\n            if (isUnicodeString && !askUnicodeString) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            if (!isUnicodeString && askUnicodeString) {\n                result = result.pipe(new utf8.Utf8DecodeWorker());\n            }\n        } catch (e) {\n            result = new GenericWorker(\"error\");\n            result.error(e);\n        }\n        return new StreamHelper(result, outputType, \"\");\n    },\n    /**\n     * Prepare the content in the asked type.\n     * @param {String} type the type of the result.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Promise the promise of the result.\n     */ async: function(type, onUpdate) {\n        return this.internalStream(type).accumulate(onUpdate);\n    },\n    /**\n     * Prepare the content as a nodejs stream.\n     * @param {String} type the type of each chunk.\n     * @param {Function} onUpdate a function to call on each internal update.\n     * @return Stream the stream.\n     */ nodeStream: function(type, onUpdate) {\n        return this.internalStream(type || \"nodebuffer\").toNodejsStream(onUpdate);\n    },\n    /**\n     * Return a worker for the compressed content.\n     * @private\n     * @param {Object} compression the compression object to use.\n     * @param {Object} compressionOptions the options to use when compressing.\n     * @return Worker the worker.\n     */ _compressWorker: function(compression, compressionOptions) {\n        if (this._data instanceof CompressedObject && this._data.compression.magic === compression.magic) {\n            return this._data.getCompressedWorker();\n        } else {\n            var result = this._decompressWorker();\n            if (!this._dataBinary) {\n                result = result.pipe(new utf8.Utf8EncodeWorker());\n            }\n            return CompressedObject.createWorkerFrom(result, compression, compressionOptions);\n        }\n    },\n    /**\n     * Return a worker for the decompressed content.\n     * @private\n     * @return Worker the worker.\n     */ _decompressWorker: function() {\n        if (this._data instanceof CompressedObject) {\n            return this._data.getContentWorker();\n        } else if (this._data instanceof GenericWorker) {\n            return this._data;\n        } else {\n            return new DataWorker(this._data);\n        }\n    }\n};\nvar removedMethods = [\n    \"asText\",\n    \"asBinary\",\n    \"asNodeBuffer\",\n    \"asUint8Array\",\n    \"asArrayBuffer\"\n];\nvar removedFn = function() {\n    throw new Error(\"This method has been removed in JSZip 3.0, please check the upgrade guide.\");\n};\nfor(var i = 0; i < removedMethods.length; i++){\n    ZipObject.prototype[removedMethods[i]] = removedFn;\n}\nmodule.exports = ZipObject;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/jszip/lib/zipObject.js\n");

/***/ })

};
;