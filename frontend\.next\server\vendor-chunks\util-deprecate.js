"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/util-deprecate";
exports.ids = ["vendor-chunks/util-deprecate"];
exports.modules = {

/***/ "(ssr)/./node_modules/util-deprecate/node.js":
/*!*********************************************!*\
  !*** ./node_modules/util-deprecate/node.js ***!
  \*********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * For Node.js, simply re-export the core `util.deprecate` function.\n */ \nmodule.exports = __webpack_require__(/*! util */ \"util\").deprecate;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXRpbC1kZXByZWNhdGUvbm9kZS5qcyIsIm1hcHBpbmdzIjoiQUFDQTs7Q0FFQztBQUVEQSxrRUFBMEMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly96dGVjaGVuZ2luZWVyaW5nLy4vbm9kZV9tb2R1bGVzL3V0aWwtZGVwcmVjYXRlL25vZGUuanM/NjRkNyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbi8qKlxuICogRm9yIE5vZGUuanMsIHNpbXBseSByZS1leHBvcnQgdGhlIGNvcmUgYHV0aWwuZGVwcmVjYXRlYCBmdW5jdGlvbi5cbiAqL1xuXG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJ3V0aWwnKS5kZXByZWNhdGU7XG4iXSwibmFtZXMiOlsibW9kdWxlIiwiZXhwb3J0cyIsInJlcXVpcmUiLCJkZXByZWNhdGUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/util-deprecate/node.js\n");

/***/ })

};
;