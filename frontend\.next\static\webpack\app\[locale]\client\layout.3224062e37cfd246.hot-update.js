"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/layout",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/sidebar.jsx":
/*!*********************************************!*\
  !*** ./src/app/[locale]/client/sidebar.jsx ***!
  \*********************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ Sidebar; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-user.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield-check.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/code-xml.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=CodeXml,CreditCard,Globe,Headset,Server,ShieldCheck,ShoppingCart,UserCircle!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headset.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_11___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_11__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst navigation = [\n    {\n        name: \"profile\",\n        href: \"/client/profile\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        checkHref: \"/client/profile\"\n    },\n    {\n        name: \"cart\",\n        href: \"/client/cart\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        checkHref: \"/client/cart\"\n    },\n    {\n        name: \"hosting_plans\",\n        href: \"/client/hosting-plans\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        checkHref: \"/client/hosting-plans\"\n    },\n    {\n        name: \"ssl_certificates\",\n        href: \"/client/ssl-certificates\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        checkHref: \"/client/ssl-certificates\"\n    },\n    {\n        name: \"domains\",\n        href: \"/client/domains\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        checkHref: \"/client/domains\"\n    },\n    {\n        name: \"web_dev.title\",\n        href: \"/client/web-development\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        checkHref: \"/client/web-development\"\n    },\n    {\n        name: \"payment_history\",\n        href: \"/client/payment-history\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        checkHref: \"/client/payment\"\n    },\n    {\n        name: \"support\",\n        href: \"/client/support/tickets\",\n        icon: _barrel_optimize_names_CodeXml_CreditCard_Globe_Headset_Server_ShieldCheck_ShoppingCart_UserCircle_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        checkHref: \"/client/support\"\n    }\n];\nfunction Sidebar(param) {\n    let { isSidebarOpen, setIsSidebarOpen, isMobile } = param;\n    _s();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const normalizedPath = pathname.replace(/^\\/[a-z]{2}/, \"\") || \"/\"; // Removes the locale prefix\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations)(\"client\");\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        id: \"sidebar\",\n        className: \"fixed lg:static inset-y-0 left-0 z-40 w-64 bg-white border-l transform \".concat(isSidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \" lg:translate-x-0 transition-transform duration-300 ease-in-out border-r border-gray-200 pt-16 lg:pt-0\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"mt-5 px-2 space-y-1\",\n            children: navigation.map((item)=>{\n                // const isActive = pathname.startsWith(item.href);\n                const isActive = normalizedPath.includes(item.checkHref);\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                    href: item.href,\n                    className: \"group flex items-center hover:bg-blue-400 hover:text-white px-3 py-3 text-sm font-medium rounded-lg transition-colors \".concat(isActive ? \"bg-blue-50 text-blue-600\" : \"text-gray-700  group-hover:text-gray-500\"),\n                    onClick: ()=>isMobile && setIsSidebarOpen(false),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                            className: \"mr-3 h-5 w-5 \"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n                            lineNumber: 95,\n                            columnNumber: 15\n                        }, this),\n                        t(\"\".concat(item.name))\n                    ]\n                }, item.name, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n                    lineNumber: 85,\n                    columnNumber: 13\n                }, this);\n            })\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\sidebar.jsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(Sidebar, \"ubWVzN2Z0wcQh3unbvhqt4fw9Wc=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_intl__WEBPACK_IMPORTED_MODULE_11__.useTranslations\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/sidebar.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js":
/*!***********************************************************!*\
  !*** ./node_modules/lucide-react/dist/esm/icons/globe.js ***!
  \***********************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   __iconNode: function() { return /* binding */ __iconNode; },\n/* harmony export */   \"default\": function() { return /* binding */ Globe; }\n/* harmony export */ });\n/* harmony import */ var _createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../createLucideIcon.js */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/createLucideIcon.js\");\n/**\n * @license lucide-react v0.475.0 - ISC\n *\n * This source code is licensed under the ISC license.\n * See the LICENSE file in the root directory of this source tree.\n */ \nconst __iconNode = [\n    [\n        \"circle\",\n        {\n            cx: \"12\",\n            cy: \"12\",\n            r: \"10\",\n            key: \"1mglay\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20\",\n            key: \"13o1zl\"\n        }\n    ],\n    [\n        \"path\",\n        {\n            d: \"M2 12h20\",\n            key: \"9i4pu4\"\n        }\n    ]\n];\nconst Globe = (0,_createLucideIcon_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"])(\"Globe\", __iconNode);\n //# sourceMappingURL=globe.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ2xvYmUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7Ozs7O0NBS0MsR0FFcUQ7QUFFdEQsTUFBTUMsYUFBYTtJQUNqQjtRQUFDO1FBQVU7WUFBRUMsSUFBSTtZQUFNQyxJQUFJO1lBQU1DLEdBQUc7WUFBTUMsS0FBSztRQUFTO0tBQUU7SUFDMUQ7UUFBQztRQUFRO1lBQUVDLEdBQUc7WUFBbURELEtBQUs7UUFBUztLQUFFO0lBQ2pGO1FBQUM7UUFBUTtZQUFFQyxHQUFHO1lBQVlELEtBQUs7UUFBUztLQUFFO0NBQzNDO0FBQ0QsTUFBTUUsUUFBUVAsZ0VBQWdCQSxDQUFDLFNBQVNDO0FBRUEsQ0FDeEMsaUNBQWlDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL25vZGVfbW9kdWxlcy9sdWNpZGUtcmVhY3QvZGlzdC9lc20vaWNvbnMvZ2xvYmUuanM/MGQ0YyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBsaWNlbnNlIGx1Y2lkZS1yZWFjdCB2MC40NzUuMCAtIElTQ1xuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIElTQyBsaWNlbnNlLlxuICogU2VlIHRoZSBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKi9cblxuaW1wb3J0IGNyZWF0ZUx1Y2lkZUljb24gZnJvbSAnLi4vY3JlYXRlTHVjaWRlSWNvbi5qcyc7XG5cbmNvbnN0IF9faWNvbk5vZGUgPSBbXG4gIFtcImNpcmNsZVwiLCB7IGN4OiBcIjEyXCIsIGN5OiBcIjEyXCIsIHI6IFwiMTBcIiwga2V5OiBcIjFtZ2xheVwiIH1dLFxuICBbXCJwYXRoXCIsIHsgZDogXCJNMTIgMmExNC41IDE0LjUgMCAwIDAgMCAyMCAxNC41IDE0LjUgMCAwIDAgMC0yMFwiLCBrZXk6IFwiMTNvMXpsXCIgfV0sXG4gIFtcInBhdGhcIiwgeyBkOiBcIk0yIDEyaDIwXCIsIGtleTogXCI5aTRwdTRcIiB9XVxuXTtcbmNvbnN0IEdsb2JlID0gY3JlYXRlTHVjaWRlSWNvbihcIkdsb2JlXCIsIF9faWNvbk5vZGUpO1xuXG5leHBvcnQgeyBfX2ljb25Ob2RlLCBHbG9iZSBhcyBkZWZhdWx0IH07XG4vLyMgc291cmNlTWFwcGluZ1VSTD1nbG9iZS5qcy5tYXBcbiJdLCJuYW1lcyI6WyJjcmVhdGVMdWNpZGVJY29uIiwiX19pY29uTm9kZSIsImN4IiwiY3kiLCJyIiwia2V5IiwiZCIsIkdsb2JlIiwiZGVmYXVsdCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\n"));

/***/ })

});