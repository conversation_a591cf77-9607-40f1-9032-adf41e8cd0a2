"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/(sharedPages)/hosting/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/page.jsx":
/*!*********************************************************!*\
  !*** ./src/app/[locale]/(sharedPages)/hosting/page.jsx ***!
  \*********************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/globe.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hand-coins.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cloud.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-up-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/gauge.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ArrowUpRight,ClockIcon,CloudIcon,GaugeIcon,GlobeIcon,HandCoins,HardDriveIcon,HeadphonesIcon,RocketIcon,ServerIcon,ShieldIcon,SparklesIcon,UsersIcon!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/headphones.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_home_Section__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/home/<USER>/ \"(app-pages-browser)/./src/components/home/<USER>");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Hosting() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations)(\"hosting\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    // Accordion state management\n    const [open, setOpen] = react__WEBPACK_IMPORTED_MODULE_2___default().useState(0);\n    const handleOpen = (value)=>setOpen(open === value ? 0 : value);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_home_Section__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"text-center mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                    variant: \"h1\",\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-extrabold text-gray-900 tracking-tight mb-4 sm:mb-6 animate-fade-in leading-tight\",\n                                    children: [\n                                        t(\"headline_start\"),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"ml-4 bg-gradient-to-r from-blue-600 via-indigo-600 to-purple-600 bg-clip-text text-transparent animate-gradient block sm:inline\",\n                                            children: t(\"headline_highlight\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 78,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 73,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                    variant: \"paragraph\",\n                                    className: \"text-base sm:text-lg xl:text-xl text-gray-600 mb-6 sm:mb-8 xl:mb-12 leading-relaxed animate-fade-in-up max-w-3xl mx-auto\",\n                                    children: t(\"subtext\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-12 sm:mt-16 grid grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 max-w-6xl mx-auto animate-fade-in-up animation-delay-1000\",\n                                    children: [\n                                        {\n                                            text: t(\"trust_indic.0\"),\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n                                            bgColor: \"bg-blue-50\",\n                                            iconColor: \"text-blue-600\"\n                                        },\n                                        {\n                                            text: t(\"trust_indic.1\"),\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                            bgColor: \"bg-green-50\",\n                                            iconColor: \"text-green-600\"\n                                        },\n                                        {\n                                            text: t(\"trust_indic.2\"),\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n                                            bgColor: \"bg-indigo-50\",\n                                            iconColor: \"text-indigo-600\"\n                                        },\n                                        {\n                                            text: t(\"trust_indic.3\"),\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n                                            bgColor: \"bg-teal-50\",\n                                            iconColor: \"text-teal-600\"\n                                        }\n                                    ].map((item, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                            className: \"group flex items-center justify-center gap-3 p-4 \".concat(item.bgColor, \" backdrop-blur-md border border-gray-200/50 rounded-xl shadow-sm hover:shadow-md hover:-translate-y-1 transition-all duration-300\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(item.icon, {\n                                                    className: \"h-6 w-6 \".concat(item.iconColor, \" flex-shrink-0 group-hover:scale-110 transition-transform duration-200\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 120,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                    variant: \"small\",\n                                                    className: \"text-sm sm:text-base text-gray-700 font-semibold leading-tight\",\n                                                    children: item.text\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, index, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 116,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                            lineNumber: 69,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            id: \"plans\",\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8 mt-10 sm:mt-18 lg:mt-26 px-4 sm:px-0 max-w-7xl mx-auto\",\n                            children: [\n                                {\n                                    href: \"/hosting/shared\",\n                                    icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n                                    title: t(\"hosting_types.0.title\"),\n                                    description: t(\"hosting_types.0.description\"),\n                                    price: t(\"hosting_types.0.price\")\n                                },\n                                {\n                                    href: \"/hosting/vps\",\n                                    icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n                                    title: t(\"hosting_types.1.title\"),\n                                    description: t(\"hosting_types.1.description\"),\n                                    price: t(\"hosting_types.1.price\")\n                                },\n                                {\n                                    href: \"/hosting/dedicated\",\n                                    icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n                                    title: t(\"hosting_types.2.title\"),\n                                    description: t(\"hosting_types.2.description\"),\n                                    price: t(\"hosting_types.2.price\")\n                                }\n                            ].map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                    className: \"group relative h-full bg-white border border-gray-200/50 rounded-2xl shadow-md hover:shadow-xl hover:-translate-y-2 transition-all duration-300 overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-blue-50/0 via-blue-50/0 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 166,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"p-6 sm:p-8 flex flex-col items-center relative z-10\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 bg-blue-50 rounded-full mb-6 group-hover:bg-blue-100 group-hover:scale-105 group-hover:shadow-sm transition-all duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(plan.icon, {\n                                                        className: \"h-12 w-12 text-blue-600 group-hover:text-blue-700 transition-colors duration-300\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 170,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 169,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                    variant: \"h3\",\n                                                    className: \"text-xl sm:text-2xl font-bold text-gray-900 mb-4 tracking-tight bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent\",\n                                                    children: plan.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 172,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                    variant: \"paragraph\",\n                                                    className: \"text-base text-gray-600 text-center mb-8 flex-grow leading-relaxed opacity-90 group-hover:opacity-100 transition-opacity duration-300\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 178,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    size: \"md\",\n                                                    className: \"w-full sm:w-auto rounded-full shadow-md hover:shadow-lg bg-[#606AF5] focus:ring-4 focus:ring-blue-200 transition-all duration-300 flex items-center justify-center gap-2 px-6 py-3 text-white hover:bg-blue-800 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed\",\n                                                    onClick: ()=>router.push(plan.href),\n                                                    \"aria-label\": \"Start plan at \".concat(plan.price, \" MAD per month\"),\n                                                    disabled: !plan.href,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white group-hover:rotate-12 transition-transform duration-300\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 191,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: t(\"starting_at\")\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 195,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg\",\n                                                            children: plan.price\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 196,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm\",\n                                                            children: \"MAD/mo\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 197,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-white group-hover:translate-x-1 transition-transform duration-300\",\n                                                            \"aria-hidden\": \"true\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 198,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, plan.title, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 17\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                            lineNumber: 134,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16 sm:mt-24 lg:mt-32 mb-16 sm:mb-24 lg:mb-32\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center max-w-3xl mx-auto mb-12 sm:mb-16\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                            variant: \"h2\",\n                                            className: \"text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-900 mb-4 sm:mb-6\",\n                                            children: t(\"why_choose_us\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 210,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                            variant: \"paragraph\",\n                                            className: \"text-base sm:text-lg text-gray-600\",\n                                            children: t(\"experience_blend\")\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 216,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 209,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\",\n                                    children: [\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n                                            title: t(\"hosting_features.0.title\"),\n                                            description: t(\"hosting_features.0.description\")\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"],\n                                            title: t(\"hosting_features.1.title\"),\n                                            description: t(\"hosting_features.1.description\")\n                                        },\n                                        {\n                                            icon: _barrel_optimize_names_ArrowRightIcon_ArrowUpRight_ClockIcon_CloudIcon_GaugeIcon_GlobeIcon_HandCoins_HardDriveIcon_HeadphonesIcon_RocketIcon_ServerIcon_ShieldIcon_SparklesIcon_UsersIcon_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"],\n                                            title: t(\"hosting_features.2.title\"),\n                                            description: t(\"hosting_features.2.description\")\n                                        }\n                                    ].map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Card, {\n                                            className: \"p-6 sm:p-8 hover:shadow-lg transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex justify-center items-center gap-2 p-3 bg-blue-50 rounded-xl mb-4 sm:mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                            className: \"h-6 w-6 sm:h-8 sm:w-8 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 246,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                            variant: \"h4\",\n                                                            className: \"text-lg sm:text-xl font-bold text-gray-900\",\n                                                            children: feature.title\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                            lineNumber: 247,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 245,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                    variant: \"paragraph\",\n                                                    className: \"text-sm sm:text-base text-gray-600 leading-relaxed\",\n                                                    children: feature.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, feature.title, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 223,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                            lineNumber: 208,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mb-20 sm:mb-28 lg:mb-36\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                    variant: \"h2\",\n                                    className: \"text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 text-center mb-12 sm:mb-16\",\n                                    children: t(\"faq_heading\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4 max-w-4xl mx-auto\",\n                                    children: [\n                                        {\n                                            question: t(\"faq.0.question\"),\n                                            answer: t(\"faq.0.answer\")\n                                        },\n                                        {\n                                            question: t(\"faq.1.question\"),\n                                            answer: t(\"faq.1.answer\")\n                                        },\n                                        {\n                                            question: t(\"faq.2.question\"),\n                                            answer: t(\"faq.2.answer\")\n                                        },\n                                        {\n                                            question: t(\"faq.3.question\"),\n                                            answer: t(\"faq.3.answer\")\n                                        }\n                                    ].map((faq, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Accordion, {\n                                            open: open === index + 1,\n                                            className: \"border border-gray-200/50 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.AccordionHeader, {\n                                                    onClick: ()=>handleOpen(index + 1),\n                                                    className: \"p-6 text-left hover:bg-gray-50 rounded-xl transition-colors duration-200\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"h5\",\n                                                        className: \"text-lg sm:text-xl font-semibold text-gray-900\",\n                                                        children: faq.question\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 382,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 378,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.AccordionBody, {\n                                                    className: \"p-6 pt-0\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_1__.Typography, {\n                                                        variant: \"paragraph\",\n                                                        className: \"text-base text-gray-600 leading-relaxed\",\n                                                        children: faq.answer\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                        lineNumber: 390,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                                    lineNumber: 389,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, faq.question, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                    lineNumber: 67,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\(sharedPages)\\\\hosting\\\\page.jsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n_s(Hosting, \"/FdS8onEd06rbGvgZfUyOZPSZGU=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_5__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter\n    ];\n});\n_c = Hosting;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Hosting);\nvar _c;\n$RefreshReg$(_c, \"Hosting\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvW2xvY2FsZV0vKHNoYXJlZFBhZ2VzKS9ob3N0aW5nL3BhZ2UuanN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQWlCc0I7QUFDc0I7QUFTVjtBQUNSO0FBQ2tCO0FBQ0k7QUFDaUI7QUFFakUsU0FBUzBCOztJQUNQLE1BQU1DLElBQUliLDBEQUFlQSxDQUFDO0lBQzFCLE1BQU1jLFNBQVNMLDBEQUFTQTtJQUV4Qiw2QkFBNkI7SUFDN0IsTUFBTSxDQUFDTSxNQUFNQyxRQUFRLEdBQUdSLHFEQUFjLENBQUM7SUFDdkMsTUFBTVUsYUFBYSxDQUFDQyxRQUFVSCxRQUFRRCxTQUFTSSxRQUFRLElBQUlBO0lBRTNELHFCQUNFLDhEQUFDQztRQUFJQyxXQUFVO2tCQUNiLDRFQUFDRDtZQUFJQyxXQUFVO3NCQUNiLDRFQUFDRDtnQkFBSUMsV0FBVTswQkFzQmIsNEVBQUNEO29CQUFJQyxXQUFVOztzQ0FFYiw4REFBQ1gsZ0VBQU9BOzRCQUFDVyxXQUFVOzs4Q0FJakIsOERBQUNsQixnRUFBVUE7b0NBQ1RtQixTQUFRO29DQUNSRCxXQUFVOzt3Q0FFVFIsRUFBRTtzREFDSCw4REFBQ1U7NENBQUtGLFdBQVU7c0RBQ2JSLEVBQUU7Ozs7Ozs7Ozs7Ozs4Q0FHUCw4REFBQ1YsZ0VBQVVBO29DQUNUbUIsU0FBUTtvQ0FDUkQsV0FBVTs4Q0FFVFIsRUFBRTs7Ozs7OzhDQUdMLDhEQUFDTztvQ0FBSUMsV0FBVTs4Q0FDWjt3Q0FDQzs0Q0FDRUcsTUFBTVgsRUFBRTs0Q0FDUlksTUFBTS9CLDhPQUFTQTs0Q0FDZmdDLFNBQVM7NENBQ1RDLFdBQVc7d0NBQ2I7d0NBQ0E7NENBQ0VILE1BQU1YLEVBQUU7NENBQ1JZLE1BQU05Qiw4T0FBU0E7NENBQ2YrQixTQUFTOzRDQUNUQyxXQUFXO3dDQUNiO3dDQUNBOzRDQUNFSCxNQUFNWCxFQUFFOzRDQUNSWSxNQUFNN0IsOE9BQVNBOzRDQUNmOEIsU0FBUzs0Q0FDVEMsV0FBVzt3Q0FDYjt3Q0FDQTs0Q0FDRUgsTUFBTVgsRUFBRTs0Q0FDUlksTUFBTTVCLDhPQUFTQTs0Q0FDZjZCLFNBQVM7NENBQ1RDLFdBQVc7d0NBQ2I7cUNBQ0QsQ0FBQ0MsR0FBRyxDQUFDLENBQUNDLE1BQU1DLHNCQUNYLDhEQUFDNUIsMERBQUlBOzRDQUVIbUIsV0FBVyxvREFBaUUsT0FBYlEsS0FBS0gsT0FBTyxFQUFDOzs4REFFNUUsOERBQUNHLEtBQUtKLElBQUk7b0RBQ1JKLFdBQVcsV0FBMEIsT0FBZlEsS0FBS0YsU0FBUyxFQUFDOzs7Ozs7OERBRXZDLDhEQUFDeEIsZ0VBQVVBO29EQUNUbUIsU0FBUTtvREFDUkQsV0FBVTs4REFFVFEsS0FBS0wsSUFBSTs7Ozs7OzsyQ0FWUE07Ozs7Ozs7Ozs7Ozs7Ozs7c0NBaUJiLDhEQUFDVjs0QkFDQ1csSUFBRzs0QkFDSFYsV0FBVTtzQ0FFVDtnQ0FDQztvQ0FDRVcsTUFBTTtvQ0FDTlAsTUFBTXRDLCtPQUFTQTtvQ0FDZjhDLE9BQU9wQixFQUFFO29DQUNUcUIsYUFBYXJCLEVBQUU7b0NBQ2ZzQixPQUFPdEIsRUFBRTtnQ0FDWDtnQ0FDQTtvQ0FDRW1CLE1BQU07b0NBQ05QLE1BQU12QywrT0FBVUE7b0NBQ2hCK0MsT0FBT3BCLEVBQUU7b0NBQ1RxQixhQUFhckIsRUFBRTtvQ0FDZnNCLE9BQU90QixFQUFFO2dDQUNYO2dDQUNBO29DQUNFbUIsTUFBTTtvQ0FDTlAsTUFBTXJDLCtPQUFhQTtvQ0FDbkI2QyxPQUFPcEIsRUFBRTtvQ0FDVHFCLGFBQWFyQixFQUFFO29DQUNmc0IsT0FBT3RCLEVBQUU7Z0NBQ1g7NkJBQ0QsQ0FBQ2UsR0FBRyxDQUFDLENBQUNRLHFCQUNMLDhEQUFDbEMsMERBQUlBO29DQUVIbUIsV0FBVTs7c0RBR1YsOERBQUNEOzRDQUFJQyxXQUFVOzs7Ozs7c0RBRWYsOERBQUNEOzRDQUFJQyxXQUFVOzs4REFDYiw4REFBQ0Q7b0RBQUlDLFdBQVU7OERBQ2IsNEVBQUNlLEtBQUtYLElBQUk7d0RBQUNKLFdBQVU7Ozs7Ozs7Ozs7OzhEQUV2Qiw4REFBQ2xCLGdFQUFVQTtvREFDVG1CLFNBQVE7b0RBQ1JELFdBQVU7OERBRVRlLEtBQUtILEtBQUs7Ozs7Ozs4REFFYiw4REFBQzlCLGdFQUFVQTtvREFDVG1CLFNBQVE7b0RBQ1JELFdBQVU7OERBRVRlLEtBQUtGLFdBQVc7Ozs7Ozs4REFFbkIsOERBQUNHO29EQUNDQyxNQUFLO29EQUNMakIsV0FBVTtvREFDVmtCLFNBQVMsSUFBTXpCLE9BQU8wQixJQUFJLENBQUNKLEtBQUtKLElBQUk7b0RBQ3BDUyxjQUFZLGlCQUE0QixPQUFYTCxLQUFLRCxLQUFLLEVBQUM7b0RBQ3hDTyxVQUFVLENBQUNOLEtBQUtKLElBQUk7O3NFQUVwQiw4REFBQ3hDLCtPQUFVQTs0REFDVDZCLFdBQVU7NERBQ1ZzQixlQUFZOzs7Ozs7c0VBRWQsOERBQUNwQjs0REFBS0YsV0FBVTtzRUFBV1IsRUFBRTs7Ozs7O3NFQUM3Qiw4REFBQ1U7NERBQUtGLFdBQVU7c0VBQVdlLEtBQUtELEtBQUs7Ozs7OztzRUFDckMsOERBQUNaOzREQUFLRixXQUFVO3NFQUFVOzs7Ozs7c0VBQzFCLDhEQUFDdEIsK09BQVlBOzREQUNYc0IsV0FBVTs0REFDVnNCLGVBQVk7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7bUNBdENiUCxLQUFLSCxLQUFLOzs7Ozs7Ozs7O3NDQThDckIsOERBQUNiOzRCQUFJQyxXQUFVOzs4Q0FDYiw4REFBQ0Q7b0NBQUlDLFdBQVU7O3NEQUNiLDhEQUFDbEIsZ0VBQVVBOzRDQUNUbUIsU0FBUTs0Q0FDUkQsV0FBVTtzREFFVFIsRUFBRTs7Ozs7O3NEQUVMLDhEQUFDVixnRUFBVUE7NENBQ1RtQixTQUFROzRDQUNSRCxXQUFVO3NEQUVUUixFQUFFOzs7Ozs7Ozs7Ozs7OENBR1AsOERBQUNPO29DQUFJQyxXQUFVOzhDQUNaO3dDQUNDOzRDQUNFSSxNQUFNcEMsK09BQVVBOzRDQUNoQjRDLE9BQU9wQixFQUFFOzRDQUNUcUIsYUFBYXJCLEVBQUU7d0NBQ2pCO3dDQUNBOzRDQUNFWSxNQUFNbkMsK09BQVNBOzRDQUNmMkMsT0FBT3BCLEVBQUU7NENBQ1RxQixhQUFhckIsRUFBRTt3Q0FDakI7d0NBQ0E7NENBQ0VZLE1BQU1sQywrT0FBY0E7NENBQ3BCMEMsT0FBT3BCLEVBQUU7NENBQ1RxQixhQUFhckIsRUFBRTt3Q0FDakI7cUNBQ0QsQ0FBQ2UsR0FBRyxDQUFDLENBQUNnQix3QkFDTCw4REFBQzFDLDBEQUFJQTs0Q0FFSG1CLFdBQVU7OzhEQUVWLDhEQUFDRDtvREFBSUMsV0FBVTs7c0VBQ2IsOERBQUN1QixRQUFRbkIsSUFBSTs0REFBQ0osV0FBVTs7Ozs7O3NFQUN4Qiw4REFBQ2xCLGdFQUFVQTs0REFDVG1CLFNBQVE7NERBQ1JELFdBQVU7c0VBRVR1QixRQUFRWCxLQUFLOzs7Ozs7Ozs7Ozs7OERBSWxCLDhEQUFDOUIsZ0VBQVVBO29EQUNUbUIsU0FBUTtvREFDUkQsV0FBVTs4REFFVHVCLFFBQVFWLFdBQVc7Ozs7Ozs7MkNBakJqQlUsUUFBUVgsS0FBSzs7Ozs7Ozs7Ozs7Ozs7OztzQ0FxSDFCLDhEQUFDYjs0QkFBSUMsV0FBVTs7OENBQ2IsOERBQUNsQixnRUFBVUE7b0NBQ1RtQixTQUFRO29DQUNSRCxXQUFVOzhDQUVUUixFQUFFOzs7Ozs7OENBRUwsOERBQUNPO29DQUFJQyxXQUFVOzhDQUNaO3dDQUNDOzRDQUFFd0IsVUFBVWhDLEVBQUU7NENBQW1CaUMsUUFBUWpDLEVBQUU7d0NBQWdCO3dDQUMzRDs0Q0FBRWdDLFVBQVVoQyxFQUFFOzRDQUFtQmlDLFFBQVFqQyxFQUFFO3dDQUFnQjt3Q0FDM0Q7NENBQUVnQyxVQUFVaEMsRUFBRTs0Q0FBbUJpQyxRQUFRakMsRUFBRTt3Q0FBZ0I7d0NBQzNEOzRDQUFFZ0MsVUFBVWhDLEVBQUU7NENBQW1CaUMsUUFBUWpDLEVBQUU7d0NBQWdCO3FDQUM1RCxDQUFDZSxHQUFHLENBQUMsQ0FBQ21CLEtBQUtqQixzQkFDViw4REFBQzFCLCtEQUFTQTs0Q0FFUlcsTUFBTUEsU0FBU2UsUUFBUTs0Q0FDdkJULFdBQVU7OzhEQUVWLDhEQUFDaEIscUVBQWVBO29EQUNka0MsU0FBUyxJQUFNckIsV0FBV1ksUUFBUTtvREFDbENULFdBQVU7OERBRVYsNEVBQUNsQixnRUFBVUE7d0RBQ1RtQixTQUFRO3dEQUNSRCxXQUFVO2tFQUVUMEIsSUFBSUYsUUFBUTs7Ozs7Ozs7Ozs7OERBR2pCLDhEQUFDdkMsbUVBQWFBO29EQUFDZSxXQUFVOzhEQUN2Qiw0RUFBQ2xCLGdFQUFVQTt3REFDVG1CLFNBQVE7d0RBQ1JELFdBQVU7a0VBRVQwQixJQUFJRCxNQUFNOzs7Ozs7Ozs7Ozs7MkNBcEJWQyxJQUFJRixRQUFROzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBa0RyQztHQXRZU2pDOztRQUNHWixzREFBZUE7UUFDVlMsc0RBQVNBOzs7S0FGakJHO0FBd1lULCtEQUFlQSxPQUFPQSxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vX05fRS8uL3NyYy9hcHAvW2xvY2FsZV0vKHNoYXJlZFBhZ2VzKS9ob3N0aW5nL3BhZ2UuanN4P2MwNjciXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcblxyXG5pbXBvcnQge1xyXG4gIFNlcnZlckljb24sXHJcbiAgQ2xvdWRJY29uLFxyXG4gIEhhcmREcml2ZUljb24sXHJcbiAgU2hpZWxkSWNvbixcclxuICBHYXVnZUljb24sXHJcbiAgSGVhZHBob25lc0ljb24sXHJcbiAgUm9ja2V0SWNvbixcclxuICBTcGFya2xlc0ljb24sXHJcbiAgQ2xvY2tJY29uLFxyXG4gIFVzZXJzSWNvbixcclxuICBHbG9iZUljb24sXHJcbiAgSGFuZENvaW5zLFxyXG4gIEFycm93UmlnaHRJY29uLFxyXG4gIEFycm93VXBSaWdodCxcclxufSBmcm9tIFwibHVjaWRlLXJlYWN0XCI7XHJcbmltcG9ydCB7IHVzZVRyYW5zbGF0aW9ucyB9IGZyb20gXCJuZXh0LWludGxcIjtcclxuaW1wb3J0IHtcclxuICBCdXR0b24sXHJcbiAgQ2FyZCxcclxuICBUeXBvZ3JhcGh5LFxyXG4gIEFjY29yZGlvbixcclxuICBBY2NvcmRpb25IZWFkZXIsXHJcbiAgQWNjb3JkaW9uQm9keSxcclxuICBDYXJvdXNlbCxcclxufSBmcm9tIFwiQG1hdGVyaWFsLXRhaWx3aW5kL3JlYWN0XCI7XHJcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQgU2VjdGlvbiBmcm9tIFwiQC9jb21wb25lbnRzL2hvbWUvU2VjdGlvblwiO1xyXG5pbXBvcnQgeyBBcnJvd1R1cm5SaWdodFVwSWNvbiB9IGZyb20gXCJAaGVyb2ljb25zL3JlYWN0LzI0L3NvbGlkXCI7XHJcblxyXG5mdW5jdGlvbiBIb3N0aW5nKCkge1xyXG4gIGNvbnN0IHQgPSB1c2VUcmFuc2xhdGlvbnMoXCJob3N0aW5nXCIpO1xyXG4gIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpO1xyXG5cclxuICAvLyBBY2NvcmRpb24gc3RhdGUgbWFuYWdlbWVudFxyXG4gIGNvbnN0IFtvcGVuLCBzZXRPcGVuXSA9IFJlYWN0LnVzZVN0YXRlKDApO1xyXG4gIGNvbnN0IGhhbmRsZU9wZW4gPSAodmFsdWUpID0+IHNldE9wZW4ob3BlbiA9PT0gdmFsdWUgPyAwIDogdmFsdWUpO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW5cIj5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJcIj5cclxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIlwiPlxyXG4gICAgICAgICAgey8qIEFuaW1hdGVkIGJhY2tncm91bmQgZWxlbWVudHMgKi99XHJcbiAgICAgICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LWhpZGRlblwiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tYiBmcm9tLWJsdWUtNTAgdG8td2hpdGVcIj48L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtdG9wLTQwIC1yaWdodC00MCB3LTYwIHNtOnctODAgaC02MCBzbTpoLTgwIGJnLWJsdWUtMTAwIHJvdW5kZWQtZnVsbCBtaXgtYmxlbmQtbXVsdGlwbHkgZmlsdGVyIGJsdXIteGwgb3BhY2l0eS03MCBhbmltYXRlLWJsb2JcIj48L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSAtYm90dG9tLTQwIC1sZWZ0LTQwIHctNjAgc206dy04MCBoLTYwIHNtOmgtODAgYmctcHVycGxlLTEwMCByb3VuZGVkLWZ1bGwgbWl4LWJsZW5kLW11bHRpcGx5IGZpbHRlciBibHVyLXhsIG9wYWNpdHktNzAgYW5pbWF0ZS1ibG9iIGFuaW1hdGlvbi1kZWxheS0yMDAwXCI+PC9kaXY+XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTQwIGxlZnQtNDAgdy02MCBzbTp3LTgwIGgtNjAgc206aC04MCBiZy1pbmRpZ28tMTAwIHJvdW5kZWQtZnVsbCBtaXgtYmxlbmQtbXVsdGlwbHkgZmlsdGVyIGJsdXIteGwgb3BhY2l0eS03MCBhbmltYXRlLWJsb2IgYW5pbWF0aW9uLWRlbGF5LTQwMDBcIj48L2Rpdj5cclxuICAgICAgICAgIDwvZGl2PiAqL31cclxuXHJcbiAgICAgICAgICB7LyogRmxvYXRpbmcgZWxlbWVudHMgKi99XHJcbiAgICAgICAgICB7LyogPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG92ZXJmbG93LWhpZGRlbiBwb2ludGVyLWV2ZW50cy1ub25lIGhpZGRlbiBsZzpibG9ja1wiPlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIHRvcC0xLzQgbGVmdC0xLzQgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgLXRyYW5zbGF0ZS15LTEvMiBhbmltYXRlLWZsb2F0LXNsb3dcIj5cclxuICAgICAgICAgICAgICA8U2VydmVySWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHhsOmgtMTIgeGw6dy0xMiB0ZXh0LWJsdWUtNDAwIG9wYWNpdHktNTBcIiAvPlxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSB0b3AtMS8zIHJpZ2h0LTEvNCB0cmFuc2Zvcm0gdHJhbnNsYXRlLXgtMS8yIC10cmFuc2xhdGUteS0xLzIgYW5pbWF0ZS1mbG9hdC1zbG93IGFuaW1hdGlvbi1kZWxheS0yMDAwXCI+XHJcbiAgICAgICAgICAgICAgPENsb3VkSWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHhsOmgtMTAgeGw6dy0xMCB0ZXh0LWluZGlnby00MDAgb3BhY2l0eS01MFwiIC8+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGJvdHRvbS0xLzQgbGVmdC0xLzMgdHJhbnNmb3JtIC10cmFuc2xhdGUteC0xLzIgdHJhbnNsYXRlLXktMS8yIGFuaW1hdGlvbi1kZWxheS00MDAwXCI+XHJcbiAgICAgICAgICAgICAgPFNoaWVsZEljb24gY2xhc3NOYW1lPVwiaC02IHctNiB4bDpoLTggeGw6dy04IHRleHQtcHVycGxlLTQwMCBvcGFjaXR5LTUwXCIgLz5cclxuICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICA8L2Rpdj4gKi99XHJcblxyXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJyZWxhdGl2ZSBteC1hdXRvIG1heC13LTd4bCBweC00IHNtOnB4LTYgbGc6cHgtOFwiPlxyXG4gICAgICAgICAgICB7LyogSGVybyBTZWN0aW9uICovfVxyXG4gICAgICAgICAgICA8U2VjdGlvbiBjbGFzc05hbWU9XCJ0ZXh0LWNlbnRlciBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgey8qIDxkaXYgY2xhc3NOYW1lPVwiaW5saW5lLWJsb2NrIG1iLTQgc206bWItNiBhbmltYXRlLWJvdW5jZS1zbG93XCI+XHJcbiAgICAgICAgICAgICAgICA8Um9ja2V0SWNvbiBjbGFzc05hbWU9XCJoLTggdy04IHNtOmgtMTAgc206dy0xMCB4bDpoLTEyIHhsOnctMTIgdGV4dC1ibHVlLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgPC9kaXY+ICovfVxyXG4gICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDFcIlxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC0zeGwgc206dGV4dC00eGwgbGc6dGV4dC01eGwgeGw6dGV4dC02eGwgZm9udC1leHRyYWJvbGQgdGV4dC1ncmF5LTkwMCB0cmFja2luZy10aWdodCBtYi00IHNtOm1iLTYgYW5pbWF0ZS1mYWRlLWluIGxlYWRpbmctdGlnaHRcIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHt0KFwiaGVhZGxpbmVfc3RhcnRcIil9XHJcbiAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJtbC00IGJnLWdyYWRpZW50LXRvLXIgZnJvbS1ibHVlLTYwMCB2aWEtaW5kaWdvLTYwMCB0by1wdXJwbGUtNjAwIGJnLWNsaXAtdGV4dCB0ZXh0LXRyYW5zcGFyZW50IGFuaW1hdGUtZ3JhZGllbnQgYmxvY2sgc206aW5saW5lXCI+XHJcbiAgICAgICAgICAgICAgICAgIHt0KFwiaGVhZGxpbmVfaGlnaGxpZ2h0XCIpfVxyXG4gICAgICAgICAgICAgICAgPC9zcGFuPlxyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cInBhcmFncmFwaFwiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJhc2Ugc206dGV4dC1sZyB4bDp0ZXh0LXhsIHRleHQtZ3JheS02MDAgbWItNiBzbTptYi04IHhsOm1iLTEyIGxlYWRpbmctcmVsYXhlZCBhbmltYXRlLWZhZGUtaW4tdXAgbWF4LXctM3hsIG14LWF1dG9cIlxyXG4gICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgIHt0KFwic3VidGV4dFwiKX1cclxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcblxyXG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTIgc206bXQtMTYgZ3JpZCBncmlkLWNvbHMtMiBsZzpncmlkLWNvbHMtNCBnYXAtNCBzbTpnYXAtNiBtYXgtdy02eGwgbXgtYXV0byBhbmltYXRlLWZhZGUtaW4tdXAgYW5pbWF0aW9uLWRlbGF5LTEwMDBcIj5cclxuICAgICAgICAgICAgICAgIHtbXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiB0KFwidHJ1c3RfaW5kaWMuMFwiKSxcclxuICAgICAgICAgICAgICAgICAgICBpY29uOiBDbG9ja0ljb24sXHJcbiAgICAgICAgICAgICAgICAgICAgYmdDb2xvcjogXCJiZy1ibHVlLTUwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbkNvbG9yOiBcInRleHQtYmx1ZS02MDBcIixcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIHRleHQ6IHQoXCJ0cnVzdF9pbmRpYy4xXCIpLFxyXG4gICAgICAgICAgICAgICAgICAgIGljb246IFVzZXJzSWNvbixcclxuICAgICAgICAgICAgICAgICAgICBiZ0NvbG9yOiBcImJnLWdyZWVuLTUwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbkNvbG9yOiBcInRleHQtZ3JlZW4tNjAwXCIsXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiB0KFwidHJ1c3RfaW5kaWMuMlwiKSxcclxuICAgICAgICAgICAgICAgICAgICBpY29uOiBHbG9iZUljb24sXHJcbiAgICAgICAgICAgICAgICAgICAgYmdDb2xvcjogXCJiZy1pbmRpZ28tNTBcIixcclxuICAgICAgICAgICAgICAgICAgICBpY29uQ29sb3I6IFwidGV4dC1pbmRpZ28tNjAwXCIsXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICB0ZXh0OiB0KFwidHJ1c3RfaW5kaWMuM1wiKSxcclxuICAgICAgICAgICAgICAgICAgICBpY29uOiBIYW5kQ29pbnMsXHJcbiAgICAgICAgICAgICAgICAgICAgYmdDb2xvcjogXCJiZy10ZWFsLTUwXCIsXHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbkNvbG9yOiBcInRleHQtdGVhbC02MDBcIixcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIF0ubWFwKChpdGVtLCBpbmRleCkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZFxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17aW5kZXh9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZ3JvdXAgZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgZ2FwLTMgcC00ICR7aXRlbS5iZ0NvbG9yfSBiYWNrZHJvcC1ibHVyLW1kIGJvcmRlciBib3JkZXItZ3JheS0yMDAvNTAgcm91bmRlZC14bCBzaGFkb3ctc20gaG92ZXI6c2hhZG93LW1kIGhvdmVyOi10cmFuc2xhdGUteS0xIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMGB9XHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8aXRlbS5pY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BoLTYgdy02ICR7aXRlbS5pY29uQ29sb3J9IGZsZXgtc2hyaW5rLTAgZ3JvdXAtaG92ZXI6c2NhbGUtMTEwIHRyYW5zaXRpb24tdHJhbnNmb3JtIGR1cmF0aW9uLTIwMGB9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInNtYWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtc20gc206dGV4dC1iYXNlIHRleHQtZ3JheS03MDAgZm9udC1zZW1pYm9sZCBsZWFkaW5nLXRpZ2h0XCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7aXRlbS50ZXh0fVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvU2VjdGlvbj5cclxuICAgICAgICAgICAgey8qIEhvc3RpbmcgUGxhbnMgKi99XHJcbiAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICBpZD1cInBsYW5zXCJcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJncmlkIGdyaWQtY29scy0xIG1kOmdyaWQtY29scy0zIGdhcC02IGxnOmdhcC04IG10LTEwIHNtOm10LTE4IGxnOm10LTI2IHB4LTQgc206cHgtMCBtYXgtdy03eGwgbXgtYXV0b1wiXHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICB7W1xyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBocmVmOiBcIi9ob3N0aW5nL3NoYXJlZFwiLFxyXG4gICAgICAgICAgICAgICAgICBpY29uOiBDbG91ZEljb24sXHJcbiAgICAgICAgICAgICAgICAgIHRpdGxlOiB0KFwiaG9zdGluZ190eXBlcy4wLnRpdGxlXCIpLFxyXG4gICAgICAgICAgICAgICAgICBkZXNjcmlwdGlvbjogdChcImhvc3RpbmdfdHlwZXMuMC5kZXNjcmlwdGlvblwiKSxcclxuICAgICAgICAgICAgICAgICAgcHJpY2U6IHQoXCJob3N0aW5nX3R5cGVzLjAucHJpY2VcIiksXHJcbiAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICBocmVmOiBcIi9ob3N0aW5nL3Zwc1wiLFxyXG4gICAgICAgICAgICAgICAgICBpY29uOiBTZXJ2ZXJJY29uLFxyXG4gICAgICAgICAgICAgICAgICB0aXRsZTogdChcImhvc3RpbmdfdHlwZXMuMS50aXRsZVwiKSxcclxuICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHQoXCJob3N0aW5nX3R5cGVzLjEuZGVzY3JpcHRpb25cIiksXHJcbiAgICAgICAgICAgICAgICAgIHByaWNlOiB0KFwiaG9zdGluZ190eXBlcy4xLnByaWNlXCIpLFxyXG4gICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgaHJlZjogXCIvaG9zdGluZy9kZWRpY2F0ZWRcIixcclxuICAgICAgICAgICAgICAgICAgaWNvbjogSGFyZERyaXZlSWNvbixcclxuICAgICAgICAgICAgICAgICAgdGl0bGU6IHQoXCJob3N0aW5nX3R5cGVzLjIudGl0bGVcIiksXHJcbiAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB0KFwiaG9zdGluZ190eXBlcy4yLmRlc2NyaXB0aW9uXCIpLFxyXG4gICAgICAgICAgICAgICAgICBwcmljZTogdChcImhvc3RpbmdfdHlwZXMuMi5wcmljZVwiKSxcclxuICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgXS5tYXAoKHBsYW4pID0+IChcclxuICAgICAgICAgICAgICAgIDxDYXJkXHJcbiAgICAgICAgICAgICAgICAgIGtleT17cGxhbi50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZ3JvdXAgcmVsYXRpdmUgaC1mdWxsIGJnLXdoaXRlIGJvcmRlciBib3JkZXItZ3JheS0yMDAvNTAgcm91bmRlZC0yeGwgc2hhZG93LW1kIGhvdmVyOnNoYWRvdy14bCBob3ZlcjotdHJhbnNsYXRlLXktMiB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDAgb3ZlcmZsb3ctaGlkZGVuXCJcclxuICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgey8qIFN1YnRsZSBHcmFkaWVudCBPdmVybGF5IG9uIEhvdmVyICovfVxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tdCBmcm9tLWJsdWUtNTAvMCB2aWEtYmx1ZS01MC8wIHRvLWJsdWUtNTAvMjAgb3BhY2l0eS0wIGdyb3VwLWhvdmVyOm9wYWNpdHktMTAwIHRyYW5zaXRpb24tb3BhY2l0eSBkdXJhdGlvbi0zMDAgcG9pbnRlci1ldmVudHMtbm9uZVwiPjwvZGl2PlxyXG5cclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTYgc206cC04IGZsZXggZmxleC1jb2wgaXRlbXMtY2VudGVyIHJlbGF0aXZlIHotMTBcIj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInAtNCBiZy1ibHVlLTUwIHJvdW5kZWQtZnVsbCBtYi02IGdyb3VwLWhvdmVyOmJnLWJsdWUtMTAwIGdyb3VwLWhvdmVyOnNjYWxlLTEwNSBncm91cC1ob3ZlcjpzaGFkb3ctc20gdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8cGxhbi5pY29uIGNsYXNzTmFtZT1cImgtMTIgdy0xMiB0ZXh0LWJsdWUtNjAwIGdyb3VwLWhvdmVyOnRleHQtYmx1ZS03MDAgdHJhbnNpdGlvbi1jb2xvcnMgZHVyYXRpb24tMzAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImgzXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQteGwgc206dGV4dC0yeGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNCB0cmFja2luZy10aWdodCBiZy1ncmFkaWVudC10by1yIGZyb20tZ3JheS05MDAgdG8tZ3JheS03MDAgYmctY2xpcC10ZXh0IHRleHQtdHJhbnNwYXJlbnRcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtwbGFuLnRpdGxlfVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInBhcmFncmFwaFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCB0ZXh0LWNlbnRlciBtYi04IGZsZXgtZ3JvdyBsZWFkaW5nLXJlbGF4ZWQgb3BhY2l0eS05MCBncm91cC1ob3ZlcjpvcGFjaXR5LTEwMCB0cmFuc2l0aW9uLW9wYWNpdHkgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICB7cGxhbi5kZXNjcmlwdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPGJ1dHRvblxyXG4gICAgICAgICAgICAgICAgICAgICAgc2l6ZT1cIm1kXCJcclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctZnVsbCBzbTp3LWF1dG8gcm91bmRlZC1mdWxsIHNoYWRvdy1tZCBob3ZlcjpzaGFkb3ctbGcgYmctWyM2MDZBRjVdIGZvY3VzOnJpbmctNCBmb2N1czpyaW5nLWJsdWUtMjAwIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBnYXAtMiBweC02IHB5LTMgdGV4dC13aGl0ZSBob3ZlcjpiZy1ibHVlLTgwMCBmb2N1czpvdXRsaW5lLW5vbmUgZGlzYWJsZWQ6b3BhY2l0eS01MCBkaXNhYmxlZDpjdXJzb3Itbm90LWFsbG93ZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17KCkgPT4gcm91dGVyLnB1c2gocGxhbi5ocmVmKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGFyaWEtbGFiZWw9e2BTdGFydCBwbGFuIGF0ICR7cGxhbi5wcmljZX0gTUFEIHBlciBtb250aGB9XHJcbiAgICAgICAgICAgICAgICAgICAgICBkaXNhYmxlZD17IXBsYW4uaHJlZn0gLy8gT3B0aW9uYWw6IGRpc2FibGUgaWYgbm8gdmFsaWQgaHJlZlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIDxSb2NrZXRJY29uXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImgtNSB3LTUgdGV4dC13aGl0ZSBncm91cC1ob3Zlcjpyb3RhdGUtMTIgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1oaWRkZW49XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LXNtXCI+e3QoXCJzdGFydGluZ19hdFwiKX08L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJ0ZXh0LWxnXCI+e3BsYW4ucHJpY2V9PC9zcGFuPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1zbVwiPk1BRC9tbzwvc3Bhbj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxBcnJvd1VwUmlnaHRcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiaC01IHctNSB0ZXh0LXdoaXRlIGdyb3VwLWhvdmVyOnRyYW5zbGF0ZS14LTEgdHJhbnNpdGlvbi10cmFuc2Zvcm0gZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgYXJpYS1oaWRkZW49XCJ0cnVlXCJcclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9idXR0b24+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICkpfVxyXG4gICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgey8qIEZlYXR1cmVzIFNlY3Rpb24gKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibXQtMTYgc206bXQtMjQgbGc6bXQtMzIgbWItMTYgc206bWItMjQgbGc6bWItMzJcIj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInRleHQtY2VudGVyIG1heC13LTN4bCBteC1hdXRvIG1iLTEyIHNtOm1iLTE2XCI+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDJcIlxyXG4gICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LTJ4bCBzbTp0ZXh0LTN4bCBsZzp0ZXh0LTR4bCBmb250LWJvbGQgdGV4dC1ncmF5LTkwMCBtYi00IHNtOm1iLTZcIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICB7dChcIndoeV9jaG9vc2VfdXNcIil9XHJcbiAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICB2YXJpYW50PVwicGFyYWdyYXBoXCJcclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1iYXNlIHNtOnRleHQtbGcgdGV4dC1ncmF5LTYwMFwiXHJcbiAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgIHt0KFwiZXhwZXJpZW5jZV9ibGVuZFwiKX1cclxuICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgc206Z3JpZC1jb2xzLTIgbGc6Z3JpZC1jb2xzLTMgZ2FwLTQgc206Z2FwLTYgbGc6Z2FwLThcIj5cclxuICAgICAgICAgICAgICAgIHtbXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICBpY29uOiBTaGllbGRJY29uLFxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiB0KFwiaG9zdGluZ19mZWF0dXJlcy4wLnRpdGxlXCIpLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB0KFwiaG9zdGluZ19mZWF0dXJlcy4wLmRlc2NyaXB0aW9uXCIpLFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbjogR2F1Z2VJY29uLFxyXG4gICAgICAgICAgICAgICAgICAgIHRpdGxlOiB0KFwiaG9zdGluZ19mZWF0dXJlcy4xLnRpdGxlXCIpLFxyXG4gICAgICAgICAgICAgICAgICAgIGRlc2NyaXB0aW9uOiB0KFwiaG9zdGluZ19mZWF0dXJlcy4xLmRlc2NyaXB0aW9uXCIpLFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgaWNvbjogSGVhZHBob25lc0ljb24sXHJcbiAgICAgICAgICAgICAgICAgICAgdGl0bGU6IHQoXCJob3N0aW5nX2ZlYXR1cmVzLjIudGl0bGVcIiksXHJcbiAgICAgICAgICAgICAgICAgICAgZGVzY3JpcHRpb246IHQoXCJob3N0aW5nX2ZlYXR1cmVzLjIuZGVzY3JpcHRpb25cIiksXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICBdLm1hcCgoZmVhdHVyZSkgPT4gKFxyXG4gICAgICAgICAgICAgICAgICA8Q2FyZFxyXG4gICAgICAgICAgICAgICAgICAgIGtleT17ZmVhdHVyZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJwLTYgc206cC04IGhvdmVyOnNoYWRvdy1sZyB0cmFuc2l0aW9uLWFsbCBkdXJhdGlvbi0zMDBcIlxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktY2VudGVyIGl0ZW1zLWNlbnRlciBnYXAtMiBwLTMgYmctYmx1ZS01MCByb3VuZGVkLXhsIG1iLTQgc206bWItNlwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPGZlYXR1cmUuaWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHNtOmgtOCBzbTp3LTggdGV4dC1ibHVlLTYwMFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwiaDRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWxnIHNtOnRleHQteGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmVhdHVyZS50aXRsZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuXHJcbiAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJwYXJhZ3JhcGhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCBsZWFkaW5nLXJlbGF4ZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgIHtmZWF0dXJlLmRlc2NyaXB0aW9ufVxyXG4gICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgPC9DYXJkPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG5cclxuICAgICAgICAgICAgey8qIFRlc3RpbW9uaWFscyAqL31cclxuICAgICAgICAgICAgey8qIDxkaXYgY2xhc3NOYW1lPVwibWItMTYgc206bWItMjQgbGc6bWItMzIgbWF4LXctN3hsIG14LWF1dG8gcHgtNCBzbTpweC02IGxnOnB4LThcIj5cclxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImgyXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtMnhsIHNtOnRleHQtM3hsIGxnOnRleHQtNHhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIHRleHQtY2VudGVyIG1iLTEyIHNtOm1iLTE2XCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7dChcImN1c3RvbWVyX3Jldmlld3NfaGVhZGluZ1wiKX1cclxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgPENhcm91c2VsXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvdmVyZmxvdy1oaWRkZW5cIlxyXG4gICAgICAgICAgICAgICAgYXV0b3BsYXlcclxuICAgICAgICAgICAgICAgIGxvb3BcclxuICAgICAgICAgICAgICAgIG5hdmlnYXRpb249eyh7IHNldEFjdGl2ZUluZGV4LCBhY3RpdmVJbmRleCwgbGVuZ3RoIH0pID0+IChcclxuICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBib3R0b20tNCBsZWZ0LTEvMiB0cmFuc2Zvcm0gLXRyYW5zbGF0ZS14LTEvMiBmbGV4IGdhcC0yIHotMTBcIj5cclxuICAgICAgICAgICAgICAgICAgICB7bmV3IEFycmF5KGxlbmd0aCkuZmlsbChcIlwiKS5tYXAoKF8sIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgIDxzcGFuXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGtleT17aX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgYmxvY2sgaC0yIHctMiByb3VuZGVkLWZ1bGwgY3Vyc29yLXBvaW50ZXIgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwICR7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgYWN0aXZlSW5kZXggPT09IGkgPyBcImJnLWJsdWUtNjAwIHctNFwiIDogXCJiZy1ncmF5LTMwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1gfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoKSA9PiBzZXRBY3RpdmVJbmRleChpKX1cclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgIHN3aXBlclBhcmFtcz17e1xyXG4gICAgICAgICAgICAgICAgICBzbGlkZXNQZXJWaWV3OiAxLCAvLyBEZWZhdWx0IGZvciBtb2JpbGVcclxuICAgICAgICAgICAgICAgICAgc3BhY2VCZXR3ZWVuOiAxNixcclxuICAgICAgICAgICAgICAgICAgYnJlYWtwb2ludHM6IHtcclxuICAgICAgICAgICAgICAgICAgICAxMDI0OiB7XHJcbiAgICAgICAgICAgICAgICAgICAgICBzbGlkZXNQZXJWaWV3OiAzLFxyXG4gICAgICAgICAgICAgICAgICAgICAgc3BhY2VCZXR3ZWVuOiAzMixcclxuICAgICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgICB9LFxyXG4gICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7W1xyXG4gICAgICAgICAgICAgICAgICB7XHJcbiAgICAgICAgICAgICAgICAgICAgcXVvdGU6IHQoXCJ0ZXN0aW1vbmlhbHMuMC5xdW90ZVwiKSxcclxuICAgICAgICAgICAgICAgICAgICBhdXRob3I6IHQoXCJ0ZXN0aW1vbmlhbHMuMC5hdXRob3JcIiksXHJcbiAgICAgICAgICAgICAgICAgICAgcm9sZTogdChcInRlc3RpbW9uaWFscy4wLnJvbGVcIiksXHJcbiAgICAgICAgICAgICAgICAgIH0sXHJcbiAgICAgICAgICAgICAgICAgIHtcclxuICAgICAgICAgICAgICAgICAgICBxdW90ZTogdChcInRlc3RpbW9uaWFscy4xLnF1b3RlXCIpLFxyXG4gICAgICAgICAgICAgICAgICAgIGF1dGhvcjogdChcInRlc3RpbW9uaWFscy4xLmF1dGhvclwiKSxcclxuICAgICAgICAgICAgICAgICAgICByb2xlOiB0KFwidGVzdGltb25pYWxzLjEucm9sZVwiKSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgICAge1xyXG4gICAgICAgICAgICAgICAgICAgIHF1b3RlOiB0KFwidGVzdGltb25pYWxzLjAucXVvdGVcIiksIC8vIER1cGxpY2F0ZSBmb3IgZGVtb1xyXG4gICAgICAgICAgICAgICAgICAgIGF1dGhvcjogdChcInRlc3RpbW9uaWFscy4wLmF1dGhvclwiKSxcclxuICAgICAgICAgICAgICAgICAgICByb2xlOiB0KFwidGVzdGltb25pYWxzLjAucm9sZVwiKSxcclxuICAgICAgICAgICAgICAgICAgfSxcclxuICAgICAgICAgICAgICAgIF0ubWFwKCh0ZXN0aW1vbmlhbCwgaW5kZXgpID0+IChcclxuICAgICAgICAgICAgICAgICAgPENhcmRcclxuICAgICAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNiBzbTpwLTggYmctd2hpdGUgYm9yZGVyIGJvcmRlci1ncmF5LTEwMCByb3VuZGVkLTJ4bCBzaGFkb3ctc20gaG92ZXI6c2hhZG93LWxnIHRyYW5zaXRpb24tYWxsIGR1cmF0aW9uLTMwMCBoLWZ1bGwgZmxleCBmbGV4LWNvbCBqdXN0aWZ5LWJldHdlZW4gbXgtMlwiXHJcbiAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFF1b3RlSWNvbiBjbGFzc05hbWU9XCJoLTYgdy02IHNtOmgtOCBzbTp3LTggdGV4dC1ibHVlLTYwMCBtYi00IHNtOm1iLTYgb3BhY2l0eS01MFwiIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YXJpYW50PVwicGFyYWdyYXBoXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1zbSBzbTp0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCBtYi02IHNtOm1iLTggaXRhbGljIGxlYWRpbmctcmVsYXhlZFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHt0ZXN0aW1vbmlhbC5xdW90ZX1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJoLTEwIHctMTAgc206aC0xMiBzbTp3LTEyIHJvdW5kZWQtZnVsbCBiZy1ibHVlLTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPFN0YXJJY29uIGNsYXNzTmFtZT1cImgtNSB3LTUgc206aC02IHNtOnctNiB0ZXh0LWJsdWUtNjAwXCIgLz5cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWwtMyBzbTptbC00XCI+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cInNtYWxsXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LXNtIHNtOnRleHQtYmFzZSB0ZXh0LWdyYXktOTAwIGZvbnQtc2VtaWJvbGRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Rlc3RpbW9uaWFsLmF1dGhvcn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgPC9UeXBvZ3JhcGh5PlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJzbWFsbFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC14cyBzbTp0ZXh0LXNtIHRleHQtZ3JheS01MDBcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3Rlc3RpbW9uaWFsLnJvbGV9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgICA8L0NhcmQ+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgICA8L0Nhcm91c2VsPlxyXG4gICAgICAgICAgICA8L2Rpdj4gKi99XHJcblxyXG4gICAgICAgICAgICB7LyogRkFRIFNlY3Rpb24gd2l0aCBBY2NvcmRpb24gKi99XHJcbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibWItMjAgc206bWItMjggbGc6bWItMzZcIj5cclxuICAgICAgICAgICAgICA8VHlwb2dyYXBoeVxyXG4gICAgICAgICAgICAgICAgdmFyaWFudD1cImgyXCJcclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInRleHQtM3hsIHNtOnRleHQtNHhsIGxnOnRleHQtNXhsIGZvbnQtYm9sZCB0ZXh0LWdyYXktOTAwIHRleHQtY2VudGVyIG1iLTEyIHNtOm1iLTE2XCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7dChcImZhcV9oZWFkaW5nXCIpfVxyXG4gICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNwYWNlLXktNCBtYXgtdy00eGwgbXgtYXV0b1wiPlxyXG4gICAgICAgICAgICAgICAge1tcclxuICAgICAgICAgICAgICAgICAgeyBxdWVzdGlvbjogdChcImZhcS4wLnF1ZXN0aW9uXCIpLCBhbnN3ZXI6IHQoXCJmYXEuMC5hbnN3ZXJcIikgfSxcclxuICAgICAgICAgICAgICAgICAgeyBxdWVzdGlvbjogdChcImZhcS4xLnF1ZXN0aW9uXCIpLCBhbnN3ZXI6IHQoXCJmYXEuMS5hbnN3ZXJcIikgfSxcclxuICAgICAgICAgICAgICAgICAgeyBxdWVzdGlvbjogdChcImZhcS4yLnF1ZXN0aW9uXCIpLCBhbnN3ZXI6IHQoXCJmYXEuMi5hbnN3ZXJcIikgfSxcclxuICAgICAgICAgICAgICAgICAgeyBxdWVzdGlvbjogdChcImZhcS4zLnF1ZXN0aW9uXCIpLCBhbnN3ZXI6IHQoXCJmYXEuMy5hbnN3ZXJcIikgfSxcclxuICAgICAgICAgICAgICAgIF0ubWFwKChmYXEsIGluZGV4KSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25cclxuICAgICAgICAgICAgICAgICAgICBrZXk9e2ZhcS5xdWVzdGlvbn1cclxuICAgICAgICAgICAgICAgICAgICBvcGVuPXtvcGVuID09PSBpbmRleCArIDF9XHJcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYm9yZGVyIGJvcmRlci1ncmF5LTIwMC81MCBiZy13aGl0ZSByb3VuZGVkLXhsIHNoYWRvdy1zbSBob3ZlcjpzaGFkb3ctbWQgdHJhbnNpdGlvbi1hbGwgZHVyYXRpb24tMzAwXCJcclxuICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgIDxBY2NvcmRpb25IZWFkZXJcclxuICAgICAgICAgICAgICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZU9wZW4oaW5kZXggKyAxKX1cclxuICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInAtNiB0ZXh0LWxlZnQgaG92ZXI6YmctZ3JheS01MCByb3VuZGVkLXhsIHRyYW5zaXRpb24tY29sb3JzIGR1cmF0aW9uLTIwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgPFR5cG9ncmFwaHlcclxuICAgICAgICAgICAgICAgICAgICAgICAgdmFyaWFudD1cImg1XCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidGV4dC1sZyBzbTp0ZXh0LXhsIGZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTkwMFwiXHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtmYXEucXVlc3Rpb259XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9BY2NvcmRpb25IZWFkZXI+XHJcbiAgICAgICAgICAgICAgICAgICAgPEFjY29yZGlvbkJvZHkgY2xhc3NOYW1lPVwicC02IHB0LTBcIj5cclxuICAgICAgICAgICAgICAgICAgICAgIDxUeXBvZ3JhcGh5XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhcmlhbnQ9XCJwYXJhZ3JhcGhcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJ0ZXh0LWJhc2UgdGV4dC1ncmF5LTYwMCBsZWFkaW5nLXJlbGF4ZWRcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICB7ZmFxLmFuc3dlcn1cclxuICAgICAgICAgICAgICAgICAgICAgIDwvVHlwb2dyYXBoeT5cclxuICAgICAgICAgICAgICAgICAgICA8L0FjY29yZGlvbkJvZHk+XHJcbiAgICAgICAgICAgICAgICAgIDwvQWNjb3JkaW9uPlxyXG4gICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICB7LyogQ1RBIFNlY3Rpb25cclxuICAgICAgICAgICAgPENhcmQgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXIgYmctZ3JhZGllbnQtdG8tciBmcm9tLWJsdWUtNTAgdG8taW5kaWdvLTUwIHAtNiBzbTpwLTggbGc6cC0xMiBtYi04IHNtOm1iLTEyIGxnOm1iLTE2XCI+XHJcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cImgyXCIgY2xhc3NOYW1lPVwidGV4dC0yeGwgc206dGV4dC0zeGwgbGc6dGV4dC00eGwgZm9udC1ib2xkIHRleHQtZ3JheS05MDAgbWItNCBzbTptYi02XCI+XHJcbiAgICAgICAgICAgICAgICB7dCgnY3RhLmhlYWRpbmcnKX1cclxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgPFR5cG9ncmFwaHkgdmFyaWFudD1cInBhcmFncmFwaFwiIGNsYXNzTmFtZT1cInRleHQtYmFzZSBzbTp0ZXh0LWxnIHRleHQtZ3JheS02MDAgbWItNiBzbTptYi04IG1heC13LTJ4bCBteC1hdXRvXCI+XHJcbiAgICAgICAgICAgICAgICB7dCgnY3RhLnN1YnRleHQnKX1cclxuICAgICAgICAgICAgICA8L1R5cG9ncmFwaHk+XHJcbiAgICAgICAgICAgICAgPEJ1dHRvblxyXG4gICAgICAgICAgICAgICAgY29sb3I9XCJibHVlXCJcclxuICAgICAgICAgICAgICAgIHNpemU9XCJsZ1wiXHJcbiAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJpbmxpbmUtYmxvY2tcIlxyXG4gICAgICAgICAgICAgICAgYXM9e0xpbmt9XHJcbiAgICAgICAgICAgICAgICBocmVmPVwiL2hvc3RpbmcvdnBzXCJcclxuICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICB7dCgnY3RhLmJ1dHRvbl90ZXh0Jyl9XHJcbiAgICAgICAgICAgICAgPC9CdXR0b24+XHJcbiAgICAgICAgICAgIDwvQ2FyZD4gKi99XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcbiAgICA8L2Rpdj5cclxuICApO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBIb3N0aW5nO1xyXG4iXSwibmFtZXMiOlsiU2VydmVySWNvbiIsIkNsb3VkSWNvbiIsIkhhcmREcml2ZUljb24iLCJTaGllbGRJY29uIiwiR2F1Z2VJY29uIiwiSGVhZHBob25lc0ljb24iLCJSb2NrZXRJY29uIiwiU3BhcmtsZXNJY29uIiwiQ2xvY2tJY29uIiwiVXNlcnNJY29uIiwiR2xvYmVJY29uIiwiSGFuZENvaW5zIiwiQXJyb3dSaWdodEljb24iLCJBcnJvd1VwUmlnaHQiLCJ1c2VUcmFuc2xhdGlvbnMiLCJCdXR0b24iLCJDYXJkIiwiVHlwb2dyYXBoeSIsIkFjY29yZGlvbiIsIkFjY29yZGlvbkhlYWRlciIsIkFjY29yZGlvbkJvZHkiLCJDYXJvdXNlbCIsIlJlYWN0IiwidXNlUm91dGVyIiwiU2VjdGlvbiIsIkFycm93VHVyblJpZ2h0VXBJY29uIiwiSG9zdGluZyIsInQiLCJyb3V0ZXIiLCJvcGVuIiwic2V0T3BlbiIsInVzZVN0YXRlIiwiaGFuZGxlT3BlbiIsInZhbHVlIiwiZGl2IiwiY2xhc3NOYW1lIiwidmFyaWFudCIsInNwYW4iLCJ0ZXh0IiwiaWNvbiIsImJnQ29sb3IiLCJpY29uQ29sb3IiLCJtYXAiLCJpdGVtIiwiaW5kZXgiLCJpZCIsImhyZWYiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIiwicHJpY2UiLCJwbGFuIiwiYnV0dG9uIiwic2l6ZSIsIm9uQ2xpY2siLCJwdXNoIiwiYXJpYS1sYWJlbCIsImRpc2FibGVkIiwiYXJpYS1oaWRkZW4iLCJmZWF0dXJlIiwicXVlc3Rpb24iLCJhbnN3ZXIiLCJmYXEiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/(sharedPages)/hosting/page.jsx\n"));

/***/ })

});