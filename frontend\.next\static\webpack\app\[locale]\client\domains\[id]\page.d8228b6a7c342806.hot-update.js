"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/[id]/page",{

/***/ "(app-pages-browser)/./src/components/domains/NameserverManager.jsx":
/*!******************************************************!*\
  !*** ./src/components/domains/NameserverManager.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ NameserverManager; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Plus,Save,Server,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var react_toastify__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-toastify */ \"(app-pages-browser)/./node_modules/react-toastify/dist/react-toastify.esm.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction NameserverManager(param) {\n    let { domain, onUpdate } = param;\n    _s();\n    const [nameservers, setNameservers] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)((domain === null || domain === void 0 ? void 0 : domain.nameservers) || [\n        \"\",\n        \"\"\n    ]);\n    const [isUpdating, setIsUpdating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const validateNameserver = (ns)=>{\n        if (!ns.trim()) return \"Nameserver cannot be empty\";\n        // Basic domain validation\n        const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;\n        if (!domainRegex.test(ns.trim())) {\n            return \"Invalid nameserver format\";\n        }\n        return null;\n    };\n    const validateAllNameservers = ()=>{\n        const newErrors = {};\n        const validNameservers = nameservers.filter((ns)=>ns.trim());\n        if (validNameservers.length < 2) {\n            newErrors.general = \"At least 2 nameservers are required\";\n        }\n        if (validNameservers.length > 13) {\n            newErrors.general = \"Maximum 13 nameservers allowed\";\n        }\n        nameservers.forEach((ns, index)=>{\n            if (ns.trim()) {\n                const error = validateNameserver(ns);\n                if (error) {\n                    newErrors[index] = error;\n                }\n            }\n        });\n        // Check for duplicates\n        const trimmedNs = validNameservers.map((ns)=>ns.trim().toLowerCase());\n        const duplicates = trimmedNs.filter((ns, index)=>trimmedNs.indexOf(ns) !== index);\n        if (duplicates.length > 0) {\n            newErrors.general = \"Duplicate nameservers are not allowed\";\n        }\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleNameserverChange = (index, value)=>{\n        const newNameservers = [\n            ...nameservers\n        ];\n        newNameservers[index] = value;\n        setNameservers(newNameservers);\n        // Clear error for this field\n        if (errors[index]) {\n            const newErrors = {\n                ...errors\n            };\n            delete newErrors[index];\n            setErrors(newErrors);\n        }\n    };\n    const addNameserver = ()=>{\n        if (nameservers.length < 13) {\n            setNameservers([\n                ...nameservers,\n                \"\"\n            ]);\n        }\n    };\n    const removeNameserver = (index)=>{\n        if (nameservers.length > 2) {\n            const newNameservers = nameservers.filter((_, i)=>i !== index);\n            setNameservers(newNameservers);\n            // Clear error for this field\n            if (errors[index]) {\n                const newErrors = {\n                    ...errors\n                };\n                delete newErrors[index];\n                setErrors(newErrors);\n            }\n        }\n    };\n    const handleSave = async ()=>{\n        if (!validateAllNameservers()) {\n            return;\n        }\n        try {\n            setIsUpdating(true);\n            const validNameservers = nameservers.filter((ns)=>ns.trim()).map((ns)=>ns.trim());\n            console.log(\"\\uD83D\\uDD27 Domain data:\", domain);\n            console.log(\"\\uD83D\\uDD27 Domain name:\", domain.name);\n            console.log(\"\\uD83D\\uDD27 Domain name type:\", typeof domain.name);\n            console.log(\"\\uD83D\\uDD27 Getting domain order ID for:\", domain.name);\n            if (!domain.name) {\n                throw new Error(\"Domain name is missing from domain data\");\n            }\n            // First, get the real domain registration order ID from the API\n            const orderIdResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainOrderId(domain.name);\n            if (!orderIdResponse.data.success) {\n                throw new Error(\"Failed to get domain order ID\");\n            }\n            const realOrderId = orderIdResponse.data.orderId;\n            console.log(\"✅ Got real domain order ID:\", realOrderId);\n            // Check domain status before attempting to modify nameservers\n            console.log(\"\\uD83D\\uDD0D Checking domain status before modification...\");\n            try {\n                const domainDetailsResponse = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getDomainDetailsByName(domain.name, \"All\");\n                console.log(\"\\uD83D\\uDCCB Domain details:\", domainDetailsResponse.data);\n                if (domainDetailsResponse.data.success && domainDetailsResponse.data.domain) {\n                    const domainInfo = domainDetailsResponse.data.domain;\n                    console.log(\"\\uD83D\\uDCCA Current status:\", domainInfo.currentstatus);\n                    console.log(\"\\uD83D\\uDCCA Order status:\", domainInfo.orderstatus);\n                    console.log(\"\\uD83D\\uDCCA Domain status:\", domainInfo.domainstatus);\n                    console.log(\"\\uD83D\\uDCCA Order ID:\", domainInfo.orderid);\n                    // Check if domain is in a state that allows nameserver modification\n                    if (domainInfo.currentstatus && domainInfo.currentstatus !== \"Active\") {\n                        throw new Error(\"Domain is not active. Current status: \".concat(domainInfo.currentstatus, \". Please wait for the domain to become active before modifying nameservers.\"));\n                    }\n                    // Check if there are any order status locks that might prevent modification\n                    if (domainInfo.orderstatus && Array.isArray(domainInfo.orderstatus) && domainInfo.orderstatus.length > 0) {\n                        console.log(\"⚠️ Domain has order status locks:\", domainInfo.orderstatus);\n                        react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Domain has status locks: \".concat(domainInfo.orderstatus.join(\", \"), \". Nameserver modification may fail.\"));\n                    }\n                }\n            } catch (statusError) {\n                console.warn(\"⚠️ Could not check domain status:\", statusError.message);\n            // Continue with nameserver modification even if status check fails\n            }\n            const updateData = {\n                orderId: realOrderId,\n                nameservers: validNameservers\n            };\n            console.log(\"\\uD83D\\uDD27 Updating nameservers with data:\", updateData);\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].modifyNameservers(updateData);\n            if (response.data.success) {\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Nameservers updated successfully\");\n                // Call onUpdate callback if provided\n                if (onUpdate) {\n                    onUpdate({\n                        ...domain,\n                        nameservers: validNameservers\n                    });\n                }\n            } else {\n                throw new Error(response.data.error || \"Failed to update nameservers\");\n            }\n        } catch (error) {\n            var _error_response_data, _error_response;\n            console.error(\"Error updating nameservers:\", error);\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.error(((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.error) || error.message || \"Failed to update nameservers\");\n        } finally{\n            setIsUpdating(false);\n        }\n    };\n    const resetToDefault = async ()=>{\n        try {\n            console.log(\"\\uD83D\\uDD04 Loading customer default nameservers...\");\n            // Get customer default nameservers from API\n            const response = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_3__[\"default\"].getCustomerDefaultNameservers();\n            console.log(\"\\uD83D\\uDD0D Full API response:\", response.data);\n            if (response.data.success && response.data.nameservers) {\n                let defaultNameservers = response.data.nameservers;\n                // Handle different response formats\n                if (Array.isArray(defaultNameservers)) {\n                    console.log(\"✅ Loaded default nameservers from API:\", defaultNameservers);\n                } else {\n                    console.warn(\"⚠️ Nameservers not in expected array format:\", defaultNameservers);\n                    throw new Error(\"Invalid nameserver format from API\");\n                }\n                setNameservers(defaultNameservers);\n                setErrors({});\n                react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.success(\"Default nameservers loaded successfully\");\n            } else {\n                console.warn(\"⚠️ API response missing success or nameservers:\", response.data);\n                throw new Error(\"Failed to load default nameservers\");\n            }\n        } catch (error) {\n            console.error(\"❌ Error loading default nameservers:\", error);\n            // Fallback to correct order from dashboard\n            const fallbackNameservers = [\n                \"moha1280036.earth.orderbox-dns.com\",\n                \"moha1280036.mars.orderbox-dns.com\",\n                \"moha1280036.mercury.orderbox-dns.com\",\n                \"moha1280036.venus.orderbox-dns.com\"\n            ];\n            setNameservers(fallbackNameservers);\n            setErrors({});\n            react_toastify__WEBPACK_IMPORTED_MODULE_4__.toast.warning(\"Using fallback default nameservers\");\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.CardBody, {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2 mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                            className: \"h-5 w-5 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 284,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                            variant: \"h5\",\n                            className: \"text-gray-800\",\n                            children: \"Nameserver Management\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 285,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 283,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-gray-600 mb-6\",\n                    children: [\n                        \"Manage the nameservers for \",\n                        domain === null || domain === void 0 ? void 0 : domain.name,\n                        \". Changes may take up to 24-48 hours to propagate globally.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 290,\n                    columnNumber: 9\n                }, this),\n                (domain === null || domain === void 0 ? void 0 : domain.orderId) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                    className: \"text-xs text-gray-500 mb-4\",\n                    children: [\n                        \"Order ID: \",\n                        domain.orderId\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 296,\n                    columnNumber: 11\n                }, this),\n                errors.general && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"red\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 303,\n                            columnNumber: 13\n                        }, this),\n                        errors.general\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 302,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3 mb-6\",\n                    children: nameservers.map((ns, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Input, {\n                                            label: \"Nameserver \".concat(index + 1),\n                                            value: ns,\n                                            onChange: (e)=>handleNameserverChange(index, e.target.value),\n                                            error: !!errors[index],\n                                            className: errors[index] ? \"border-red-500\" : \"\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 312,\n                                            columnNumber: 17\n                                        }, this),\n                                        errors[index] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                            className: \"text-red-500 text-xs mt-1\",\n                                            children: errors[index]\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 322,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 311,\n                                    columnNumber: 15\n                                }, this),\n                                nameservers.length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"text\",\n                                    color: \"red\",\n                                    size: \"sm\",\n                                    onClick: ()=>removeNameserver(index),\n                                    className: \"p-2\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                        lineNumber: 336,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, index, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 310,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 308,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2 mb-6\",\n                    children: [\n                        nameservers.length < 13 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: addNameserver,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, this),\n                                \"Add Nameserver\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 345,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                            variant: \"outlined\",\n                            size: \"sm\",\n                            onClick: resetToDefault,\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 362,\n                                    columnNumber: 13\n                                }, this),\n                                \"Use Default\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 343,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Alert, {\n                    color: \"blue\",\n                    className: \"mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 368,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Typography, {\n                                    className: \"font-semibold\",\n                                    children: \"Important Notes:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"text-sm mt-1 space-y-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• At least 2 nameservers are required\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Maximum 13 nameservers allowed\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 373,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Changes may take 24-48 hours to propagate\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 374,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"• Incorrect nameservers may cause website downtime\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 371,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                            lineNumber: 369,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 367,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                        className: \"bg-blue-600 hover:bg-blue-700 flex items-center gap-2\",\n                        onClick: handleSave,\n                        disabled: isUpdating,\n                        children: isUpdating ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 17\n                                }, this),\n                                \"Updating...\"\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Plus_Save_Server_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 17\n                                }, this),\n                                \"Save Changes\"\n                            ]\n                        }, void 0, true)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                        lineNumber: 381,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n                    lineNumber: 380,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n            lineNumber: 282,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\components\\\\domains\\\\NameserverManager.jsx\",\n        lineNumber: 281,\n        columnNumber: 5\n    }, this);\n}\n_s(NameserverManager, \"0sR8sUYdtmYjlrPleVvRXk6Qprk=\");\n_c = NameserverManager;\nvar _c;\n$RefreshReg$(_c, \"NameserverManager\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/domains/NameserverManager.jsx\n"));

/***/ })

});