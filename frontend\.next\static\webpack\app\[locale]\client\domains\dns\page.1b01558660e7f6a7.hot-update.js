"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/[locale]/client/domains/dns/page",{

/***/ "(app-pages-browser)/./src/app/[locale]/client/domains/dns/page.jsx":
/*!******************************************************!*\
  !*** ./src/app/[locale]/client/domains/dns/page.jsx ***!
  \******************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ DnsMangementPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! next-intl */ \"(app-pages-browser)/./node_modules/next-intl/dist/development/index.react-client.js\");\n/* harmony import */ var next_intl__WEBPACK_IMPORTED_MODULE_7___default = /*#__PURE__*/__webpack_require__.n(next_intl__WEBPACK_IMPORTED_MODULE_7__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @material-tailwind/react */ \"(app-pages-browser)/./node_modules/@material-tailwind/react/index.js\");\n/* harmony import */ var _material_tailwind_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Server_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/server.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Server_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Server!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/app/services/domainMngService */ \"(app-pages-browser)/./src/app/services/domainMngService.js\");\n/* harmony import */ var _components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/domains/NameserverManager */ \"(app-pages-browser)/./src/components/domains/NameserverManager.jsx\");\n/* harmony import */ var _components_domains_ChildNameServerManager__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/domains/ChildNameServerManager */ \"(app-pages-browser)/./src/components/domains/ChildNameServerManager.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction DnsMangementPage() {\n    _s();\n    const t = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client\");\n    const dt = (0,next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations)(\"client.domainWrapper\");\n    const [domains, setDomains] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    // Function to update a specific domain in the list\n    const updateDomain = (updatedDomain)=>{\n        setDomains((prevDomains)=>prevDomains.map((domain)=>domain.id === updatedDomain.id ? {\n                    ...domain,\n                    ...updatedDomain\n                } : domain));\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const getUserDomains = async ()=>{\n            try {\n                setLoading(true);\n                // Get real domain data from the API\n                const res = await _app_services_domainMngService__WEBPACK_IMPORTED_MODULE_4__[\"default\"].getUserDomains();\n                console.log(\"Domain data for DNS management:\", res.data);\n                if (res.data && res.data.domains) {\n                    // Filter only active domains for DNS management\n                    const activeDomains = res.data.domains.filter((domain)=>domain.status === \"active\");\n                    console.log(\"\\uD83D\\uDD27 Active domains for DNS management:\", activeDomains);\n                    setDomains(activeDomains);\n                } else {\n                    setDomains([]);\n                }\n                setLoading(false);\n            } catch (error) {\n                console.error(\"Error getting domains for DNS management\", error);\n                setLoading(false);\n                // If there's an error, set domains to empty array\n                setDomains([]);\n            }\n        };\n        getUserDomains();\n    }, []);\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Server_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-6 w-6 text-blue-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                            lineNumber: 63,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                        variant: \"h6\",\n                        className: \"text-gray-600\",\n                        children: [\n                            t(\"loading\"),\n                            \"...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                lineNumber: 61,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gray-50 min-h-screen\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                    variant: \"text\",\n                    className: \"mb-6 text-blue-600 flex items-center gap-2\",\n                    onClick: ()=>router.push(\"/client/domains\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Server_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-4 w-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this),\n                        dt(\"back_to_domains\")\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            variant: \"h1\",\n                            className: \"text-3xl font-bold text-gray-800\",\n                            children: dt(\"dns_settings\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                            className: \"text-gray-600 mt-1\",\n                            children: t(\"manage_dns_records_description\", {\n                                defaultValue: \"Configure DNS records, nameservers, and more for your domains.\"\n                            })\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                            lineNumber: 89,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-6\",\n                    children: domains.map((domain)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            className: \"bg-white rounded-xl shadow-sm border border-gray-200\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.CardBody, {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-10 w-10 bg-blue-100 rounded-lg flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Server_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-600\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 107,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 106,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"ml-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                                className: \"font-medium text-gray-900\",\n                                                                children: domain.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 110,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize \".concat(domain.status === \"active\" ? \"bg-green-100 text-green-800\" : domain.status === \"pending\" ? \"bg-yellow-100 text-yellow-800\" : \"bg-gray-100 text-gray-800\"),\n                                                                children: dt(domain.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 113,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                        lineNumber: 109,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                lineNumber: 105,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                className: \"bg-blue-600 hover:bg-blue-700\",\n                                                onClick: ()=>router.push(\"/client/domains/\".concat(domain.id, \"/dns\")),\n                                                children: t(\"manage_records\", {\n                                                    defaultValue: \"Manage Records\"\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                lineNumber: 126,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_NameserverManager__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            domain: domain,\n                                            onUpdate: updateDomain\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"errrrrrrrrr\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_domains_ChildNameServerManager__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            domain: domain,\n                                            onUpdate: updateDomain\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"border-t pt-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                variant: \"h6\",\n                                                className: \"mb-4 text-gray-800\",\n                                                children: \"DNS Records\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 19\n                                            }, this),\n                                            domain.dnsRecords && domain.dnsRecords.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                className: \"bg-gray-50 border-b border-gray-200\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                        children: t(\"type\", {\n                                                                            defaultValue: \"Type\"\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 157,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                        children: t(\"name\", {\n                                                                            defaultValue: \"Name\"\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 160,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                        children: t(\"content\", {\n                                                                            defaultValue: \"Content\"\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 163,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                        className: \"px-4 py-3 text-left text-sm font-medium text-gray-500\",\n                                                                        children: t(\"ttl\", {\n                                                                            defaultValue: \"TTL\"\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                        lineNumber: 166,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                lineNumber: 156,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 155,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                            className: \"divide-y divide-gray-200\",\n                                                            children: domain.dnsRecords.map((record)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                                    className: \"hover:bg-gray-50\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-3 text-sm font-medium text-gray-900\",\n                                                                            children: record.type\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 174,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                            children: record.name\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 177,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                            children: record.content\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 180,\n                                                                            columnNumber: 31\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                            className: \"px-4 py-3 text-sm text-gray-500\",\n                                                                            children: record.ttl\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                            lineNumber: 183,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, record.id, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                                    lineNumber: 173,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                            lineNumber: 171,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 154,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                lineNumber: 153,\n                                                columnNumber: 21\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-6 bg-gray-50 rounded-lg\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_material_tailwind_react__WEBPACK_IMPORTED_MODULE_3__.Typography, {\n                                                    className: \"text-gray-500\",\n                                                    children: t(\"no_dns_records\", {\n                                                        defaultValue: \"No DNS records found\"\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                                lineNumber: 192,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                        lineNumber: 147,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this)\n                        }, domain.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n                    lineNumber: 97,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Work\\\\ztech_new_env\\\\ztech_dev\\\\frontend\\\\src\\\\app\\\\[locale]\\\\client\\\\domains\\\\dns\\\\page.jsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(DnsMangementPage, \"gMTDiDDVHZ+wuBdkndIxnYZuUOk=\", false, function() {\n    return [\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_intl__WEBPACK_IMPORTED_MODULE_7__.useTranslations,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DnsMangementPage;\nvar _c;\n$RefreshReg$(_c, \"DnsMangementPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/[locale]/client/domains/dns/page.jsx\n"));

/***/ })

});