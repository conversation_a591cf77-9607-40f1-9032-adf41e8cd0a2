{"c": ["app/[locale]/layout", "app/admin/layout", "webpack"], "r": ["app/[locale]/client/domains/[id]/page", "app/[locale]/domains/page", "app/[locale]/client/domains/[id]/renew/page", "app/[locale]/client/domains/dns/page", "app/[locale]/client/domains/locks/page", "app/[locale]/client/domains/privacy/page"], "m": ["(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye-off.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5C%5Bid%5D%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/page.jsx", "(app-pages-browser)/./src/components/domains/NameserverManager.jsx", "(app-pages-browser)/./src/components/domains/PrivacyProtectionManager.jsx", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cdomains%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/domains/page.jsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5C%5Bid%5D%5Crenew%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/[id]/renew/page.jsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5Cdns%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/dns/page.jsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5Clocks%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/locks/page.jsx", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=C%3A%5CUsers%5Cabder%5CDesktop%5CWork%5Cztech_new_env%5Cztech_dev%5Cfrontend%5Csrc%5Capp%5C%5Blocale%5D%5Cclient%5Cdomains%5Cprivacy%5Cpage.jsx&server=false!", "(app-pages-browser)/./src/app/[locale]/client/domains/privacy/page.jsx"]}